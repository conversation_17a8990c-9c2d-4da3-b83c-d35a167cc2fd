import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Grid,
  Switch,
  FormControlLabel,
  TextField,
  Divider,
  Alert,
  Tabs,
  Tab,
  Paper,
} from '@mui/material';
import {
  Save,
  Security,
  Notifications,
  Payment,
  Storage,
  Api,
  Email,
  Sms,
} from '@mui/icons-material';

export default function SystemSettings() {
  const [currentTab, setCurrentTab] = useState(0);
  const [settings, setSettings] = useState({
    // General Settings
    siteName: 'ProChat',
    siteDescription: 'Jukwaa la kisasa la mitandao ya kijamii na miamala ya kifedha',
    maintenanceMode: false,
    registrationEnabled: true,
    emailVerificationRequired: true,
    
    // Security Settings
    twoFactorRequired: false,
    passwordMinLength: 8,
    sessionTimeout: 60,
    maxLoginAttempts: 5,
    
    // Payment Settings
    proPayEnabled: true,
    transactionFee: 2.5,
    withdrawalFee: 1000,
    minimumWithdrawal: 10000,
    maximumWithdrawal: 5000000,
    
    // Notification Settings
    emailNotifications: true,
    smsNotifications: true,
    pushNotifications: true,
    
    // API Settings
    apiRateLimit: 1000,
    apiTimeout: 30,
    
    // Storage Settings
    maxFileSize: 10,
    allowedFileTypes: 'jpg,jpeg,png,gif,mp4,pdf',
  });

  const tabs = [
    { label: 'Jumla', icon: <Settings /> },
    { label: 'Usalama', icon: <Security /> },
    { label: 'Malipo', icon: <Payment /> },
    { label: 'Arifa', icon: <Notifications /> },
    { label: 'API', icon: <Api /> },
    { label: 'Hifadhi', icon: <Storage /> },
  ];

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSave = () => {
    console.log('Saving settings:', settings);
    // Implement save functionality
  };

  const renderGeneralSettings = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Jina la Tovuti"
          value={settings.siteName}
          onChange={(e) => handleSettingChange('siteName', e.target.value)}
        />
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          multiline
          rows={3}
          label="Maelezo ya Tovuti"
          value={settings.siteDescription}
          onChange={(e) => handleSettingChange('siteDescription', e.target.value)}
        />
      </Grid>
      <Grid item xs={12}>
        <FormControlLabel
          control={
            <Switch
              checked={settings.maintenanceMode}
              onChange={(e) => handleSettingChange('maintenanceMode', e.target.checked)}
            />
          }
          label="Hali ya Matengenezo"
        />
      </Grid>
      <Grid item xs={12}>
        <FormControlLabel
          control={
            <Switch
              checked={settings.registrationEnabled}
              onChange={(e) => handleSettingChange('registrationEnabled', e.target.checked)}
            />
          }
          label="Ruhusu Usajili Mpya"
        />
      </Grid>
      <Grid item xs={12}>
        <FormControlLabel
          control={
            <Switch
              checked={settings.emailVerificationRequired}
              onChange={(e) => handleSettingChange('emailVerificationRequired', e.target.checked)}
            />
          }
          label="Hitaji Uthibitisho wa Barua Pepe"
        />
      </Grid>
    </Grid>
  );

  const renderSecuritySettings = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <FormControlLabel
          control={
            <Switch
              checked={settings.twoFactorRequired}
              onChange={(e) => handleSettingChange('twoFactorRequired', e.target.checked)}
            />
          }
          label="Hitaji 2FA kwa Waongozi Wote"
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          type="number"
          label="Urefu wa Chini wa Nywila"
          value={settings.passwordMinLength}
          onChange={(e) => handleSettingChange('passwordMinLength', parseInt(e.target.value))}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          type="number"
          label="Muda wa Session (dakika)"
          value={settings.sessionTimeout}
          onChange={(e) => handleSettingChange('sessionTimeout', parseInt(e.target.value))}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          type="number"
          label="Majaribio ya Juu ya Kuingia"
          value={settings.maxLoginAttempts}
          onChange={(e) => handleSettingChange('maxLoginAttempts', parseInt(e.target.value))}
        />
      </Grid>
    </Grid>
  );

  const renderPaymentSettings = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <FormControlLabel
          control={
            <Switch
              checked={settings.proPayEnabled}
              onChange={(e) => handleSettingChange('proPayEnabled', e.target.checked)}
            />
          }
          label="Washa ProPay Wallet"
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          type="number"
          label="Ada ya Muamala (%)"
          value={settings.transactionFee}
          onChange={(e) => handleSettingChange('transactionFee', parseFloat(e.target.value))}
          inputProps={{ step: 0.1 }}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          type="number"
          label="Ada ya Kutoa (TSH)"
          value={settings.withdrawalFee}
          onChange={(e) => handleSettingChange('withdrawalFee', parseInt(e.target.value))}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          type="number"
          label="Kiwango cha Chini cha Kutoa (TSH)"
          value={settings.minimumWithdrawal}
          onChange={(e) => handleSettingChange('minimumWithdrawal', parseInt(e.target.value))}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          type="number"
          label="Kiwango cha Juu cha Kutoa (TSH)"
          value={settings.maximumWithdrawal}
          onChange={(e) => handleSettingChange('maximumWithdrawal', parseInt(e.target.value))}
        />
      </Grid>
    </Grid>
  );

  const renderNotificationSettings = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <FormControlLabel
          control={
            <Switch
              checked={settings.emailNotifications}
              onChange={(e) => handleSettingChange('emailNotifications', e.target.checked)}
            />
          }
          label="Arifa za Barua Pepe"
        />
      </Grid>
      <Grid item xs={12}>
        <FormControlLabel
          control={
            <Switch
              checked={settings.smsNotifications}
              onChange={(e) => handleSettingChange('smsNotifications', e.target.checked)}
            />
          }
          label="Arifa za SMS"
        />
      </Grid>
      <Grid item xs={12}>
        <FormControlLabel
          control={
            <Switch
              checked={settings.pushNotifications}
              onChange={(e) => handleSettingChange('pushNotifications', e.target.checked)}
            />
          }
          label="Arifa za Push"
        />
      </Grid>
    </Grid>
  );

  const renderApiSettings = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          type="number"
          label="Kikomo cha API (maombi kwa dakika)"
          value={settings.apiRateLimit}
          onChange={(e) => handleSettingChange('apiRateLimit', parseInt(e.target.value))}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          type="number"
          label="Muda wa Kusubiri API (sekunde)"
          value={settings.apiTimeout}
          onChange={(e) => handleSettingChange('apiTimeout', parseInt(e.target.value))}
        />
      </Grid>
    </Grid>
  );

  const renderStorageSettings = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          type="number"
          label="Ukubwa wa Juu wa Faili (MB)"
          value={settings.maxFileSize}
          onChange={(e) => handleSettingChange('maxFileSize', parseInt(e.target.value))}
        />
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Aina za Faili Zinazoruhusiwa"
          value={settings.allowedFileTypes}
          onChange={(e) => handleSettingChange('allowedFileTypes', e.target.value)}
          helperText="Tenganisha kwa koma (mfano: jpg,png,pdf)"
        />
      </Grid>
    </Grid>
  );

  const renderTabContent = () => {
    switch (currentTab) {
      case 0: return renderGeneralSettings();
      case 1: return renderSecuritySettings();
      case 2: return renderPaymentSettings();
      case 3: return renderNotificationSettings();
      case 4: return renderApiSettings();
      case 5: return renderStorageSettings();
      default: return renderGeneralSettings();
    }
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Mipangilio ya Mfumo
        </Typography>
        <Button variant="contained" startIcon={<Save />} onClick={handleSave}>
          Hifadhi Mabadiliko
        </Button>
      </Box>

      {/* Warning Alert */}
      <Alert severity="warning" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>Onyo:</strong> Mabadiliko ya mipangilio ya mfumo yanaweza kuathiri utendaji wa platform nzima. 
          Hakikisha umesoma na kuelewa athari za kila mpangilio kabla ya kuhifadhi.
        </Typography>
      </Alert>

      {/* Settings Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={currentTab}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
        >
          {tabs.map((tab, index) => (
            <Tab
              key={index}
              label={tab.label}
              icon={tab.icon}
              iconPosition="start"
            />
          ))}
        </Tabs>
      </Paper>

      {/* Settings Content */}
      <Card>
        <CardContent sx={{ p: 4 }}>
          {renderTabContent()}
          
          <Divider sx={{ my: 3 }} />
          
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <Button variant="outlined">
              Rejesha Chaguo za Msingi
            </Button>
            <Button variant="contained" startIcon={<Save />} onClick={handleSave}>
              Hifadhi Mipangilio
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
}
