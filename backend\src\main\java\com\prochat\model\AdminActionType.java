package com.prochat.model;

public enum AdminActionType {
    // Authentication
    LOGIN,
    LOG<PERSON>UT,
    LOGIN_FAILED,
    PASSWORD_CHANGED,
    TWO_FACTOR_ENABLED,
    TWO_FACTOR_DISABLED,
    
    // User Management
    USER_CREATED,
    USER_UPDATED,
    USER_DELETED,
    USER_BLOCKED,
    USER_UNBLOCKED,
    USER_VERIFIED,
    USER_UNVERIFIED,
    USER_ROLE_ASSIGNED,
    
    // Content Moderation
    POST_APPROVED,
    POST_REJECTED,
    POST_DELETED,
    POST_FLAGGED,
    COMMENT_DELETED,
    VIDEO_APPROVED,
    VIDEO_REJECTED,
    NEWS_APPROVED,
    NEWS_REJECTED,
    
    // Financial Operations
    TRANSACTION_APPROVED,
    TRANSACTION_REJECTED,
    WITHDRAWAL_APPROVED,
    WIT<PERSON><PERSON><PERSON>L_REJECTED,
    DEPOSIT_APPROVED,
    COMMISSION_UPDATED,
    FEE_CONFIGURED,
    DIS<PERSON><PERSON>E_RESOLVED,
    
    // Event Management
    EVENT_APPROVED,
    EVENT_REJECTED,
    TICKET_VALIDATED,
    EVENT_CANCELLED,
    
    // Advertisement
    AD_APPROVED,
    AD_REJECTED,
    CAMPAIGN_CREATED,
    AD_RATE_UPDATED,
    
    // Job & Tender
    JOB_APPROVED,
    JOB_REJECTED,
    APPLICATION_REVIEWED,
    INTERVIEW_SCHEDULED,
    CANDIDATE_RANKED,
    
    // System Administration
    ADMIN_CREATED,
    ADMIN_UPDATED,
    ADMIN_DELETED,
    PERMISSION_GRANTED,
    PERMISSION_REVOKED,
    SYSTEM_CONFIGURED,
    BACKUP_CREATED,
    BACKUP_RESTORED,
    API_CONFIGURED,
    NOTIFICATION_SENT,
    
    // Support
    TICKET_RESPONDED,
    TICKET_ESCALATED,
    TICKET_CLOSED,
    REPORT_REVIEWED,
    USER_BANNED,
    
    // Security
    IP_RESTRICTED,
    SECURITY_ALERT,
    AUDIT_LOG_VIEWED,
    EMERGENCY_ACCESS_USED,
    
    // Data Operations
    DATA_EXPORTED,
    REPORT_GENERATED,
    ANALYTICS_VIEWED,
    
    // System Events
    SYSTEM_MAINTENANCE,
    DATABASE_UPDATED,
    CACHE_CLEARED,
    SERVICE_RESTARTED
}
