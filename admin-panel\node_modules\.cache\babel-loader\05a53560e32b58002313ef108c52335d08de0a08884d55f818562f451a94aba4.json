{"ast": null, "code": "var _excluded = [\"children\"],\n  _excluded2 = [\"children\"];\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  for (var key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key)) {\n      if (excluded.indexOf(key) >= 0) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nimport get from 'lodash/get';\nimport isNil from 'lodash/isNil';\nimport isString from 'lodash/isString';\nimport isFunction from 'lodash/isFunction';\nimport isObject from 'lodash/isObject';\nimport { Children, isValidElement } from 'react';\nimport { isFragment } from 'react-is';\nimport { isNumber } from './DataUtils';\nimport { shallowEqual } from './ShallowEqual';\nimport { FilteredElementKeyMap, SVGElementPropKeys, EventKeys } from './types';\nvar REACT_BROWSER_EVENT_MAP = {\n  click: 'onClick',\n  mousedown: 'onMouseDown',\n  mouseup: 'onMouseUp',\n  mouseover: 'onMouseOver',\n  mousemove: 'onMouseMove',\n  mouseout: 'onMouseOut',\n  mouseenter: 'onMouseEnter',\n  mouseleave: 'onMouseLeave',\n  touchcancel: 'onTouchCancel',\n  touchend: 'onTouchEnd',\n  touchmove: 'onTouchMove',\n  touchstart: 'onTouchStart',\n  contextmenu: 'onContextMenu',\n  dblclick: 'onDoubleClick'\n};\nexport var SCALE_TYPES = ['auto', 'linear', 'pow', 'sqrt', 'log', 'identity', 'time', 'band', 'point', 'ordinal', 'quantile', 'quantize', 'utc', 'sequential', 'threshold'];\nexport var LEGEND_TYPES = ['plainline', 'line', 'square', 'rect', 'circle', 'cross', 'diamond', 'star', 'triangle', 'wye', 'none'];\nexport var TOOLTIP_TYPES = ['none'];\n\n/**\n * Get the display name of a component\n * @param  {Object} Comp Specified Component\n * @return {String}      Display name of Component\n */\nexport var getDisplayName = function getDisplayName(Comp) {\n  if (typeof Comp === 'string') {\n    return Comp;\n  }\n  if (!Comp) {\n    return '';\n  }\n  return Comp.displayName || Comp.name || 'Component';\n};\n\n// `toArray` gets called multiple times during the render\n// so we can memoize last invocation (since reference to `children` is the same)\nvar lastChildren = null;\nvar lastResult = null;\nexport var toArray = function toArray(children) {\n  if (children === lastChildren && Array.isArray(lastResult)) {\n    return lastResult;\n  }\n  var result = [];\n  Children.forEach(children, function (child) {\n    if (isNil(child)) return;\n    if (isFragment(child)) {\n      result = result.concat(toArray(child.props.children));\n    } else {\n      // @ts-expect-error this could still be Iterable<ReactNode> and TS does not like that\n      result.push(child);\n    }\n  });\n  lastResult = result;\n  lastChildren = children;\n  return result;\n};\n\n/*\n * Find and return all matched children by type.\n * `type` must be a React.ComponentType\n */\nexport function findAllByType(children, type) {\n  var result = [];\n  var types = [];\n  if (Array.isArray(type)) {\n    types = type.map(function (t) {\n      return getDisplayName(t);\n    });\n  } else {\n    types = [getDisplayName(type)];\n  }\n  toArray(children).forEach(function (child) {\n    var childType = get(child, 'type.displayName') || get(child, 'type.name');\n    if (types.indexOf(childType) !== -1) {\n      result.push(child);\n    }\n  });\n  return result;\n}\n\n/*\n * Return the first matched child by type, return null otherwise.\n * `type` must be a React.ComponentType\n */\nexport function findChildByType(children, type) {\n  var result = findAllByType(children, type);\n  return result && result[0];\n}\n\n/*\n * Create a new array of children excluding the ones matched the type\n */\nexport var withoutType = function withoutType(children, type) {\n  var newChildren = [];\n  var types;\n  if (Array.isArray(type)) {\n    types = type.map(function (t) {\n      return getDisplayName(t);\n    });\n  } else {\n    types = [getDisplayName(type)];\n  }\n  toArray(children).forEach(function (child) {\n    var displayName = get(child, 'type.displayName');\n    if (displayName && types.indexOf(displayName) !== -1) {\n      return;\n    }\n    newChildren.push(child);\n  });\n  return newChildren;\n};\n\n/**\n * validate the width and height props of a chart element\n * @param  {Object} el A chart element\n * @return {Boolean}   true If the props width and height are number, and greater than 0\n */\nexport var validateWidthHeight = function validateWidthHeight(el) {\n  if (!el || !el.props) {\n    return false;\n  }\n  var _el$props = el.props,\n    width = _el$props.width,\n    height = _el$props.height;\n  if (!isNumber(width) || width <= 0 || !isNumber(height) || height <= 0) {\n    return false;\n  }\n  return true;\n};\nvar SVG_TAGS = ['a', 'altGlyph', 'altGlyphDef', 'altGlyphItem', 'animate', 'animateColor', 'animateMotion', 'animateTransform', 'circle', 'clipPath', 'color-profile', 'cursor', 'defs', 'desc', 'ellipse', 'feBlend', 'feColormatrix', 'feComponentTransfer', 'feComposite', 'feConvolveMatrix', 'feDiffuseLighting', 'feDisplacementMap', 'feDistantLight', 'feFlood', 'feFuncA', 'feFuncB', 'feFuncG', 'feFuncR', 'feGaussianBlur', 'feImage', 'feMerge', 'feMergeNode', 'feMorphology', 'feOffset', 'fePointLight', 'feSpecularLighting', 'feSpotLight', 'feTile', 'feTurbulence', 'filter', 'font', 'font-face', 'font-face-format', 'font-face-name', 'font-face-url', 'foreignObject', 'g', 'glyph', 'glyphRef', 'hkern', 'image', 'line', 'lineGradient', 'marker', 'mask', 'metadata', 'missing-glyph', 'mpath', 'path', 'pattern', 'polygon', 'polyline', 'radialGradient', 'rect', 'script', 'set', 'stop', 'style', 'svg', 'switch', 'symbol', 'text', 'textPath', 'title', 'tref', 'tspan', 'use', 'view', 'vkern'];\nvar isSvgElement = function isSvgElement(child) {\n  return child && child.type && isString(child.type) && SVG_TAGS.indexOf(child.type) >= 0;\n};\nexport var hasClipDot = function hasClipDot(dot) {\n  return dot && _typeof(dot) === 'object' && 'clipDot' in dot;\n};\n\n/**\n * Checks if the property is valid to spread onto an SVG element or onto a specific component\n * @param {unknown} property property value currently being compared\n * @param {string} key property key currently being compared\n * @param {boolean} includeEvents if events are included in spreadable props\n * @param {boolean} svgElementType checks against map of SVG element types to attributes\n * @returns {boolean} is prop valid\n */\nexport var isValidSpreadableProp = function isValidSpreadableProp(property, key, includeEvents, svgElementType) {\n  var _FilteredElementKeyMa;\n  /**\n   * If the svg element type is explicitly included, check against the filtered element key map\n   * to determine if there are attributes that should only exist on that element type.\n   * @todo Add an internal cjs version of https://github.com/wooorm/svg-element-attributes for full coverage.\n   */\n  var matchingElementTypeKeys = (_FilteredElementKeyMa = FilteredElementKeyMap === null || FilteredElementKeyMap === void 0 ? void 0 : FilteredElementKeyMap[svgElementType]) !== null && _FilteredElementKeyMa !== void 0 ? _FilteredElementKeyMa : [];\n  return key.startsWith('data-') || !isFunction(property) && (svgElementType && matchingElementTypeKeys.includes(key) || SVGElementPropKeys.includes(key)) || includeEvents && EventKeys.includes(key);\n};\n\n/**\n * Filter all the svg elements of children\n * @param  {Array} children The children of a react element\n * @return {Array}          All the svg elements\n */\nexport var filterSvgElements = function filterSvgElements(children) {\n  var svgElements = [];\n  toArray(children).forEach(function (entry) {\n    if (isSvgElement(entry)) {\n      svgElements.push(entry);\n    }\n  });\n  return svgElements;\n};\nexport var filterProps = function filterProps(props, includeEvents, svgElementType) {\n  if (!props || typeof props === 'function' || typeof props === 'boolean') {\n    return null;\n  }\n  var inputProps = props;\n  if (/*#__PURE__*/isValidElement(props)) {\n    inputProps = props.props;\n  }\n  if (!isObject(inputProps)) {\n    return null;\n  }\n  var out = {};\n\n  /**\n   * Props are blindly spread onto SVG elements. This loop filters out properties that we don't want to spread.\n   * Items filtered out are as follows:\n   *   - functions in properties that are SVG attributes (functions are included when includeEvents is true)\n   *   - props that are SVG attributes but don't matched the passed svgElementType\n   *   - any prop that is not in SVGElementPropKeys (or in EventKeys if includeEvents is true)\n   */\n  Object.keys(inputProps).forEach(function (key) {\n    var _inputProps;\n    if (isValidSpreadableProp((_inputProps = inputProps) === null || _inputProps === void 0 ? void 0 : _inputProps[key], key, includeEvents, svgElementType)) {\n      out[key] = inputProps[key];\n    }\n  });\n  return out;\n};\n\n/**\n * Wether props of children changed\n * @param  {Object} nextChildren The latest children\n * @param  {Object} prevChildren The prev children\n * @return {Boolean}             equal or not\n */\nexport var isChildrenEqual = function isChildrenEqual(nextChildren, prevChildren) {\n  if (nextChildren === prevChildren) {\n    return true;\n  }\n  var count = Children.count(nextChildren);\n  if (count !== Children.count(prevChildren)) {\n    return false;\n  }\n  if (count === 0) {\n    return true;\n  }\n  if (count === 1) {\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    return isSingleChildEqual(Array.isArray(nextChildren) ? nextChildren[0] : nextChildren, Array.isArray(prevChildren) ? prevChildren[0] : prevChildren);\n  }\n  for (var i = 0; i < count; i++) {\n    var nextChild = nextChildren[i];\n    var prevChild = prevChildren[i];\n    if (Array.isArray(nextChild) || Array.isArray(prevChild)) {\n      if (!isChildrenEqual(nextChild, prevChild)) {\n        return false;\n      }\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    } else if (!isSingleChildEqual(nextChild, prevChild)) {\n      return false;\n    }\n  }\n  return true;\n};\nexport var isSingleChildEqual = function isSingleChildEqual(nextChild, prevChild) {\n  if (isNil(nextChild) && isNil(prevChild)) {\n    return true;\n  }\n  if (!isNil(nextChild) && !isNil(prevChild)) {\n    var _ref = nextChild.props || {},\n      nextChildren = _ref.children,\n      nextProps = _objectWithoutProperties(_ref, _excluded);\n    var _ref2 = prevChild.props || {},\n      prevChildren = _ref2.children,\n      prevProps = _objectWithoutProperties(_ref2, _excluded2);\n    if (nextChildren && prevChildren) {\n      return shallowEqual(nextProps, prevProps) && isChildrenEqual(nextChildren, prevChildren);\n    }\n    if (!nextChildren && !prevChildren) {\n      return shallowEqual(nextProps, prevProps);\n    }\n    return false;\n  }\n  return false;\n};\nexport var renderByOrder = function renderByOrder(children, renderMap) {\n  var elements = [];\n  var record = {};\n  toArray(children).forEach(function (child, index) {\n    if (isSvgElement(child)) {\n      elements.push(child);\n    } else if (child) {\n      var displayName = getDisplayName(child.type);\n      var _ref3 = renderMap[displayName] || {},\n        handler = _ref3.handler,\n        once = _ref3.once;\n      if (handler && (!once || !record[displayName])) {\n        var results = handler(child, displayName, index);\n        elements.push(results);\n        record[displayName] = true;\n      }\n    }\n  });\n  return elements;\n};\nexport var getReactEventByType = function getReactEventByType(e) {\n  var type = e && e.type;\n  if (type && REACT_BROWSER_EVENT_MAP[type]) {\n    return REACT_BROWSER_EVENT_MAP[type];\n  }\n  return null;\n};\nexport var parseChildIndex = function parseChildIndex(child, children) {\n  return toArray(children).indexOf(child);\n};", "map": {"version": 3, "names": ["_excluded", "_excluded2", "_objectWithoutProperties", "source", "excluded", "target", "_objectWithoutPropertiesLoose", "key", "i", "Object", "getOwnPropertySymbols", "sourceSymbolKeys", "length", "indexOf", "prototype", "propertyIsEnumerable", "call", "hasOwnProperty", "_typeof", "o", "Symbol", "iterator", "constructor", "get", "isNil", "isString", "isFunction", "isObject", "Children", "isValidElement", "isFragment", "isNumber", "shallowEqual", "FilteredElementKeyMap", "SVGElementPropKeys", "EventKeys", "REACT_BROWSER_EVENT_MAP", "click", "mousedown", "mouseup", "mouseover", "mousemove", "mouseout", "mouseenter", "mouseleave", "touchcancel", "touchend", "touchmove", "touchstart", "contextmenu", "dblclick", "SCALE_TYPES", "LEGEND_TYPES", "TOOLTIP_TYPES", "getDisplayName", "Comp", "displayName", "name", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastResult", "toArray", "children", "Array", "isArray", "result", "for<PERSON>ach", "child", "concat", "props", "push", "findAllByType", "type", "types", "map", "t", "childType", "findChildByType", "withoutType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "validateWidthHeight", "el", "_el$props", "width", "height", "SVG_TAGS", "isSvgElement", "hasClipDot", "dot", "isValidSpreadableProp", "property", "includeEvents", "svgElementType", "_FilteredElementKeyMa", "matchingElementTypeKeys", "startsWith", "includes", "filterSvgElements", "svgElements", "entry", "filterProps", "inputProps", "out", "keys", "_inputProps", "isChildrenEqual", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prev<PERSON><PERSON><PERSON><PERSON>", "count", "isSingleChildEqual", "<PERSON><PERSON><PERSON><PERSON>", "prev<PERSON><PERSON><PERSON>", "_ref", "nextProps", "_ref2", "prevProps", "renderByOrder", "renderMap", "elements", "record", "index", "_ref3", "handler", "once", "results", "getReactEventByType", "e", "parseChildIndex"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/recharts/es6/util/ReactUtils.js"], "sourcesContent": ["var _excluded = [\"children\"],\n  _excluded2 = [\"children\"];\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } } return target; }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nimport get from 'lodash/get';\nimport isNil from 'lodash/isNil';\nimport isString from 'lodash/isString';\nimport isFunction from 'lodash/isFunction';\nimport isObject from 'lodash/isObject';\nimport { Children, isValidElement } from 'react';\nimport { isFragment } from 'react-is';\nimport { isNumber } from './DataUtils';\nimport { shallowEqual } from './ShallowEqual';\nimport { FilteredElementKeyMap, SVGElementPropKeys, EventKeys } from './types';\nvar REACT_BROWSER_EVENT_MAP = {\n  click: 'onClick',\n  mousedown: 'onMouseDown',\n  mouseup: 'onMouseUp',\n  mouseover: 'onMouseOver',\n  mousemove: 'onMouseMove',\n  mouseout: 'onMouseOut',\n  mouseenter: 'onMouseEnter',\n  mouseleave: 'onMouseLeave',\n  touchcancel: 'onTouchCancel',\n  touchend: 'onTouchEnd',\n  touchmove: 'onTouchMove',\n  touchstart: 'onTouchStart',\n  contextmenu: 'onContextMenu',\n  dblclick: 'onDoubleClick'\n};\nexport var SCALE_TYPES = ['auto', 'linear', 'pow', 'sqrt', 'log', 'identity', 'time', 'band', 'point', 'ordinal', 'quantile', 'quantize', 'utc', 'sequential', 'threshold'];\nexport var LEGEND_TYPES = ['plainline', 'line', 'square', 'rect', 'circle', 'cross', 'diamond', 'star', 'triangle', 'wye', 'none'];\nexport var TOOLTIP_TYPES = ['none'];\n\n/**\n * Get the display name of a component\n * @param  {Object} Comp Specified Component\n * @return {String}      Display name of Component\n */\nexport var getDisplayName = function getDisplayName(Comp) {\n  if (typeof Comp === 'string') {\n    return Comp;\n  }\n  if (!Comp) {\n    return '';\n  }\n  return Comp.displayName || Comp.name || 'Component';\n};\n\n// `toArray` gets called multiple times during the render\n// so we can memoize last invocation (since reference to `children` is the same)\nvar lastChildren = null;\nvar lastResult = null;\nexport var toArray = function toArray(children) {\n  if (children === lastChildren && Array.isArray(lastResult)) {\n    return lastResult;\n  }\n  var result = [];\n  Children.forEach(children, function (child) {\n    if (isNil(child)) return;\n    if (isFragment(child)) {\n      result = result.concat(toArray(child.props.children));\n    } else {\n      // @ts-expect-error this could still be Iterable<ReactNode> and TS does not like that\n      result.push(child);\n    }\n  });\n  lastResult = result;\n  lastChildren = children;\n  return result;\n};\n\n/*\n * Find and return all matched children by type.\n * `type` must be a React.ComponentType\n */\nexport function findAllByType(children, type) {\n  var result = [];\n  var types = [];\n  if (Array.isArray(type)) {\n    types = type.map(function (t) {\n      return getDisplayName(t);\n    });\n  } else {\n    types = [getDisplayName(type)];\n  }\n  toArray(children).forEach(function (child) {\n    var childType = get(child, 'type.displayName') || get(child, 'type.name');\n    if (types.indexOf(childType) !== -1) {\n      result.push(child);\n    }\n  });\n  return result;\n}\n\n/*\n * Return the first matched child by type, return null otherwise.\n * `type` must be a React.ComponentType\n */\nexport function findChildByType(children, type) {\n  var result = findAllByType(children, type);\n  return result && result[0];\n}\n\n/*\n * Create a new array of children excluding the ones matched the type\n */\nexport var withoutType = function withoutType(children, type) {\n  var newChildren = [];\n  var types;\n  if (Array.isArray(type)) {\n    types = type.map(function (t) {\n      return getDisplayName(t);\n    });\n  } else {\n    types = [getDisplayName(type)];\n  }\n  toArray(children).forEach(function (child) {\n    var displayName = get(child, 'type.displayName');\n    if (displayName && types.indexOf(displayName) !== -1) {\n      return;\n    }\n    newChildren.push(child);\n  });\n  return newChildren;\n};\n\n/**\n * validate the width and height props of a chart element\n * @param  {Object} el A chart element\n * @return {Boolean}   true If the props width and height are number, and greater than 0\n */\nexport var validateWidthHeight = function validateWidthHeight(el) {\n  if (!el || !el.props) {\n    return false;\n  }\n  var _el$props = el.props,\n    width = _el$props.width,\n    height = _el$props.height;\n  if (!isNumber(width) || width <= 0 || !isNumber(height) || height <= 0) {\n    return false;\n  }\n  return true;\n};\nvar SVG_TAGS = ['a', 'altGlyph', 'altGlyphDef', 'altGlyphItem', 'animate', 'animateColor', 'animateMotion', 'animateTransform', 'circle', 'clipPath', 'color-profile', 'cursor', 'defs', 'desc', 'ellipse', 'feBlend', 'feColormatrix', 'feComponentTransfer', 'feComposite', 'feConvolveMatrix', 'feDiffuseLighting', 'feDisplacementMap', 'feDistantLight', 'feFlood', 'feFuncA', 'feFuncB', 'feFuncG', 'feFuncR', 'feGaussianBlur', 'feImage', 'feMerge', 'feMergeNode', 'feMorphology', 'feOffset', 'fePointLight', 'feSpecularLighting', 'feSpotLight', 'feTile', 'feTurbulence', 'filter', 'font', 'font-face', 'font-face-format', 'font-face-name', 'font-face-url', 'foreignObject', 'g', 'glyph', 'glyphRef', 'hkern', 'image', 'line', 'lineGradient', 'marker', 'mask', 'metadata', 'missing-glyph', 'mpath', 'path', 'pattern', 'polygon', 'polyline', 'radialGradient', 'rect', 'script', 'set', 'stop', 'style', 'svg', 'switch', 'symbol', 'text', 'textPath', 'title', 'tref', 'tspan', 'use', 'view', 'vkern'];\nvar isSvgElement = function isSvgElement(child) {\n  return child && child.type && isString(child.type) && SVG_TAGS.indexOf(child.type) >= 0;\n};\nexport var hasClipDot = function hasClipDot(dot) {\n  return dot && _typeof(dot) === 'object' && 'clipDot' in dot;\n};\n\n/**\n * Checks if the property is valid to spread onto an SVG element or onto a specific component\n * @param {unknown} property property value currently being compared\n * @param {string} key property key currently being compared\n * @param {boolean} includeEvents if events are included in spreadable props\n * @param {boolean} svgElementType checks against map of SVG element types to attributes\n * @returns {boolean} is prop valid\n */\nexport var isValidSpreadableProp = function isValidSpreadableProp(property, key, includeEvents, svgElementType) {\n  var _FilteredElementKeyMa;\n  /**\n   * If the svg element type is explicitly included, check against the filtered element key map\n   * to determine if there are attributes that should only exist on that element type.\n   * @todo Add an internal cjs version of https://github.com/wooorm/svg-element-attributes for full coverage.\n   */\n  var matchingElementTypeKeys = (_FilteredElementKeyMa = FilteredElementKeyMap === null || FilteredElementKeyMap === void 0 ? void 0 : FilteredElementKeyMap[svgElementType]) !== null && _FilteredElementKeyMa !== void 0 ? _FilteredElementKeyMa : [];\n  return key.startsWith('data-') || !isFunction(property) && (svgElementType && matchingElementTypeKeys.includes(key) || SVGElementPropKeys.includes(key)) || includeEvents && EventKeys.includes(key);\n};\n\n/**\n * Filter all the svg elements of children\n * @param  {Array} children The children of a react element\n * @return {Array}          All the svg elements\n */\nexport var filterSvgElements = function filterSvgElements(children) {\n  var svgElements = [];\n  toArray(children).forEach(function (entry) {\n    if (isSvgElement(entry)) {\n      svgElements.push(entry);\n    }\n  });\n  return svgElements;\n};\nexport var filterProps = function filterProps(props, includeEvents, svgElementType) {\n  if (!props || typeof props === 'function' || typeof props === 'boolean') {\n    return null;\n  }\n  var inputProps = props;\n  if ( /*#__PURE__*/isValidElement(props)) {\n    inputProps = props.props;\n  }\n  if (!isObject(inputProps)) {\n    return null;\n  }\n  var out = {};\n\n  /**\n   * Props are blindly spread onto SVG elements. This loop filters out properties that we don't want to spread.\n   * Items filtered out are as follows:\n   *   - functions in properties that are SVG attributes (functions are included when includeEvents is true)\n   *   - props that are SVG attributes but don't matched the passed svgElementType\n   *   - any prop that is not in SVGElementPropKeys (or in EventKeys if includeEvents is true)\n   */\n  Object.keys(inputProps).forEach(function (key) {\n    var _inputProps;\n    if (isValidSpreadableProp((_inputProps = inputProps) === null || _inputProps === void 0 ? void 0 : _inputProps[key], key, includeEvents, svgElementType)) {\n      out[key] = inputProps[key];\n    }\n  });\n  return out;\n};\n\n/**\n * Wether props of children changed\n * @param  {Object} nextChildren The latest children\n * @param  {Object} prevChildren The prev children\n * @return {Boolean}             equal or not\n */\nexport var isChildrenEqual = function isChildrenEqual(nextChildren, prevChildren) {\n  if (nextChildren === prevChildren) {\n    return true;\n  }\n  var count = Children.count(nextChildren);\n  if (count !== Children.count(prevChildren)) {\n    return false;\n  }\n  if (count === 0) {\n    return true;\n  }\n  if (count === 1) {\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    return isSingleChildEqual(Array.isArray(nextChildren) ? nextChildren[0] : nextChildren, Array.isArray(prevChildren) ? prevChildren[0] : prevChildren);\n  }\n  for (var i = 0; i < count; i++) {\n    var nextChild = nextChildren[i];\n    var prevChild = prevChildren[i];\n    if (Array.isArray(nextChild) || Array.isArray(prevChild)) {\n      if (!isChildrenEqual(nextChild, prevChild)) {\n        return false;\n      }\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    } else if (!isSingleChildEqual(nextChild, prevChild)) {\n      return false;\n    }\n  }\n  return true;\n};\nexport var isSingleChildEqual = function isSingleChildEqual(nextChild, prevChild) {\n  if (isNil(nextChild) && isNil(prevChild)) {\n    return true;\n  }\n  if (!isNil(nextChild) && !isNil(prevChild)) {\n    var _ref = nextChild.props || {},\n      nextChildren = _ref.children,\n      nextProps = _objectWithoutProperties(_ref, _excluded);\n    var _ref2 = prevChild.props || {},\n      prevChildren = _ref2.children,\n      prevProps = _objectWithoutProperties(_ref2, _excluded2);\n    if (nextChildren && prevChildren) {\n      return shallowEqual(nextProps, prevProps) && isChildrenEqual(nextChildren, prevChildren);\n    }\n    if (!nextChildren && !prevChildren) {\n      return shallowEqual(nextProps, prevProps);\n    }\n    return false;\n  }\n  return false;\n};\nexport var renderByOrder = function renderByOrder(children, renderMap) {\n  var elements = [];\n  var record = {};\n  toArray(children).forEach(function (child, index) {\n    if (isSvgElement(child)) {\n      elements.push(child);\n    } else if (child) {\n      var displayName = getDisplayName(child.type);\n      var _ref3 = renderMap[displayName] || {},\n        handler = _ref3.handler,\n        once = _ref3.once;\n      if (handler && (!once || !record[displayName])) {\n        var results = handler(child, displayName, index);\n        elements.push(results);\n        record[displayName] = true;\n      }\n    }\n  });\n  return elements;\n};\nexport var getReactEventByType = function getReactEventByType(e) {\n  var type = e && e.type;\n  if (type && REACT_BROWSER_EVENT_MAP[type]) {\n    return REACT_BROWSER_EVENT_MAP[type];\n  }\n  return null;\n};\nexport var parseChildIndex = function parseChildIndex(child, children) {\n  return toArray(children).indexOf(child);\n};"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,UAAU,CAAC;EAC1BC,UAAU,GAAG,CAAC,UAAU,CAAC;AAC3B,SAASC,wBAAwBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAGC,6BAA6B,CAACH,MAAM,EAAEC,QAAQ,CAAC;EAAE,IAAIG,GAAG,EAAEC,CAAC;EAAE,IAAIC,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,gBAAgB,GAAGF,MAAM,CAACC,qBAAqB,CAACP,MAAM,CAAC;IAAE,KAAKK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,gBAAgB,CAACC,MAAM,EAAEJ,CAAC,EAAE,EAAE;MAAED,GAAG,GAAGI,gBAAgB,CAACH,CAAC,CAAC;MAAE,IAAIJ,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACE,MAAM,CAACK,SAAS,CAACC,oBAAoB,CAACC,IAAI,CAACb,MAAM,EAAEI,GAAG,CAAC,EAAE;MAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOF,MAAM;AAAE;AAC3e,SAASC,6BAA6BA,CAACH,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIE,GAAG,IAAIJ,MAAM,EAAE;IAAE,IAAIM,MAAM,CAACK,SAAS,CAACG,cAAc,CAACD,IAAI,CAACb,MAAM,EAAEI,GAAG,CAAC,EAAE;MAAE,IAAIH,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,IAAI,CAAC,EAAE;MAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOF,MAAM;AAAE;AACtR,SAASa,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACN,SAAS,GAAG,QAAQ,GAAG,OAAOK,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,OAAOI,GAAG,MAAM,YAAY;AAC5B,OAAOC,KAAK,MAAM,cAAc;AAChC,OAAOC,QAAQ,MAAM,iBAAiB;AACtC,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,OAAOC,QAAQ,MAAM,iBAAiB;AACtC,SAASC,QAAQ,EAAEC,cAAc,QAAQ,OAAO;AAChD,SAASC,UAAU,QAAQ,UAAU;AACrC,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,qBAAqB,EAAEC,kBAAkB,EAAEC,SAAS,QAAQ,SAAS;AAC9E,IAAIC,uBAAuB,GAAG;EAC5BC,KAAK,EAAE,SAAS;EAChBC,SAAS,EAAE,aAAa;EACxBC,OAAO,EAAE,WAAW;EACpBC,SAAS,EAAE,aAAa;EACxBC,SAAS,EAAE,aAAa;EACxBC,QAAQ,EAAE,YAAY;EACtBC,UAAU,EAAE,cAAc;EAC1BC,UAAU,EAAE,cAAc;EAC1BC,WAAW,EAAE,eAAe;EAC5BC,QAAQ,EAAE,YAAY;EACtBC,SAAS,EAAE,aAAa;EACxBC,UAAU,EAAE,cAAc;EAC1BC,WAAW,EAAE,eAAe;EAC5BC,QAAQ,EAAE;AACZ,CAAC;AACD,OAAO,IAAIC,WAAW,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,YAAY,EAAE,WAAW,CAAC;AAC3K,OAAO,IAAIC,YAAY,GAAG,CAAC,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,CAAC;AAClI,OAAO,IAAIC,aAAa,GAAG,CAAC,MAAM,CAAC;;AAEnC;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,IAAI,EAAE;EACxD,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5B,OAAOA,IAAI;EACb;EACA,IAAI,CAACA,IAAI,EAAE;IACT,OAAO,EAAE;EACX;EACA,OAAOA,IAAI,CAACC,WAAW,IAAID,IAAI,CAACE,IAAI,IAAI,WAAW;AACrD,CAAC;;AAED;AACA;AACA,IAAIC,YAAY,GAAG,IAAI;AACvB,IAAIC,UAAU,GAAG,IAAI;AACrB,OAAO,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,QAAQ,EAAE;EAC9C,IAAIA,QAAQ,KAAKH,YAAY,IAAII,KAAK,CAACC,OAAO,CAACJ,UAAU,CAAC,EAAE;IAC1D,OAAOA,UAAU;EACnB;EACA,IAAIK,MAAM,GAAG,EAAE;EACfpC,QAAQ,CAACqC,OAAO,CAACJ,QAAQ,EAAE,UAAUK,KAAK,EAAE;IAC1C,IAAI1C,KAAK,CAAC0C,KAAK,CAAC,EAAE;IAClB,IAAIpC,UAAU,CAACoC,KAAK,CAAC,EAAE;MACrBF,MAAM,GAAGA,MAAM,CAACG,MAAM,CAACP,OAAO,CAACM,KAAK,CAACE,KAAK,CAACP,QAAQ,CAAC,CAAC;IACvD,CAAC,MAAM;MACL;MACAG,MAAM,CAACK,IAAI,CAACH,KAAK,CAAC;IACpB;EACF,CAAC,CAAC;EACFP,UAAU,GAAGK,MAAM;EACnBN,YAAY,GAAGG,QAAQ;EACvB,OAAOG,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,SAASM,aAAaA,CAACT,QAAQ,EAAEU,IAAI,EAAE;EAC5C,IAAIP,MAAM,GAAG,EAAE;EACf,IAAIQ,KAAK,GAAG,EAAE;EACd,IAAIV,KAAK,CAACC,OAAO,CAACQ,IAAI,CAAC,EAAE;IACvBC,KAAK,GAAGD,IAAI,CAACE,GAAG,CAAC,UAAUC,CAAC,EAAE;MAC5B,OAAOpB,cAAc,CAACoB,CAAC,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC,MAAM;IACLF,KAAK,GAAG,CAAClB,cAAc,CAACiB,IAAI,CAAC,CAAC;EAChC;EACAX,OAAO,CAACC,QAAQ,CAAC,CAACI,OAAO,CAAC,UAAUC,KAAK,EAAE;IACzC,IAAIS,SAAS,GAAGpD,GAAG,CAAC2C,KAAK,EAAE,kBAAkB,CAAC,IAAI3C,GAAG,CAAC2C,KAAK,EAAE,WAAW,CAAC;IACzE,IAAIM,KAAK,CAAC3D,OAAO,CAAC8D,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;MACnCX,MAAM,CAACK,IAAI,CAACH,KAAK,CAAC;IACpB;EACF,CAAC,CAAC;EACF,OAAOF,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASY,eAAeA,CAACf,QAAQ,EAAEU,IAAI,EAAE;EAC9C,IAAIP,MAAM,GAAGM,aAAa,CAACT,QAAQ,EAAEU,IAAI,CAAC;EAC1C,OAAOP,MAAM,IAAIA,MAAM,CAAC,CAAC,CAAC;AAC5B;;AAEA;AACA;AACA;AACA,OAAO,IAAIa,WAAW,GAAG,SAASA,WAAWA,CAAChB,QAAQ,EAAEU,IAAI,EAAE;EAC5D,IAAIO,WAAW,GAAG,EAAE;EACpB,IAAIN,KAAK;EACT,IAAIV,KAAK,CAACC,OAAO,CAACQ,IAAI,CAAC,EAAE;IACvBC,KAAK,GAAGD,IAAI,CAACE,GAAG,CAAC,UAAUC,CAAC,EAAE;MAC5B,OAAOpB,cAAc,CAACoB,CAAC,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC,MAAM;IACLF,KAAK,GAAG,CAAClB,cAAc,CAACiB,IAAI,CAAC,CAAC;EAChC;EACAX,OAAO,CAACC,QAAQ,CAAC,CAACI,OAAO,CAAC,UAAUC,KAAK,EAAE;IACzC,IAAIV,WAAW,GAAGjC,GAAG,CAAC2C,KAAK,EAAE,kBAAkB,CAAC;IAChD,IAAIV,WAAW,IAAIgB,KAAK,CAAC3D,OAAO,CAAC2C,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE;MACpD;IACF;IACAsB,WAAW,CAACT,IAAI,CAACH,KAAK,CAAC;EACzB,CAAC,CAAC;EACF,OAAOY,WAAW;AACpB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,EAAE,EAAE;EAChE,IAAI,CAACA,EAAE,IAAI,CAACA,EAAE,CAACZ,KAAK,EAAE;IACpB,OAAO,KAAK;EACd;EACA,IAAIa,SAAS,GAAGD,EAAE,CAACZ,KAAK;IACtBc,KAAK,GAAGD,SAAS,CAACC,KAAK;IACvBC,MAAM,GAAGF,SAAS,CAACE,MAAM;EAC3B,IAAI,CAACpD,QAAQ,CAACmD,KAAK,CAAC,IAAIA,KAAK,IAAI,CAAC,IAAI,CAACnD,QAAQ,CAACoD,MAAM,CAAC,IAAIA,MAAM,IAAI,CAAC,EAAE;IACtE,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb,CAAC;AACD,IAAIC,QAAQ,GAAG,CAAC,GAAG,EAAE,UAAU,EAAE,aAAa,EAAE,cAAc,EAAE,SAAS,EAAE,cAAc,EAAE,eAAe,EAAE,kBAAkB,EAAE,QAAQ,EAAE,UAAU,EAAE,eAAe,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,eAAe,EAAE,qBAAqB,EAAE,aAAa,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,gBAAgB,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,oBAAoB,EAAE,aAAa,EAAE,QAAQ,EAAE,cAAc,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,eAAe,EAAE,eAAe,EAAE,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC;AACh+B,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACnB,KAAK,EAAE;EAC9C,OAAOA,KAAK,IAAIA,KAAK,CAACK,IAAI,IAAI9C,QAAQ,CAACyC,KAAK,CAACK,IAAI,CAAC,IAAIa,QAAQ,CAACvE,OAAO,CAACqD,KAAK,CAACK,IAAI,CAAC,IAAI,CAAC;AACzF,CAAC;AACD,OAAO,IAAIe,UAAU,GAAG,SAASA,UAAUA,CAACC,GAAG,EAAE;EAC/C,OAAOA,GAAG,IAAIrE,OAAO,CAACqE,GAAG,CAAC,KAAK,QAAQ,IAAI,SAAS,IAAIA,GAAG;AAC7D,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,QAAQ,EAAElF,GAAG,EAAEmF,aAAa,EAAEC,cAAc,EAAE;EAC9G,IAAIC,qBAAqB;EACzB;AACF;AACA;AACA;AACA;EACE,IAAIC,uBAAuB,GAAG,CAACD,qBAAqB,GAAG3D,qBAAqB,KAAK,IAAI,IAAIA,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAAC0D,cAAc,CAAC,MAAM,IAAI,IAAIC,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,EAAE;EACrP,OAAOrF,GAAG,CAACuF,UAAU,CAAC,OAAO,CAAC,IAAI,CAACpE,UAAU,CAAC+D,QAAQ,CAAC,KAAKE,cAAc,IAAIE,uBAAuB,CAACE,QAAQ,CAACxF,GAAG,CAAC,IAAI2B,kBAAkB,CAAC6D,QAAQ,CAACxF,GAAG,CAAC,CAAC,IAAImF,aAAa,IAAIvD,SAAS,CAAC4D,QAAQ,CAACxF,GAAG,CAAC;AACtM,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIyF,iBAAiB,GAAG,SAASA,iBAAiBA,CAACnC,QAAQ,EAAE;EAClE,IAAIoC,WAAW,GAAG,EAAE;EACpBrC,OAAO,CAACC,QAAQ,CAAC,CAACI,OAAO,CAAC,UAAUiC,KAAK,EAAE;IACzC,IAAIb,YAAY,CAACa,KAAK,CAAC,EAAE;MACvBD,WAAW,CAAC5B,IAAI,CAAC6B,KAAK,CAAC;IACzB;EACF,CAAC,CAAC;EACF,OAAOD,WAAW;AACpB,CAAC;AACD,OAAO,IAAIE,WAAW,GAAG,SAASA,WAAWA,CAAC/B,KAAK,EAAEsB,aAAa,EAAEC,cAAc,EAAE;EAClF,IAAI,CAACvB,KAAK,IAAI,OAAOA,KAAK,KAAK,UAAU,IAAI,OAAOA,KAAK,KAAK,SAAS,EAAE;IACvE,OAAO,IAAI;EACb;EACA,IAAIgC,UAAU,GAAGhC,KAAK;EACtB,IAAK,aAAavC,cAAc,CAACuC,KAAK,CAAC,EAAE;IACvCgC,UAAU,GAAGhC,KAAK,CAACA,KAAK;EAC1B;EACA,IAAI,CAACzC,QAAQ,CAACyE,UAAU,CAAC,EAAE;IACzB,OAAO,IAAI;EACb;EACA,IAAIC,GAAG,GAAG,CAAC,CAAC;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;EACE5F,MAAM,CAAC6F,IAAI,CAACF,UAAU,CAAC,CAACnC,OAAO,CAAC,UAAU1D,GAAG,EAAE;IAC7C,IAAIgG,WAAW;IACf,IAAIf,qBAAqB,CAAC,CAACe,WAAW,GAAGH,UAAU,MAAM,IAAI,IAAIG,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAChG,GAAG,CAAC,EAAEA,GAAG,EAAEmF,aAAa,EAAEC,cAAc,CAAC,EAAE;MACxJU,GAAG,CAAC9F,GAAG,CAAC,GAAG6F,UAAU,CAAC7F,GAAG,CAAC;IAC5B;EACF,CAAC,CAAC;EACF,OAAO8F,GAAG;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIG,eAAe,GAAG,SAASA,eAAeA,CAACC,YAAY,EAAEC,YAAY,EAAE;EAChF,IAAID,YAAY,KAAKC,YAAY,EAAE;IACjC,OAAO,IAAI;EACb;EACA,IAAIC,KAAK,GAAG/E,QAAQ,CAAC+E,KAAK,CAACF,YAAY,CAAC;EACxC,IAAIE,KAAK,KAAK/E,QAAQ,CAAC+E,KAAK,CAACD,YAAY,CAAC,EAAE;IAC1C,OAAO,KAAK;EACd;EACA,IAAIC,KAAK,KAAK,CAAC,EAAE;IACf,OAAO,IAAI;EACb;EACA,IAAIA,KAAK,KAAK,CAAC,EAAE;IACf;IACA,OAAOC,kBAAkB,CAAC9C,KAAK,CAACC,OAAO,CAAC0C,YAAY,CAAC,GAAGA,YAAY,CAAC,CAAC,CAAC,GAAGA,YAAY,EAAE3C,KAAK,CAACC,OAAO,CAAC2C,YAAY,CAAC,GAAGA,YAAY,CAAC,CAAC,CAAC,GAAGA,YAAY,CAAC;EACvJ;EACA,KAAK,IAAIlG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmG,KAAK,EAAEnG,CAAC,EAAE,EAAE;IAC9B,IAAIqG,SAAS,GAAGJ,YAAY,CAACjG,CAAC,CAAC;IAC/B,IAAIsG,SAAS,GAAGJ,YAAY,CAAClG,CAAC,CAAC;IAC/B,IAAIsD,KAAK,CAACC,OAAO,CAAC8C,SAAS,CAAC,IAAI/C,KAAK,CAACC,OAAO,CAAC+C,SAAS,CAAC,EAAE;MACxD,IAAI,CAACN,eAAe,CAACK,SAAS,EAAEC,SAAS,CAAC,EAAE;QAC1C,OAAO,KAAK;MACd;MACA;IACF,CAAC,MAAM,IAAI,CAACF,kBAAkB,CAACC,SAAS,EAAEC,SAAS,CAAC,EAAE;MACpD,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb,CAAC;AACD,OAAO,IAAIF,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,SAAS,EAAEC,SAAS,EAAE;EAChF,IAAItF,KAAK,CAACqF,SAAS,CAAC,IAAIrF,KAAK,CAACsF,SAAS,CAAC,EAAE;IACxC,OAAO,IAAI;EACb;EACA,IAAI,CAACtF,KAAK,CAACqF,SAAS,CAAC,IAAI,CAACrF,KAAK,CAACsF,SAAS,CAAC,EAAE;IAC1C,IAAIC,IAAI,GAAGF,SAAS,CAACzC,KAAK,IAAI,CAAC,CAAC;MAC9BqC,YAAY,GAAGM,IAAI,CAAClD,QAAQ;MAC5BmD,SAAS,GAAG9G,wBAAwB,CAAC6G,IAAI,EAAE/G,SAAS,CAAC;IACvD,IAAIiH,KAAK,GAAGH,SAAS,CAAC1C,KAAK,IAAI,CAAC,CAAC;MAC/BsC,YAAY,GAAGO,KAAK,CAACpD,QAAQ;MAC7BqD,SAAS,GAAGhH,wBAAwB,CAAC+G,KAAK,EAAEhH,UAAU,CAAC;IACzD,IAAIwG,YAAY,IAAIC,YAAY,EAAE;MAChC,OAAO1E,YAAY,CAACgF,SAAS,EAAEE,SAAS,CAAC,IAAIV,eAAe,CAACC,YAAY,EAAEC,YAAY,CAAC;IAC1F;IACA,IAAI,CAACD,YAAY,IAAI,CAACC,YAAY,EAAE;MAClC,OAAO1E,YAAY,CAACgF,SAAS,EAAEE,SAAS,CAAC;IAC3C;IACA,OAAO,KAAK;EACd;EACA,OAAO,KAAK;AACd,CAAC;AACD,OAAO,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACtD,QAAQ,EAAEuD,SAAS,EAAE;EACrE,IAAIC,QAAQ,GAAG,EAAE;EACjB,IAAIC,MAAM,GAAG,CAAC,CAAC;EACf1D,OAAO,CAACC,QAAQ,CAAC,CAACI,OAAO,CAAC,UAAUC,KAAK,EAAEqD,KAAK,EAAE;IAChD,IAAIlC,YAAY,CAACnB,KAAK,CAAC,EAAE;MACvBmD,QAAQ,CAAChD,IAAI,CAACH,KAAK,CAAC;IACtB,CAAC,MAAM,IAAIA,KAAK,EAAE;MAChB,IAAIV,WAAW,GAAGF,cAAc,CAACY,KAAK,CAACK,IAAI,CAAC;MAC5C,IAAIiD,KAAK,GAAGJ,SAAS,CAAC5D,WAAW,CAAC,IAAI,CAAC,CAAC;QACtCiE,OAAO,GAAGD,KAAK,CAACC,OAAO;QACvBC,IAAI,GAAGF,KAAK,CAACE,IAAI;MACnB,IAAID,OAAO,KAAK,CAACC,IAAI,IAAI,CAACJ,MAAM,CAAC9D,WAAW,CAAC,CAAC,EAAE;QAC9C,IAAImE,OAAO,GAAGF,OAAO,CAACvD,KAAK,EAAEV,WAAW,EAAE+D,KAAK,CAAC;QAChDF,QAAQ,CAAChD,IAAI,CAACsD,OAAO,CAAC;QACtBL,MAAM,CAAC9D,WAAW,CAAC,GAAG,IAAI;MAC5B;IACF;EACF,CAAC,CAAC;EACF,OAAO6D,QAAQ;AACjB,CAAC;AACD,OAAO,IAAIO,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,CAAC,EAAE;EAC/D,IAAItD,IAAI,GAAGsD,CAAC,IAAIA,CAAC,CAACtD,IAAI;EACtB,IAAIA,IAAI,IAAInC,uBAAuB,CAACmC,IAAI,CAAC,EAAE;IACzC,OAAOnC,uBAAuB,CAACmC,IAAI,CAAC;EACtC;EACA,OAAO,IAAI;AACb,CAAC;AACD,OAAO,IAAIuD,eAAe,GAAG,SAASA,eAAeA,CAAC5D,KAAK,EAAEL,QAAQ,EAAE;EACrE,OAAOD,OAAO,CAACC,QAAQ,CAAC,CAAChD,OAAO,CAACqD,KAAK,CAAC;AACzC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}