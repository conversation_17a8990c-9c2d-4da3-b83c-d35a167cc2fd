# 📱 ProChat Mobile App Deployment Guide

This comprehensive guide will help you build and deploy ProChat mobile app for all platforms and distribution methods.

## 🚀 Quick Start Commands

### Prerequisites
```bash
# Install EAS CLI globally
npm install -g @expo/eas-cli

# Login to Expo
eas login

# Navigate to mobile directory
cd mobile

# Install dependencies
npm install
```

## 📱 Android Deployment

### 1. Google Play Store (AAB)
```bash
# Build Android App Bundle for Play Store
chmod +x scripts/build-android.sh
./scripts/build-android.sh aab

# Or manually:
eas build --platform android --profile production
```

### 2. Direct APK Distribution
```bash
# Build APK for direct installation
./scripts/build-android.sh apk

# Or manually:
eas build --platform android --profile production-apk
```

### 3. Preview/Testing APK
```bash
# Build preview APK for testing
./scripts/build-android.sh preview

# Or manually:
eas build --platform android --profile preview
```

### 4. Build All Android Formats
```bash
# Build both AAB and APK
./scripts/build-android.sh all
```

## 🍎 iOS Deployment

### 1. App Store Distribution
```bash
# Build for App Store
chmod +x scripts/build-ios.sh
./scripts/build-ios.sh production

# Or manually:
eas build --platform ios --profile production
```

### 2. TestFlight/Internal Testing
```bash
# Build for TestFlight
./scripts/build-ios.sh preview

# Or manually:
eas build --platform ios --profile preview
```

## 📦 Download Built Apps

### Check Build Status
```bash
# List recent builds
eas build:list --platform android --limit 5
eas build:list --platform ios --limit 5
```

### Download Builds
```bash
# Download specific build
eas build:download [BUILD_ID]

# Download latest build
eas build:download --platform android
eas build:download --platform ios
```

## 🔧 Configuration Files

### App Configuration (app.json)
- ✅ Package name: `com.prochat.app`
- ✅ Bundle identifier: `com.prochat.app`
- ✅ Version: `1.0.0`
- ✅ Permissions configured
- ✅ Icons and splash screen

### Build Profiles (eas.json)
- ✅ Production profile for stores
- ✅ Preview profile for testing
- ✅ Development profile for dev builds

### Environment Variables (.env.production)
- ✅ Production API URLs
- ✅ Firebase configuration
- ✅ AWS S3 settings
- ✅ Feature flags

## 📱 App Store Submission

### Google Play Store
1. **Prepare Assets**:
   - App icon (512x512)
   - Feature graphic (1024x500)
   - Screenshots (various sizes)
   - App description

2. **Upload AAB**:
   - Go to Google Play Console
   - Create new app or new release
   - Upload `app-release.aab`
   - Fill app information
   - Submit for review

3. **Required Information**:
   - App name: ProChat
   - Package name: com.prochat.app
   - Category: Social
   - Content rating: Teen (13+)
   - Privacy policy: https://prochat.co.tz/privacy-policy

### Apple App Store
1. **Prepare Assets**:
   - App icon (1024x1024)
   - Screenshots for all device sizes
   - App preview video (optional)
   - App description

2. **Upload IPA**:
   - Go to App Store Connect
   - Create app record
   - Upload `ProChat.ipa` via Xcode or Transporter
   - Fill app information
   - Submit for review

3. **Required Information**:
   - App name: ProChat
   - Bundle ID: com.prochat.app
   - Category: Social Networking
   - Age rating: 13+
   - Privacy policy: https://prochat.co.tz/privacy-policy

## 🌐 Direct Distribution

### Website Download
1. **Host APK**:
   ```bash
   # Upload app-release.apk to your website
   # Example: https://prochat.co.tz/download/prochat.apk
   ```

2. **Download Page**:
   ```html
   <a href="/download/prochat.apk" download>
     Download ProChat APK
   </a>
   ```

3. **Installation Instructions**:
   - Enable "Unknown Sources" in Android settings
   - Download APK file
   - Tap to install

### Direct Device Install
1. **ADB Install**:
   ```bash
   adb install app-release.apk
   ```

2. **File Transfer**:
   - Transfer APK to device
   - Use file manager to install
   - Enable unknown sources if needed

## 🔒 Code Signing

### Android Keystore
```bash
# Generate keystore (if needed)
keytool -genkey -v -keystore prochat-release-key.keystore \
  -alias prochat-key-alias -keyalg RSA -keysize 2048 -validity 10000

# EAS handles signing automatically with managed workflow
```

### iOS Certificates
```bash
# EAS handles iOS certificates automatically
# Or configure manually in Apple Developer Portal
```

## 🧪 Testing Before Release

### Device Testing
1. **Android**:
   - Test on various Android versions (7.0+)
   - Test on different screen sizes
   - Test with/without Google Play Services

2. **iOS**:
   - Test on various iOS versions (13.0+)
   - Test on iPhone and iPad
   - Test with TestFlight

### Feature Testing Checklist
- [ ] User registration/login
- [ ] Chat functionality
- [ ] ProPay wallet operations
- [ ] Event browsing and ticketing
- [ ] Job applications
- [ ] Live streaming
- [ ] Push notifications
- [ ] Camera/photo upload
- [ ] Location services
- [ ] Offline functionality

## 📊 Analytics & Monitoring

### Crash Reporting
- Configured with Expo Application Services
- Real-time crash monitoring
- Performance metrics

### User Analytics
- User engagement tracking
- Feature usage analytics
- Revenue tracking

## 🔄 Update Strategy

### Over-the-Air Updates
```bash
# Publish OTA update
eas update --branch production --message "Bug fixes and improvements"
```

### App Store Updates
- Increment version number
- Build new release
- Submit to stores
- Communicate changes to users

## 🆘 Troubleshooting

### Common Build Issues
1. **Build Failed**:
   - Check build logs in EAS dashboard
   - Verify all dependencies are compatible
   - Check for syntax errors

2. **Signing Issues**:
   - Verify keystore/certificates
   - Check bundle identifier matches

3. **Upload Issues**:
   - Verify app meets store requirements
   - Check file size limits
   - Ensure proper metadata

### Getting Help
- **EAS Documentation**: https://docs.expo.dev/build/introduction/
- **Expo Forums**: https://forums.expo.dev/
- **ProChat Support**: <EMAIL>

## 📞 Support Contacts

**Developer Support**: <EMAIL>
**Technical Issues**: <EMAIL>
**Business Inquiries**: <EMAIL>

---

**Ready to launch ProChat to the world! 🚀**
