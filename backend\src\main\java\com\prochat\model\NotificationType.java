package com.prochat.model;

public enum NotificationType {
    // Social notifications
    NEW_FOLLOWER,
    POST_LIKED,
    POST_COMMENTED,
    POST_SHARED,
    MENTION,
    
    // Chat notifications
    NEW_MESSAGE,
    MISSED_CALL,
    VI<PERSON><PERSON>_CALL,
    VO<PERSON><PERSON>_CALL,
    
    // Financial notifications
    MONEY_RECEIVED,
    MONEY_SENT,
    PAYMENT_SUCCESSFUL,
    PAYMENT_FAILED,
    LOW_BALANCE,
    TRANSACTION_LIMIT_REACHED,
    
    // Gift notifications
    GIFT_RECEIVED,
    GIFT_SENT,
    
    // Live stream notifications
    STREAM_STARTED,
    STREAM_ENDED,
    STREAM_REMINDER,
    
    // Event notifications
    EVENT_REMINDER,
    TICKET_PURCHASED,
    EVENT_CANCELLED,
    EVENT_UPDATED,
    
    // System notifications
    ACCOUNT_VERIFIED,
    SECURITY_ALERT,
    SYSTEM_MAINTENANCE,
    APP_UPDATE,
    WELCO<PERSON>,
    
    // Advertisement notifications
    AD_APPROVED,
    AD_REJECTED,
    AD_EXPIRED,
    
    // Agent/Merchant notifications
    AGENT_APPLICATION_STATUS,
    ME<PERSON>HANT_APPLICATION_STATUS,
    COMMISSION_EARNED,
    
    // General
    ANNOUNCEMENT,
    PROMOTION,
    REMINDER,
    WARNING,
    ERROR
}
