{"ast": null, "code": "export default function range(start, stop, step) {\n  start = +start, stop = +stop, step = (n = arguments.length) < 2 ? (stop = start, start = 0, 1) : n < 3 ? 1 : +step;\n  var i = -1,\n    n = Math.max(0, Math.ceil((stop - start) / step)) | 0,\n    range = new Array(n);\n  while (++i < n) {\n    range[i] = start + i * step;\n  }\n  return range;\n}", "map": {"version": 3, "names": ["range", "start", "stop", "step", "n", "arguments", "length", "i", "Math", "max", "ceil", "Array"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/d3-array/src/range.js"], "sourcesContent": ["export default function range(start, stop, step) {\n  start = +start, stop = +stop, step = (n = arguments.length) < 2 ? (stop = start, start = 0, 1) : n < 3 ? 1 : +step;\n\n  var i = -1,\n      n = Math.max(0, Math.ceil((stop - start) / step)) | 0,\n      range = new Array(n);\n\n  while (++i < n) {\n    range[i] = start + i * step;\n  }\n\n  return range;\n}\n"], "mappings": "AAAA,eAAe,SAASA,KAAKA,CAACC,KAAK,EAAEC,IAAI,EAAEC,IAAI,EAAE;EAC/CF,KAAK,GAAG,CAACA,KAAK,EAAEC,IAAI,GAAG,CAACA,IAAI,EAAEC,IAAI,GAAG,CAACC,CAAC,GAAGC,SAAS,CAACC,MAAM,IAAI,CAAC,IAAIJ,IAAI,GAAGD,KAAK,EAAEA,KAAK,GAAG,CAAC,EAAE,CAAC,IAAIG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAACD,IAAI;EAElH,IAAII,CAAC,GAAG,CAAC,CAAC;IACNH,CAAC,GAAGI,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,IAAI,CAAC,CAACR,IAAI,GAAGD,KAAK,IAAIE,IAAI,CAAC,CAAC,GAAG,CAAC;IACrDH,KAAK,GAAG,IAAIW,KAAK,CAACP,CAAC,CAAC;EAExB,OAAO,EAAEG,CAAC,GAAGH,CAAC,EAAE;IACdJ,KAAK,CAACO,CAAC,CAAC,GAAGN,KAAK,GAAGM,CAAC,GAAGJ,IAAI;EAC7B;EAEA,OAAOH,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}