#!/bin/bash

# ProChat Android Build Script
# This script builds ProChat for Android in various formats

set -e

echo "🚀 Starting ProChat Android Build Process..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the mobile directory
if [ ! -f "package.json" ]; then
    print_error "Please run this script from the mobile directory"
    exit 1
fi

# Check if EAS CLI is installed
if ! command -v eas &> /dev/null; then
    print_warning "EAS CLI not found. Installing..."
    npm install -g @expo/eas-cli
fi

# Login to EAS (if not already logged in)
print_status "Checking EAS authentication..."
if ! eas whoami &> /dev/null; then
    print_warning "Please login to EAS:"
    eas login
fi

# Install dependencies
print_status "Installing dependencies..."
npm install

# Clean previous builds
print_status "Cleaning previous builds..."
rm -rf dist/
mkdir -p dist/

# Set production environment
export NODE_ENV=production
cp .env.production .env

print_status "Building ProChat for Android..."

# Build options
BUILD_TYPE=${1:-"all"}

case $BUILD_TYPE in
    "aab")
        print_status "Building Android App Bundle (AAB) for Google Play Store..."
        eas build --platform android --profile production
        ;;
    "apk")
        print_status "Building APK for direct distribution..."
        eas build --platform android --profile production-apk
        ;;
    "preview")
        print_status "Building preview APK for testing..."
        eas build --platform android --profile preview
        ;;
    "all")
        print_status "Building all Android formats..."
        
        # Build AAB for Play Store
        print_status "1/2 Building AAB for Google Play Store..."
        eas build --platform android --profile production
        
        # Build APK for direct distribution
        print_status "2/2 Building APK for direct distribution..."
        eas build --platform android --profile production-apk
        ;;
    *)
        print_error "Invalid build type. Use: aab, apk, preview, or all"
        exit 1
        ;;
esac

print_success "Android build process completed!"

# Download builds
print_status "Downloading builds to dist/ folder..."
eas build:list --platform android --limit 5

print_status "To download your builds, use:"
echo "eas build:download [BUILD_ID]"

# Generate build info
cat > dist/build-info.txt << EOF
ProChat Android Build Information
================================

Build Date: $(date)
Build Type: $BUILD_TYPE
Package Name: com.prochat.app
Version: 1.0.0
Version Code: 1

Files Generated:
- app-release.aab (for Google Play Store)
- app-release.apk (for direct distribution)

Installation Instructions:
1. For Google Play Store: Upload app-release.aab
2. For direct install: Use app-release.apk
3. Enable "Unknown Sources" for APK installation

Build Commands Used:
- AAB: eas build --platform android --profile production
- APK: eas build --platform android --profile production-apk

Next Steps:
1. Test the APK on various devices
2. Upload AAB to Google Play Console
3. Configure app store listing
4. Submit for review

Support: <EMAIL>
EOF

print_success "Build information saved to dist/build-info.txt"

echo ""
echo "🎉 ProChat Android build completed successfully!"
echo "📱 Your app is ready for distribution!"
echo ""
echo "Next steps:"
echo "1. Download your builds using: eas build:download [BUILD_ID]"
echo "2. Test the APK on devices"
echo "3. Upload AAB to Google Play Console"
echo "4. Configure app store listing"
echo ""
echo "For help: https://docs.expo.dev/build/introduction/"
