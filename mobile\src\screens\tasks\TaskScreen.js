import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  RefreshControl,
  Alert,
  Modal,
  ProgressBarAndroid,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors, typography, spacing } from '../../theme/theme';
import { tasksAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

export default function TaskScreen({ navigation }) {
  const { user } = useAuth();
  const [tasks, setTasks] = useState([]);
  const [userTasks, setUserTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);
  const [showTaskModal, setShowTaskModal] = useState(false);
  const [activeTab, setActiveTab] = useState('available'); // available, completed, rewards

  useEffect(() => {
    loadTasks();
    loadUserTasks();
  }, []);

  const loadTasks = async () => {
    try {
      const response = await tasksAPI.getAvailableTasks();
      if (response.success) {
        setTasks(response.data);
      }
    } catch (error) {
      console.error('Error loading tasks:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const loadUserTasks = async () => {
    try {
      const response = await tasksAPI.getUserTasks();
      if (response.success) {
        setUserTasks(response.data);
      }
    } catch (error) {
      console.error('Error loading user tasks:', error);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadTasks();
    loadUserTasks();
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getTaskIcon = (taskType) => {
    switch (taskType) {
      case 'LIKE_POSTS': return 'thumb-up';
      case 'COMMENT_POSTS': return 'comment';
      case 'SHARE_POSTS': return 'share';
      case 'INVITE_FRIENDS': return 'person-add';
      case 'COMPLETE_PROFILE': return 'account-circle';
      case 'VERIFY_EMAIL': return 'email';
      case 'VERIFY_PHONE': return 'phone';
      case 'FOLLOW_USERS': return 'person-add-alt';
      case 'CREATE_POSTS': return 'create';
      case 'WATCH_VIDEOS': return 'play-circle';
      case 'ATTEND_EVENTS': return 'event';
      case 'BUY_TICKETS': return 'confirmation-number';
      case 'USE_PROPAY': return 'account-balance-wallet';
      case 'SEND_GIFTS': return 'card-giftcard';
      case 'DAILY_LOGIN': return 'login';
      case 'WEEKLY_ACTIVE': return 'trending-up';
      case 'MONTHLY_CHALLENGE': return 'emoji-events';
      default: return 'task-alt';
    }
  };

  const getTaskTypeDisplayName = (taskType) => {
    switch (taskType) {
      case 'LIKE_POSTS': return 'Penda Machapisho';
      case 'COMMENT_POSTS': return 'Andika Maoni';
      case 'SHARE_POSTS': return 'Shiriki Machapisho';
      case 'INVITE_FRIENDS': return 'Alika Marafiki';
      case 'COMPLETE_PROFILE': return 'Kamilisha Wasifu';
      case 'VERIFY_EMAIL': return 'Thibitisha Barua Pepe';
      case 'VERIFY_PHONE': return 'Thibitisha Simu';
      case 'FOLLOW_USERS': return 'Fuata Watumiaji';
      case 'CREATE_POSTS': return 'Tengeneza Machapisho';
      case 'WATCH_VIDEOS': return 'Tazama Video';
      case 'ATTEND_EVENTS': return 'Hudhuria Matukio';
      case 'BUY_TICKETS': return 'Nunua Tiketi';
      case 'USE_PROPAY': return 'Tumia ProPay';
      case 'SEND_GIFTS': return 'Tuma Zawadi';
      case 'DAILY_LOGIN': return 'Ingia Kila Siku';
      case 'WEEKLY_ACTIVE': return 'Kuwa Hai Wiki Nzima';
      case 'MONTHLY_CHALLENGE': return 'Changamoto ya Mwezi';
      default: return taskType;
    }
  };

  const handleTaskPress = (task) => {
    setSelectedTask(task);
    setShowTaskModal(true);
  };

  const handleStartTask = async (task) => {
    try {
      const response = await tasksAPI.startTask(task.id);
      if (response.success) {
        Alert.alert('Mafanikio!', 'Kazi imeanza. Kamilisha ili kupata tuzo!');
        setShowTaskModal(false);
        loadUserTasks();
      } else {
        Alert.alert('Hitilafu', response.message || 'Imeshindwa kuanza kazi');
      }
    } catch (error) {
      Alert.alert('Hitilafu', error.message || 'Hitilafu ya mtandao');
    }
  };

  const handleCompleteTask = async (userTask) => {
    try {
      const response = await tasksAPI.completeTask(userTask.id);
      if (response.success) {
        Alert.alert(
          'Hongera!',
          `Umekamilisha kazi na kupata ${formatCurrency(response.data.rewardAmount)}!`,
          [
            {
              text: 'Sawa',
              onPress: () => {
                loadUserTasks();
                loadTasks();
              }
            }
          ]
        );
      } else {
        Alert.alert('Hitilafu', response.message || 'Imeshindwa kukamilisha kazi');
      }
    } catch (error) {
      Alert.alert('Hitilafu', error.message || 'Hitilafu ya mtandao');
    }
  };

  const renderAvailableTask = ({ item }) => {
    const userTask = userTasks.find(ut => ut.taskId === item.id);
    const isStarted = !!userTask;
    const isCompleted = userTask?.status === 'COMPLETED';
    
    return (
      <TouchableOpacity
        style={[
          styles.taskItem,
          isCompleted && styles.completedTaskItem
        ]}
        onPress={() => handleTaskPress(item)}
        disabled={isCompleted}
      >
        <View style={[
          styles.taskIcon,
          { backgroundColor: isCompleted ? colors.success + '20' : colors.primary + '20' }
        ]}>
          <Icon 
            name={getTaskIcon(item.taskType)} 
            size={24} 
            color={isCompleted ? colors.success : colors.primary} 
          />
        </View>
        
        <View style={styles.taskContent}>
          <Text style={styles.taskTitle}>
            {getTaskTypeDisplayName(item.taskType)}
          </Text>
          <Text style={styles.taskDescription} numberOfLines={2}>
            {item.description}
          </Text>
          
          <View style={styles.taskDetails}>
            <View style={styles.taskProgress}>
              <Text style={styles.progressText}>
                {userTask ? `${userTask.currentProgress}/${item.targetCount}` : `0/${item.targetCount}`}
              </Text>
              {userTask && (
                <View style={styles.progressBar}>
                  <View 
                    style={[
                      styles.progressFill,
                      { width: `${(userTask.currentProgress / item.targetCount) * 100}%` }
                    ]} 
                  />
                </View>
              )}
            </View>
            
            <Text style={styles.taskReward}>
              {formatCurrency(item.rewardAmount)}
            </Text>
          </View>
          
          <View style={styles.taskMeta}>
            {item.isDaily && (
              <View style={styles.taskBadge}>
                <Text style={styles.badgeText}>Kila Siku</Text>
              </View>
            )}
            {item.isWeekly && (
              <View style={[styles.taskBadge, { backgroundColor: colors.warning + '20' }]}>
                <Text style={[styles.badgeText, { color: colors.warning }]}>Wiki</Text>
              </View>
            )}
            {item.isMonthly && (
              <View style={[styles.taskBadge, { backgroundColor: colors.info + '20' }]}>
                <Text style={[styles.badgeText, { color: colors.info }]}>Mwezi</Text>
              </View>
            )}
          </View>
        </View>
        
        {isCompleted ? (
          <Icon name="check-circle" size={24} color={colors.success} />
        ) : isStarted ? (
          <TouchableOpacity
            style={styles.completeButton}
            onPress={() => handleCompleteTask(userTask)}
          >
            <Text style={styles.completeButtonText}>Kamilisha</Text>
          </TouchableOpacity>
        ) : (
          <Icon name="chevron-right" size={24} color={colors.gray} />
        )}
      </TouchableOpacity>
    );
  };

  const renderCompletedTask = ({ item }) => (
    <View style={[styles.taskItem, styles.completedTaskItem]}>
      <View style={[styles.taskIcon, { backgroundColor: colors.success + '20' }]}>
        <Icon name={getTaskIcon(item.task.taskType)} size={24} color={colors.success} />
      </View>
      
      <View style={styles.taskContent}>
        <Text style={styles.taskTitle}>
          {getTaskTypeDisplayName(item.task.taskType)}
        </Text>
        <Text style={styles.completedDate}>
          Imekamilika: {new Date(item.completedAt).toLocaleDateString('sw-TZ')}
        </Text>
        <Text style={styles.earnedReward}>
          Umepata: {formatCurrency(item.rewardEarned)}
        </Text>
      </View>
      
      <Icon name="check-circle" size={24} color={colors.success} />
    </View>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'available':
        return (
          <FlatList
            data={tasks}
            renderItem={renderAvailableTask}
            keyExtractor={(item) => item.id.toString()}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.tasksList}
          />
        );
      
      case 'completed':
        const completedTasks = userTasks.filter(ut => ut.status === 'COMPLETED');
        return (
          <FlatList
            data={completedTasks}
            renderItem={renderCompletedTask}
            keyExtractor={(item) => item.id.toString()}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.tasksList}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Icon name="task-alt" size={64} color={colors.gray} />
                <Text style={styles.emptyText}>Hujamalizia kazi yoyote bado</Text>
              </View>
            }
          />
        );
      
      case 'rewards':
        const totalEarned = userTasks
          .filter(ut => ut.status === 'COMPLETED')
          .reduce((sum, ut) => sum + (ut.rewardEarned || 0), 0);
        
        return (
          <View style={styles.rewardsContainer}>
            <View style={styles.rewardsCard}>
              <Icon name="emoji-events" size={48} color={colors.warning} />
              <Text style={styles.rewardsTitle}>Jumla ya Tuzo</Text>
              <Text style={styles.rewardsAmount}>
                {formatCurrency(totalEarned)}
              </Text>
              <Text style={styles.rewardsSubtext}>
                Kutoka kazi {userTasks.filter(ut => ut.status === 'COMPLETED').length}
              </Text>
            </View>
            
            <View style={styles.rewardsStats}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>
                  {userTasks.filter(ut => ut.status === 'IN_PROGRESS').length}
                </Text>
                <Text style={styles.statLabel}>Zinaendelea</Text>
              </View>
              
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>
                  {userTasks.filter(ut => ut.status === 'COMPLETED').length}
                </Text>
                <Text style={styles.statLabel}>Zimekamilika</Text>
              </View>
              
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>
                  {tasks.length - userTasks.length}
                </Text>
                <Text style={styles.statLabel}>Zinapatikana</Text>
              </View>
            </View>
          </View>
        );
      
      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Kazi za ProChat</Text>
        <TouchableOpacity style={styles.infoButton}>
          <Icon name="info" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      {/* Tabs */}
      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'available' && styles.activeTab]}
          onPress={() => setActiveTab('available')}
        >
          <Text style={[
            styles.tabText,
            activeTab === 'available' && styles.activeTabText
          ]}>
            Zinapatikana
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tab, activeTab === 'completed' && styles.activeTab]}
          onPress={() => setActiveTab('completed')}
        >
          <Text style={[
            styles.tabText,
            activeTab === 'completed' && styles.activeTabText
          ]}>
            Zimekamilika
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tab, activeTab === 'rewards' && styles.activeTab]}
          onPress={() => setActiveTab('rewards')}
        >
          <Text style={[
            styles.tabText,
            activeTab === 'rewards' && styles.activeTabText
          ]}>
            Tuzo
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      {renderTabContent()}

      {/* Task Details Modal */}
      <Modal
        visible={showTaskModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowTaskModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {selectedTask && (
              <>
                <View style={styles.modalHeader}>
                  <Icon 
                    name={getTaskIcon(selectedTask.taskType)} 
                    size={32} 
                    color={colors.primary} 
                  />
                  <Text style={styles.modalTitle}>
                    {getTaskTypeDisplayName(selectedTask.taskType)}
                  </Text>
                </View>
                
                <Text style={styles.modalDescription}>
                  {selectedTask.description}
                </Text>
                
                <View style={styles.modalDetails}>
                  <View style={styles.modalDetailRow}>
                    <Text style={styles.modalDetailLabel}>Lengo:</Text>
                    <Text style={styles.modalDetailValue}>
                      {selectedTask.targetCount} {selectedTask.taskType === 'LIKE_POSTS' ? 'mapendekezo' : 'vitendo'}
                    </Text>
                  </View>
                  
                  <View style={styles.modalDetailRow}>
                    <Text style={styles.modalDetailLabel}>Tuzo:</Text>
                    <Text style={[styles.modalDetailValue, { color: colors.success }]}>
                      {formatCurrency(selectedTask.rewardAmount)}
                    </Text>
                  </View>
                  
                  {selectedTask.endDate && (
                    <View style={styles.modalDetailRow}>
                      <Text style={styles.modalDetailLabel}>Mwisho:</Text>
                      <Text style={styles.modalDetailValue}>
                        {new Date(selectedTask.endDate).toLocaleDateString('sw-TZ')}
                      </Text>
                    </View>
                  )}
                </View>
                
                <View style={styles.modalActions}>
                  <TouchableOpacity
                    style={styles.modalCancelButton}
                    onPress={() => setShowTaskModal(false)}
                  >
                    <Text style={styles.modalCancelText}>Ghairi</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={styles.modalStartButton}
                    onPress={() => handleStartTask(selectedTask)}
                  >
                    <Text style={styles.modalStartText}>Anza Kazi</Text>
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
  },
  infoButton: {
    padding: spacing.sm,
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  tab: {
    flex: 1,
    paddingVertical: spacing.md,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
  },
  tabText: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
  },
  activeTabText: {
    color: colors.primary,
    fontWeight: typography.fontWeight.semiBold,
  },
  tasksList: {
    padding: spacing.lg,
  },
  taskItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.md,
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
  },
  completedTaskItem: {
    opacity: 0.7,
  },
  taskIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  taskContent: {
    flex: 1,
  },
  taskTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  taskDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginBottom: spacing.sm,
  },
  taskDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  taskProgress: {
    flex: 1,
  },
  progressText: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  progressBar: {
    height: 4,
    backgroundColor: colors.border,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
  },
  taskReward: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.bold,
    color: colors.success,
  },
  taskMeta: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  taskBadge: {
    backgroundColor: colors.primary + '20',
    paddingHorizontal: spacing.sm,
    paddingVertical: 2,
    borderRadius: 12,
  },
  badgeText: {
    fontSize: typography.fontSize.xs,
    color: colors.primary,
    fontWeight: typography.fontWeight.medium,
  },
  completeButton: {
    backgroundColor: colors.success,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 8,
  },
  completeButtonText: {
    color: colors.white,
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  completedDate: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  earnedReward: {
    fontSize: typography.fontSize.sm,
    color: colors.success,
    fontWeight: typography.fontWeight.medium,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xxxl,
  },
  emptyText: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  rewardsContainer: {
    padding: spacing.lg,
  },
  rewardsCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.xl,
    alignItems: 'center',
    marginBottom: spacing.lg,
    borderWidth: 1,
    borderColor: colors.border,
  },
  rewardsTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  rewardsAmount: {
    fontSize: typography.fontSize.xxxl,
    fontWeight: typography.fontWeight.bold,
    color: colors.success,
    marginBottom: spacing.xs,
  },
  rewardsSubtext: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
  rewardsStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.lg,
    borderWidth: 1,
    borderColor: colors.border,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary,
    marginBottom: spacing.xs,
  },
  statLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.lg,
    width: '90%',
    maxHeight: '80%',
  },
  modalHeader: {
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  modalTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
    marginTop: spacing.sm,
    textAlign: 'center',
  },
  modalDescription: {
    fontSize: typography.fontSize.md,
    color: colors.text,
    lineHeight: 22,
    marginBottom: spacing.lg,
    textAlign: 'center',
  },
  modalDetails: {
    marginBottom: spacing.lg,
  },
  modalDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  modalDetailLabel: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
  },
  modalDetailValue: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
  },
  modalActions: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  modalCancelButton: {
    flex: 1,
    paddingVertical: spacing.md,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    alignItems: 'center',
  },
  modalCancelText: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
  },
  modalStartButton: {
    flex: 1,
    paddingVertical: spacing.md,
    borderRadius: 8,
    backgroundColor: colors.primary,
    alignItems: 'center',
  },
  modalStartText: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.white,
  },
});
