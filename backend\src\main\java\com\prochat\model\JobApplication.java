package com.prochat.model;

import jakarta.persistence.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "job_applications")
@EntityListeners(AuditingEntityListener.class)
public class JobApplication {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "job_posting_id", nullable = false)
    private JobPosting jobPosting;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "applicant_id", nullable = false)
    private User applicant;
    
    @Column(name = "cover_letter", columnDefinition = "TEXT")
    private String coverLetter;
    
    @Column(name = "resume_url")
    private String resumeUrl;
    
    @Column(name = "portfolio_url")
    private String portfolioUrl;
    
    @Column(name = "expected_salary", precision = 15, scale = 2)
    private BigDecimal expectedSalary;
    
    @Column(name = "available_start_date")
    private LocalDateTime availableStartDate;
    
    @Column(name = "years_of_experience")
    private Integer yearsOfExperience;
    
    @Column(name = "education_level")
    private String educationLevel;
    
    @Column(name = "skills")
    private String skills; // Comma-separated
    
    @Column(name = "certifications")
    private String certifications; // Comma-separated
    
    @Column(name = "languages_spoken")
    private String languagesSpoken; // Comma-separated
    
    @Column(name = "references", columnDefinition = "TEXT")
    private String references;
    
    @Column(name = "additional_documents")
    private String additionalDocuments; // JSON array of document URLs
    
    @Column(name = "questionnaire_answers", columnDefinition = "TEXT")
    private String questionnaireAnswers; // JSON of custom questions and answers
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private ApplicationStatus status = ApplicationStatus.SUBMITTED;
    
    @Column(name = "ai_score", precision = 5, scale = 2)
    private BigDecimal aiScore; // AI-generated matching score (0-100)
    
    @Column(name = "admin_score", precision = 5, scale = 2)
    private BigDecimal adminScore; // Admin-assigned score (0-100)
    
    @Column(name = "ranking_position")
    private Integer rankingPosition;
    
    @Column(name = "is_shortlisted")
    private Boolean isShortlisted = false;
    
    @Column(name = "shortlisted_at")
    private LocalDateTime shortlistedAt;
    
    @Column(name = "shortlisted_by")
    private Long shortlistedBy;
    
    @Column(name = "interview_scheduled")
    private Boolean interviewScheduled = false;
    
    @Column(name = "interview_date")
    private LocalDateTime interviewDate;
    
    @Column(name = "interview_type")
    private String interviewType; // PHONE, VIDEO, IN_PERSON
    
    @Column(name = "interview_location")
    private String interviewLocation;
    
    @Column(name = "interview_notes", columnDefinition = "TEXT")
    private String interviewNotes;
    
    @Column(name = "interviewer_id")
    private Long interviewerId;
    
    @Column(name = "feedback", columnDefinition = "TEXT")
    private String feedback;
    
    @Column(name = "rejection_reason")
    private String rejectionReason;
    
    @Column(name = "application_fee_paid", precision = 10, scale = 2)
    private BigDecimal applicationFeePaid = BigDecimal.ZERO;
    
    @Column(name = "payment_reference")
    private String paymentReference;
    
    @Column(name = "viewed_by_employer")
    private Boolean viewedByEmployer = false;
    
    @Column(name = "viewed_at")
    private LocalDateTime viewedAt;
    
    @Column(name = "response_deadline")
    private LocalDateTime responseDeadline;
    
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Constructors
    public JobApplication() {}
    
    public JobApplication(JobPosting jobPosting, User applicant) {
        this.jobPosting = jobPosting;
        this.applicant = applicant;
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public JobPosting getJobPosting() { return jobPosting; }
    public void setJobPosting(JobPosting jobPosting) { this.jobPosting = jobPosting; }
    
    public User getApplicant() { return applicant; }
    public void setApplicant(User applicant) { this.applicant = applicant; }
    
    public String getCoverLetter() { return coverLetter; }
    public void setCoverLetter(String coverLetter) { this.coverLetter = coverLetter; }
    
    public String getResumeUrl() { return resumeUrl; }
    public void setResumeUrl(String resumeUrl) { this.resumeUrl = resumeUrl; }
    
    public String getPortfolioUrl() { return portfolioUrl; }
    public void setPortfolioUrl(String portfolioUrl) { this.portfolioUrl = portfolioUrl; }
    
    public BigDecimal getExpectedSalary() { return expectedSalary; }
    public void setExpectedSalary(BigDecimal expectedSalary) { this.expectedSalary = expectedSalary; }
    
    public LocalDateTime getAvailableStartDate() { return availableStartDate; }
    public void setAvailableStartDate(LocalDateTime availableStartDate) { this.availableStartDate = availableStartDate; }
    
    public Integer getYearsOfExperience() { return yearsOfExperience; }
    public void setYearsOfExperience(Integer yearsOfExperience) { this.yearsOfExperience = yearsOfExperience; }
    
    public String getEducationLevel() { return educationLevel; }
    public void setEducationLevel(String educationLevel) { this.educationLevel = educationLevel; }
    
    public String getSkills() { return skills; }
    public void setSkills(String skills) { this.skills = skills; }
    
    public String getCertifications() { return certifications; }
    public void setCertifications(String certifications) { this.certifications = certifications; }
    
    public String getLanguagesSpoken() { return languagesSpoken; }
    public void setLanguagesSpoken(String languagesSpoken) { this.languagesSpoken = languagesSpoken; }
    
    public String getReferences() { return references; }
    public void setReferences(String references) { this.references = references; }
    
    public String getAdditionalDocuments() { return additionalDocuments; }
    public void setAdditionalDocuments(String additionalDocuments) { this.additionalDocuments = additionalDocuments; }
    
    public String getQuestionnaireAnswers() { return questionnaireAnswers; }
    public void setQuestionnaireAnswers(String questionnaireAnswers) { this.questionnaireAnswers = questionnaireAnswers; }
    
    public ApplicationStatus getStatus() { return status; }
    public void setStatus(ApplicationStatus status) { this.status = status; }
    
    public BigDecimal getAiScore() { return aiScore; }
    public void setAiScore(BigDecimal aiScore) { this.aiScore = aiScore; }
    
    public BigDecimal getAdminScore() { return adminScore; }
    public void setAdminScore(BigDecimal adminScore) { this.adminScore = adminScore; }
    
    public Integer getRankingPosition() { return rankingPosition; }
    public void setRankingPosition(Integer rankingPosition) { this.rankingPosition = rankingPosition; }
    
    public Boolean getIsShortlisted() { return isShortlisted; }
    public void setIsShortlisted(Boolean isShortlisted) { this.isShortlisted = isShortlisted; }
    
    public LocalDateTime getShortlistedAt() { return shortlistedAt; }
    public void setShortlistedAt(LocalDateTime shortlistedAt) { this.shortlistedAt = shortlistedAt; }
    
    public Long getShortlistedBy() { return shortlistedBy; }
    public void setShortlistedBy(Long shortlistedBy) { this.shortlistedBy = shortlistedBy; }
    
    public Boolean getInterviewScheduled() { return interviewScheduled; }
    public void setInterviewScheduled(Boolean interviewScheduled) { this.interviewScheduled = interviewScheduled; }
    
    public LocalDateTime getInterviewDate() { return interviewDate; }
    public void setInterviewDate(LocalDateTime interviewDate) { this.interviewDate = interviewDate; }
    
    public String getInterviewType() { return interviewType; }
    public void setInterviewType(String interviewType) { this.interviewType = interviewType; }
    
    public String getInterviewLocation() { return interviewLocation; }
    public void setInterviewLocation(String interviewLocation) { this.interviewLocation = interviewLocation; }
    
    public String getInterviewNotes() { return interviewNotes; }
    public void setInterviewNotes(String interviewNotes) { this.interviewNotes = interviewNotes; }
    
    public Long getInterviewerId() { return interviewerId; }
    public void setInterviewerId(Long interviewerId) { this.interviewerId = interviewerId; }
    
    public String getFeedback() { return feedback; }
    public void setFeedback(String feedback) { this.feedback = feedback; }
    
    public String getRejectionReason() { return rejectionReason; }
    public void setRejectionReason(String rejectionReason) { this.rejectionReason = rejectionReason; }
    
    public BigDecimal getApplicationFeePaid() { return applicationFeePaid; }
    public void setApplicationFeePaid(BigDecimal applicationFeePaid) { this.applicationFeePaid = applicationFeePaid; }
    
    public String getPaymentReference() { return paymentReference; }
    public void setPaymentReference(String paymentReference) { this.paymentReference = paymentReference; }
    
    public Boolean getViewedByEmployer() { return viewedByEmployer; }
    public void setViewedByEmployer(Boolean viewedByEmployer) { this.viewedByEmployer = viewedByEmployer; }
    
    public LocalDateTime getViewedAt() { return viewedAt; }
    public void setViewedAt(LocalDateTime viewedAt) { this.viewedAt = viewedAt; }
    
    public LocalDateTime getResponseDeadline() { return responseDeadline; }
    public void setResponseDeadline(LocalDateTime responseDeadline) { this.responseDeadline = responseDeadline; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
