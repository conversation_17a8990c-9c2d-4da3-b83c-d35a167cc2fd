import React, { createContext, useContext, useEffect, useState } from 'react';
import io from 'socket.io-client';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from './AuthContext';

const SocketContext = createContext();

export const useSocket = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

export const SocketProvider = ({ children }) => {
  const [socket, setSocket] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState([]);
  const { user, token, isAuthenticated } = useAuth();

  useEffect(() => {
    if (isAuthenticated && user && token) {
      initializeSocket();
    } else {
      disconnectSocket();
    }

    return () => {
      disconnectSocket();
    };
  }, [isAuthenticated, user, token]);

  const initializeSocket = async () => {
    try {
      const SOCKET_URL = __DEV__ 
        ? 'http://localhost:8080' 
        : 'https://api.prochat.co.tz';

      const socketInstance = io(SOCKET_URL, {
        auth: {
          token: token,
          userId: user.id,
        },
        transports: ['websocket'],
        timeout: 20000,
      });

      socketInstance.on('connect', () => {
        console.log('Socket connected:', socketInstance.id);
        setIsConnected(true);
        
        // Join user's personal room
        socketInstance.emit('join-user-room', user.id);
      });

      socketInstance.on('disconnect', () => {
        console.log('Socket disconnected');
        setIsConnected(false);
      });

      socketInstance.on('connect_error', (error) => {
        console.error('Socket connection error:', error);
        setIsConnected(false);
      });

      // Chat events
      socketInstance.on('new-message', (message) => {
        console.log('New message received:', message);
        // Handle new message
      });

      socketInstance.on('message-delivered', (messageId) => {
        console.log('Message delivered:', messageId);
        // Update message status
      });

      socketInstance.on('message-read', (messageId) => {
        console.log('Message read:', messageId);
        // Update message status
      });

      socketInstance.on('user-typing', (data) => {
        console.log('User typing:', data);
        // Show typing indicator
      });

      socketInstance.on('user-stopped-typing', (data) => {
        console.log('User stopped typing:', data);
        // Hide typing indicator
      });

      // Online status events
      socketInstance.on('user-online', (userId) => {
        setOnlineUsers(prev => [...prev.filter(id => id !== userId), userId]);
      });

      socketInstance.on('user-offline', (userId) => {
        setOnlineUsers(prev => prev.filter(id => id !== userId));
      });

      socketInstance.on('online-users', (users) => {
        setOnlineUsers(users);
      });

      // Notification events
      socketInstance.on('new-notification', (notification) => {
        console.log('New notification:', notification);
        // Handle new notification
      });

      // Live stream events
      socketInstance.on('stream-started', (streamData) => {
        console.log('Stream started:', streamData);
        // Handle stream start
      });

      socketInstance.on('stream-ended', (streamId) => {
        console.log('Stream ended:', streamId);
        // Handle stream end
      });

      // ProPay events
      socketInstance.on('transaction-update', (transaction) => {
        console.log('Transaction update:', transaction);
        // Handle transaction update
      });

      socketInstance.on('balance-update', (balance) => {
        console.log('Balance update:', balance);
        // Handle balance update
      });

      setSocket(socketInstance);
    } catch (error) {
      console.error('Error initializing socket:', error);
    }
  };

  const disconnectSocket = () => {
    if (socket) {
      socket.disconnect();
      setSocket(null);
      setIsConnected(false);
      setOnlineUsers([]);
    }
  };

  // Socket methods
  const sendMessage = (conversationId, message) => {
    if (socket && isConnected) {
      socket.emit('send-message', {
        conversationId,
        message,
        senderId: user.id,
      });
    }
  };

  const joinConversation = (conversationId) => {
    if (socket && isConnected) {
      socket.emit('join-conversation', conversationId);
    }
  };

  const leaveConversation = (conversationId) => {
    if (socket && isConnected) {
      socket.emit('leave-conversation', conversationId);
    }
  };

  const markMessageAsRead = (messageId) => {
    if (socket && isConnected) {
      socket.emit('mark-message-read', messageId);
    }
  };

  const startTyping = (conversationId) => {
    if (socket && isConnected) {
      socket.emit('start-typing', {
        conversationId,
        userId: user.id,
      });
    }
  };

  const stopTyping = (conversationId) => {
    if (socket && isConnected) {
      socket.emit('stop-typing', {
        conversationId,
        userId: user.id,
      });
    }
  };

  const updateOnlineStatus = (isOnline) => {
    if (socket && isConnected) {
      socket.emit('update-online-status', {
        userId: user.id,
        isOnline,
      });
    }
  };

  const joinLiveStream = (streamId) => {
    if (socket && isConnected) {
      socket.emit('join-stream', streamId);
    }
  };

  const leaveLiveStream = (streamId) => {
    if (socket && isConnected) {
      socket.emit('leave-stream', streamId);
    }
  };

  const sendStreamComment = (streamId, comment) => {
    if (socket && isConnected) {
      socket.emit('stream-comment', {
        streamId,
        comment,
        userId: user.id,
      });
    }
  };

  const sendStreamGift = (streamId, giftId, quantity) => {
    if (socket && isConnected) {
      socket.emit('stream-gift', {
        streamId,
        giftId,
        quantity,
        senderId: user.id,
      });
    }
  };

  const value = {
    socket,
    isConnected,
    onlineUsers,
    sendMessage,
    joinConversation,
    leaveConversation,
    markMessageAsRead,
    startTyping,
    stopTyping,
    updateOnlineStatus,
    joinLiveStream,
    leaveLiveStream,
    sendStreamComment,
    sendStreamGift,
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};
