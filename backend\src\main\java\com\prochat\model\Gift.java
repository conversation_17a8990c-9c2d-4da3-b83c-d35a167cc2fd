package com.prochat.model;

import jakarta.persistence.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "gifts")
@EntityListeners(AuditingEntityListener.class)
public class Gift {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sender_id", nullable = false)
    private User sender;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "receiver_id", nullable = false)
    private User receiver;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "post_id")
    private Post post; // Gift sent to a post
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "live_stream_id")
    private LiveStream liveStream; // Gift sent during live stream
    
    @Enumerated(EnumType.STRING)
    @Column(name = "gift_type", nullable = false)
    private GiftType giftType;
    
    @Column(name = "gift_name", nullable = false)
    private String giftName;
    
    @Column(name = "gift_icon_url")
    private String giftIconUrl;
    
    @Column(name = "gift_animation_url")
    private String giftAnimationUrl;
    
    @Column(name = "value", precision = 10, scale = 2, nullable = false)
    private BigDecimal value;
    
    @Column(name = "quantity")
    private Integer quantity = 1;
    
    @Column(name = "total_amount", precision = 15, scale = 2)
    private BigDecimal totalAmount;
    
    @Column(name = "message", columnDefinition = "TEXT")
    private String message;
    
    @Column(name = "is_anonymous")
    private Boolean isAnonymous = false;
    
    @Column(name = "is_public")
    private Boolean isPublic = true;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private GiftStatus status = GiftStatus.SENT;
    
    @Column(name = "transaction_reference")
    private String transactionReference;
    
    @Column(name = "platform_commission", precision = 10, scale = 4)
    private BigDecimal platformCommission = new BigDecimal("0.10"); // 10% platform fee
    
    @Column(name = "commission_amount", precision = 15, scale = 2)
    private BigDecimal commissionAmount;
    
    @Column(name = "receiver_amount", precision = 15, scale = 2)
    private BigDecimal receiverAmount;
    
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Constructors
    public Gift() {}
    
    public Gift(User sender, User receiver, GiftType giftType, String giftName, BigDecimal value) {
        this.sender = sender;
        this.receiver = receiver;
        this.giftType = giftType;
        this.giftName = giftName;
        this.value = value;
        this.totalAmount = value.multiply(BigDecimal.valueOf(this.quantity));
        this.commissionAmount = this.totalAmount.multiply(this.platformCommission);
        this.receiverAmount = this.totalAmount.subtract(this.commissionAmount);
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public User getSender() { return sender; }
    public void setSender(User sender) { this.sender = sender; }
    
    public User getReceiver() { return receiver; }
    public void setReceiver(User receiver) { this.receiver = receiver; }
    
    public Post getPost() { return post; }
    public void setPost(Post post) { this.post = post; }
    
    public LiveStream getLiveStream() { return liveStream; }
    public void setLiveStream(LiveStream liveStream) { this.liveStream = liveStream; }
    
    public GiftType getGiftType() { return giftType; }
    public void setGiftType(GiftType giftType) { this.giftType = giftType; }
    
    public String getGiftName() { return giftName; }
    public void setGiftName(String giftName) { this.giftName = giftName; }
    
    public String getGiftIconUrl() { return giftIconUrl; }
    public void setGiftIconUrl(String giftIconUrl) { this.giftIconUrl = giftIconUrl; }
    
    public String getGiftAnimationUrl() { return giftAnimationUrl; }
    public void setGiftAnimationUrl(String giftAnimationUrl) { this.giftAnimationUrl = giftAnimationUrl; }
    
    public BigDecimal getValue() { return value; }
    public void setValue(BigDecimal value) { 
        this.value = value;
        this.totalAmount = value.multiply(BigDecimal.valueOf(this.quantity));
        this.commissionAmount = this.totalAmount.multiply(this.platformCommission);
        this.receiverAmount = this.totalAmount.subtract(this.commissionAmount);
    }
    
    public Integer getQuantity() { return quantity; }
    public void setQuantity(Integer quantity) { 
        this.quantity = quantity;
        this.totalAmount = this.value.multiply(BigDecimal.valueOf(quantity));
        this.commissionAmount = this.totalAmount.multiply(this.platformCommission);
        this.receiverAmount = this.totalAmount.subtract(this.commissionAmount);
    }
    
    public BigDecimal getTotalAmount() { return totalAmount; }
    public void setTotalAmount(BigDecimal totalAmount) { this.totalAmount = totalAmount; }
    
    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }
    
    public Boolean getIsAnonymous() { return isAnonymous; }
    public void setIsAnonymous(Boolean isAnonymous) { this.isAnonymous = isAnonymous; }
    
    public Boolean getIsPublic() { return isPublic; }
    public void setIsPublic(Boolean isPublic) { this.isPublic = isPublic; }
    
    public GiftStatus getStatus() { return status; }
    public void setStatus(GiftStatus status) { this.status = status; }
    
    public String getTransactionReference() { return transactionReference; }
    public void setTransactionReference(String transactionReference) { this.transactionReference = transactionReference; }
    
    public BigDecimal getPlatformCommission() { return platformCommission; }
    public void setPlatformCommission(BigDecimal platformCommission) { 
        this.platformCommission = platformCommission;
        if (this.totalAmount != null) {
            this.commissionAmount = this.totalAmount.multiply(platformCommission);
            this.receiverAmount = this.totalAmount.subtract(this.commissionAmount);
        }
    }
    
    public BigDecimal getCommissionAmount() { return commissionAmount; }
    public void setCommissionAmount(BigDecimal commissionAmount) { this.commissionAmount = commissionAmount; }
    
    public BigDecimal getReceiverAmount() { return receiverAmount; }
    public void setReceiverAmount(BigDecimal receiverAmount) { this.receiverAmount = receiverAmount; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
