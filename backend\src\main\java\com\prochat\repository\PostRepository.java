package com.prochat.repository;

import com.prochat.model.Post;
import com.prochat.model.PostStatus;
import com.prochat.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface PostRepository extends JpaRepository<Post, Long> {
    
    Page<Post> findByStatusOrderByCreatedAtDesc(PostStatus status, Pageable pageable);
    
    Page<Post> findByUserAndStatusOrderByCreatedAtDesc(User user, PostStatus status, Pageable pageable);
    
    List<Post> findByUserOrderByCreatedAtDesc(User user);
    
    @Query("SELECT p FROM Post p WHERE p.status = :status AND p.createdAt >= :since ORDER BY p.createdAt DESC")
    List<Post> findRecentPosts(@Param("status") PostStatus status, @Param("since") LocalDateTime since);
    
    @Query("SELECT p FROM Post p WHERE p.status = 'ACTIVE' AND " +
           "(p.content LIKE %:searchTerm% OR p.user.username LIKE %:searchTerm% OR p.user.firstName LIKE %:searchTerm%)")
    Page<Post> searchPosts(@Param("searchTerm") String searchTerm, Pageable pageable);
    
    @Query("SELECT p FROM Post p WHERE p.status = 'ACTIVE' ORDER BY p.likesCount DESC")
    Page<Post> findTrendingPosts(Pageable pageable);
    
    @Query("SELECT COUNT(p) FROM Post p WHERE p.user = :user AND p.status = 'ACTIVE'")
    Long countActivePostsByUser(@Param("user") User user);
    
    @Query("SELECT COUNT(p) FROM Post p WHERE p.status = :status AND p.createdAt >= :since")
    Long countPostsByStatusSince(@Param("status") PostStatus status, @Param("since") LocalDateTime since);
}
