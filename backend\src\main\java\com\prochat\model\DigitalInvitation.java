package com.prochat.model;

import jakarta.persistence.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "digital_invitations")
@EntityListeners(AuditingEntityListener.class)
public class DigitalInvitation {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "creator_id", nullable = false)
    private User creator;
    
    @Column(name = "title", nullable = false)
    private String title;
    
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "invitation_type")
    private InvitationType invitationType;
    
    @Column(name = "template_id")
    private String templateId;
    
    @Column(name = "background_image_url")
    private String backgroundImageUrl;
    
    @Column(name = "background_color")
    private String backgroundColor = "#FFFFFF";
    
    @Column(name = "text_color")
    private String textColor = "#000000";
    
    @Column(name = "accent_color")
    private String accentColor = "#007AFF";
    
    @Column(name = "font_family")
    private String fontFamily = "Arial";
    
    @Column(name = "font_size")
    private Integer fontSize = 16;
    
    @ElementCollection
    @CollectionTable(name = "invitation_media", joinColumns = @JoinColumn(name = "invitation_id"))
    @Column(name = "media_url")
    private List<String> mediaUrls;
    
    @Column(name = "event_name")
    private String eventName;
    
    @Column(name = "event_date")
    private LocalDateTime eventDate;
    
    @Column(name = "event_time")
    private String eventTime;
    
    @Column(name = "event_location")
    private String eventLocation;
    
    @Column(name = "event_address")
    private String eventAddress;
    
    @Column(name = "dress_code")
    private String dressCode;
    
    @Column(name = "special_instructions", columnDefinition = "TEXT")
    private String specialInstructions;
    
    @Column(name = "rsvp_required")
    private Boolean rsvpRequired = false;
    
    @Column(name = "rsvp_deadline")
    private LocalDateTime rsvpDeadline;
    
    @Column(name = "rsvp_contact")
    private String rsvpContact;
    
    @Column(name = "max_guests")
    private Integer maxGuests;
    
    @Column(name = "plus_one_allowed")
    private Boolean plusOneAllowed = false;
    
    @Column(name = "gift_registry_info", columnDefinition = "TEXT")
    private String giftRegistryInfo;
    
    @Column(name = "custom_message", columnDefinition = "TEXT")
    private String customMessage;
    
    @Column(name = "host_name")
    private String hostName;
    
    @Column(name = "host_contact")
    private String hostContact;
    
    @Column(name = "invitation_url", unique = true)
    private String invitationUrl;
    
    @Column(name = "qr_code_url")
    private String qrCodeUrl;
    
    @Column(name = "is_public")
    private Boolean isPublic = false;
    
    @Column(name = "password_protected")
    private Boolean passwordProtected = false;
    
    @Column(name = "access_password")
    private String accessPassword;
    
    @Column(name = "views_count")
    private Long viewsCount = 0L;
    
    @Column(name = "rsvp_yes_count")
    private Integer rsvpYesCount = 0;
    
    @Column(name = "rsvp_no_count")
    private Integer rsvpNoCount = 0;
    
    @Column(name = "rsvp_maybe_count")
    private Integer rsvpMaybeCount = 0;
    
    @Column(name = "total_sent")
    private Integer totalSent = 0;
    
    @Column(name = "sms_sent")
    private Integer smsSent = 0;
    
    @Column(name = "email_sent")
    private Integer emailSent = 0;
    
    @Column(name = "whatsapp_sent")
    private Integer whatsappSent = 0;
    
    @Column(name = "creation_fee", precision = 10, scale = 2)
    private BigDecimal creationFee = BigDecimal.ZERO;
    
    @Column(name = "sms_fee_per_message", precision = 5, scale = 2)
    private BigDecimal smsFeePerMessage = new BigDecimal("50.00"); // 50 TSH per SMS
    
    @Column(name = "total_fees_paid", precision = 15, scale = 2)
    private BigDecimal totalFeesPaid = BigDecimal.ZERO;
    
    @Column(name = "payment_reference")
    private String paymentReference;
    
    @Column(name = "expires_at")
    private LocalDateTime expiresAt;
    
    @Column(name = "is_active")
    private Boolean isActive = true;
    
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Constructors
    public DigitalInvitation() {}
    
    public DigitalInvitation(User creator, String title, InvitationType invitationType) {
        this.creator = creator;
        this.title = title;
        this.invitationType = invitationType;
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public User getCreator() { return creator; }
    public void setCreator(User creator) { this.creator = creator; }
    
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public InvitationType getInvitationType() { return invitationType; }
    public void setInvitationType(InvitationType invitationType) { this.invitationType = invitationType; }
    
    public String getTemplateId() { return templateId; }
    public void setTemplateId(String templateId) { this.templateId = templateId; }
    
    public String getBackgroundImageUrl() { return backgroundImageUrl; }
    public void setBackgroundImageUrl(String backgroundImageUrl) { this.backgroundImageUrl = backgroundImageUrl; }
    
    public String getBackgroundColor() { return backgroundColor; }
    public void setBackgroundColor(String backgroundColor) { this.backgroundColor = backgroundColor; }
    
    public String getTextColor() { return textColor; }
    public void setTextColor(String textColor) { this.textColor = textColor; }
    
    public String getAccentColor() { return accentColor; }
    public void setAccentColor(String accentColor) { this.accentColor = accentColor; }
    
    public String getFontFamily() { return fontFamily; }
    public void setFontFamily(String fontFamily) { this.fontFamily = fontFamily; }
    
    public Integer getFontSize() { return fontSize; }
    public void setFontSize(Integer fontSize) { this.fontSize = fontSize; }
    
    public List<String> getMediaUrls() { return mediaUrls; }
    public void setMediaUrls(List<String> mediaUrls) { this.mediaUrls = mediaUrls; }
    
    public String getEventName() { return eventName; }
    public void setEventName(String eventName) { this.eventName = eventName; }
    
    public LocalDateTime getEventDate() { return eventDate; }
    public void setEventDate(LocalDateTime eventDate) { this.eventDate = eventDate; }
    
    public String getEventTime() { return eventTime; }
    public void setEventTime(String eventTime) { this.eventTime = eventTime; }
    
    public String getEventLocation() { return eventLocation; }
    public void setEventLocation(String eventLocation) { this.eventLocation = eventLocation; }
    
    public String getEventAddress() { return eventAddress; }
    public void setEventAddress(String eventAddress) { this.eventAddress = eventAddress; }
    
    public String getDressCode() { return dressCode; }
    public void setDressCode(String dressCode) { this.dressCode = dressCode; }
    
    public String getSpecialInstructions() { return specialInstructions; }
    public void setSpecialInstructions(String specialInstructions) { this.specialInstructions = specialInstructions; }
    
    public Boolean getRsvpRequired() { return rsvpRequired; }
    public void setRsvpRequired(Boolean rsvpRequired) { this.rsvpRequired = rsvpRequired; }
    
    public LocalDateTime getRsvpDeadline() { return rsvpDeadline; }
    public void setRsvpDeadline(LocalDateTime rsvpDeadline) { this.rsvpDeadline = rsvpDeadline; }
    
    public String getRsvpContact() { return rsvpContact; }
    public void setRsvpContact(String rsvpContact) { this.rsvpContact = rsvpContact; }
    
    public Integer getMaxGuests() { return maxGuests; }
    public void setMaxGuests(Integer maxGuests) { this.maxGuests = maxGuests; }
    
    public Boolean getPlusOneAllowed() { return plusOneAllowed; }
    public void setPlusOneAllowed(Boolean plusOneAllowed) { this.plusOneAllowed = plusOneAllowed; }
    
    public String getGiftRegistryInfo() { return giftRegistryInfo; }
    public void setGiftRegistryInfo(String giftRegistryInfo) { this.giftRegistryInfo = giftRegistryInfo; }
    
    public String getCustomMessage() { return customMessage; }
    public void setCustomMessage(String customMessage) { this.customMessage = customMessage; }
    
    public String getHostName() { return hostName; }
    public void setHostName(String hostName) { this.hostName = hostName; }
    
    public String getHostContact() { return hostContact; }
    public void setHostContact(String hostContact) { this.hostContact = hostContact; }
    
    public String getInvitationUrl() { return invitationUrl; }
    public void setInvitationUrl(String invitationUrl) { this.invitationUrl = invitationUrl; }
    
    public String getQrCodeUrl() { return qrCodeUrl; }
    public void setQrCodeUrl(String qrCodeUrl) { this.qrCodeUrl = qrCodeUrl; }
    
    public Boolean getIsPublic() { return isPublic; }
    public void setIsPublic(Boolean isPublic) { this.isPublic = isPublic; }
    
    public Boolean getPasswordProtected() { return passwordProtected; }
    public void setPasswordProtected(Boolean passwordProtected) { this.passwordProtected = passwordProtected; }
    
    public String getAccessPassword() { return accessPassword; }
    public void setAccessPassword(String accessPassword) { this.accessPassword = accessPassword; }
    
    public Long getViewsCount() { return viewsCount; }
    public void setViewsCount(Long viewsCount) { this.viewsCount = viewsCount; }
    
    public Integer getRsvpYesCount() { return rsvpYesCount; }
    public void setRsvpYesCount(Integer rsvpYesCount) { this.rsvpYesCount = rsvpYesCount; }
    
    public Integer getRsvpNoCount() { return rsvpNoCount; }
    public void setRsvpNoCount(Integer rsvpNoCount) { this.rsvpNoCount = rsvpNoCount; }
    
    public Integer getRsvpMaybeCount() { return rsvpMaybeCount; }
    public void setRsvpMaybeCount(Integer rsvpMaybeCount) { this.rsvpMaybeCount = rsvpMaybeCount; }
    
    public Integer getTotalSent() { return totalSent; }
    public void setTotalSent(Integer totalSent) { this.totalSent = totalSent; }
    
    public Integer getSmsSent() { return smsSent; }
    public void setSmsSent(Integer smsSent) { this.smsSent = smsSent; }
    
    public Integer getEmailSent() { return emailSent; }
    public void setEmailSent(Integer emailSent) { this.emailSent = emailSent; }
    
    public Integer getWhatsappSent() { return whatsappSent; }
    public void setWhatsappSent(Integer whatsappSent) { this.whatsappSent = whatsappSent; }
    
    public BigDecimal getCreationFee() { return creationFee; }
    public void setCreationFee(BigDecimal creationFee) { this.creationFee = creationFee; }
    
    public BigDecimal getSmsFeePerMessage() { return smsFeePerMessage; }
    public void setSmsFeePerMessage(BigDecimal smsFeePerMessage) { this.smsFeePerMessage = smsFeePerMessage; }
    
    public BigDecimal getTotalFeesPaid() { return totalFeesPaid; }
    public void setTotalFeesPaid(BigDecimal totalFeesPaid) { this.totalFeesPaid = totalFeesPaid; }
    
    public String getPaymentReference() { return paymentReference; }
    public void setPaymentReference(String paymentReference) { this.paymentReference = paymentReference; }
    
    public LocalDateTime getExpiresAt() { return expiresAt; }
    public void setExpiresAt(LocalDateTime expiresAt) { this.expiresAt = expiresAt; }
    
    public Boolean getIsActive() { return isActive; }
    public void setIsActive(Boolean isActive) { this.isActive = isActive; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
