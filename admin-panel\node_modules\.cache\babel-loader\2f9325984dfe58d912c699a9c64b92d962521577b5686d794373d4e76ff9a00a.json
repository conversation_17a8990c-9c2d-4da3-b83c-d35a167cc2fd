{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport default function requirePropFactory(componentNameInError, Component) {\n  if (process.env.NODE_ENV === 'production') {\n    return () => null;\n  }\n\n  // eslint-disable-next-line react/forbid-foreign-prop-types\n  const prevPropTypes = Component ? _extends({}, Component.propTypes) : null;\n  const requireProp = requiredProp => (props, propName, componentName, location, propFullName, ...args) => {\n    const propFullNameSafe = propFullName || propName;\n    const defaultTypeChecker = prevPropTypes == null ? void 0 : prevPropTypes[propFullNameSafe];\n    if (defaultTypeChecker) {\n      const typeCheckerResult = defaultTypeChecker(props, propName, componentName, location, propFullName, ...args);\n      if (typeCheckerResult) {\n        return typeCheckerResult;\n      }\n    }\n    if (typeof props[propName] !== 'undefined' && !props[requiredProp]) {\n      return new Error(`The prop \\`${propFullNameSafe}\\` of ` + `\\`${componentNameInError}\\` can only be used together with the \\`${requiredProp}\\` prop.`);\n    }\n    return null;\n  };\n  return requireProp;\n}", "map": {"version": 3, "names": ["_extends", "requirePropFactory", "componentNameInError", "Component", "process", "env", "NODE_ENV", "prevPropTypes", "propTypes", "requireProp", "requiredProp", "props", "propName", "componentName", "location", "prop<PERSON><PERSON><PERSON><PERSON>", "args", "propFullNameSafe", "defaultTypeChecker", "typeCheckerResult", "Error"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/@mui/utils/esm/requirePropFactory/requirePropFactory.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport default function requirePropFactory(componentNameInError, Component) {\n  if (process.env.NODE_ENV === 'production') {\n    return () => null;\n  }\n\n  // eslint-disable-next-line react/forbid-foreign-prop-types\n  const prevPropTypes = Component ? _extends({}, Component.propTypes) : null;\n  const requireProp = requiredProp => (props, propName, componentName, location, propFullName, ...args) => {\n    const propFullNameSafe = propFullName || propName;\n    const defaultTypeChecker = prevPropTypes == null ? void 0 : prevPropTypes[propFullNameSafe];\n    if (defaultTypeChecker) {\n      const typeCheckerResult = defaultTypeChecker(props, propName, componentName, location, propFullName, ...args);\n      if (typeCheckerResult) {\n        return typeCheckerResult;\n      }\n    }\n    if (typeof props[propName] !== 'undefined' && !props[requiredProp]) {\n      return new Error(`The prop \\`${propFullNameSafe}\\` of ` + `\\`${componentNameInError}\\` can only be used together with the \\`${requiredProp}\\` prop.`);\n    }\n    return null;\n  };\n  return requireProp;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,eAAe,SAASC,kBAAkBA,CAACC,oBAAoB,EAAEC,SAAS,EAAE;EAC1E,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,OAAO,MAAM,IAAI;EACnB;;EAEA;EACA,MAAMC,aAAa,GAAGJ,SAAS,GAAGH,QAAQ,CAAC,CAAC,CAAC,EAAEG,SAAS,CAACK,SAAS,CAAC,GAAG,IAAI;EAC1E,MAAMC,WAAW,GAAGC,YAAY,IAAI,CAACC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAE,GAAGC,IAAI,KAAK;IACvG,MAAMC,gBAAgB,GAAGF,YAAY,IAAIH,QAAQ;IACjD,MAAMM,kBAAkB,GAAGX,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACU,gBAAgB,CAAC;IAC3F,IAAIC,kBAAkB,EAAE;MACtB,MAAMC,iBAAiB,GAAGD,kBAAkB,CAACP,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAE,GAAGC,IAAI,CAAC;MAC7G,IAAIG,iBAAiB,EAAE;QACrB,OAAOA,iBAAiB;MAC1B;IACF;IACA,IAAI,OAAOR,KAAK,CAACC,QAAQ,CAAC,KAAK,WAAW,IAAI,CAACD,KAAK,CAACD,YAAY,CAAC,EAAE;MAClE,OAAO,IAAIU,KAAK,CAAC,cAAcH,gBAAgB,QAAQ,GAAG,KAAKf,oBAAoB,2CAA2CQ,YAAY,UAAU,CAAC;IACvJ;IACA,OAAO,IAAI;EACb,CAAC;EACD,OAAOD,WAAW;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}