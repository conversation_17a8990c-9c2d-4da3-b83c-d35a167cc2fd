import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  Share,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors, typography, spacing } from '../../theme/theme';
import { newsAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

export default function NewsDetailsScreen({ navigation, route }) {
  const { newsId } = route.params;
  const { user } = useAuth();
  const [news, setNews] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isBookmarked, setIsBookmarked] = useState(false);

  useEffect(() => {
    loadNewsDetails();
  }, [newsId]);

  const loadNewsDetails = async () => {
    try {
      const response = await newsAPI.getNewsById(newsId);
      if (response.success) {
        setNews(response.data);
        setIsBookmarked(response.data.isBookmarked || false);
      }
    } catch (error) {
      console.error('Error loading news:', error);
      Alert.alert('Hitilafu', 'Imeshindwa kupakia habari');
    } finally {
      setLoading(false);
    }
  };

  const handleShare = async () => {
    try {
      await Share.share({
        message: `${news.title}\n\n${news.summary}\n\nSoma zaidi kwenye ProChat app!`,
        url: news.shareUrl || '',
        title: news.title,
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  const handleBookmark = async () => {
    try {
      const response = await newsAPI.toggleBookmark(newsId);
      if (response.success) {
        setIsBookmarked(!isBookmarked);
      }
    } catch (error) {
      console.error('Error bookmarking:', error);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('sw-TZ', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case 'POLITICS': return colors.error;
      case 'BUSINESS': return colors.success;
      case 'SPORTS': return colors.warning;
      case 'TECHNOLOGY': return colors.info;
      case 'ENTERTAINMENT': return colors.secondary;
      default: return colors.primary;
    }
  };

  const getCategoryName = (category) => {
    switch (category) {
      case 'POLITICS': return 'Siasa';
      case 'BUSINESS': return 'Biashara';
      case 'SPORTS': return 'Michezo';
      case 'TECHNOLOGY': return 'Teknolojia';
      case 'ENTERTAINMENT': return 'Burudani';
      case 'HEALTH': return 'Afya';
      case 'EDUCATION': return 'Elimu';
      default: return category;
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Inapakia habari...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!news) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text>Habari haipatikani</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.actionButton} onPress={handleBookmark}>
            <Icon 
              name={isBookmarked ? "bookmark" : "bookmark-border"} 
              size={24} 
              color={isBookmarked ? colors.primary : colors.text} 
            />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton} onPress={handleShare}>
            <Icon name="share" size={24} color={colors.text} />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.content}>
        {/* Featured Image */}
        {news.imageUrl && (
          <Image source={{ uri: news.imageUrl }} style={styles.featuredImage} />
        )}

        {/* Article Content */}
        <View style={styles.articleContainer}>
          {/* Category Badge */}
          <View style={[
            styles.categoryBadge,
            { backgroundColor: getCategoryColor(news.category) + '20' }
          ]}>
            <Text style={[
              styles.categoryText,
              { color: getCategoryColor(news.category) }
            ]}>
              {getCategoryName(news.category)}
            </Text>
          </View>

          {/* Title */}
          <Text style={styles.title}>{news.title}</Text>

          {/* Meta Info */}
          <View style={styles.metaInfo}>
            <View style={styles.authorInfo}>
              <Icon name="person" size={16} color={colors.textSecondary} />
              <Text style={styles.authorName}>{news.author || 'ProChat News'}</Text>
            </View>
            
            <View style={styles.dateInfo}>
              <Icon name="schedule" size={16} color={colors.textSecondary} />
              <Text style={styles.publishDate}>{formatDate(news.publishedAt)}</Text>
            </View>
          </View>

          {/* Summary */}
          {news.summary && (
            <View style={styles.summaryContainer}>
              <Text style={styles.summary}>{news.summary}</Text>
            </View>
          )}

          {/* Content */}
          <View style={styles.contentContainer}>
            <Text style={styles.articleContent}>{news.content}</Text>
          </View>

          {/* Tags */}
          {news.tags && news.tags.length > 0 && (
            <View style={styles.tagsContainer}>
              <Text style={styles.tagsTitle}>Vipengele:</Text>
              <View style={styles.tags}>
                {news.tags.map((tag, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>#{tag}</Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* Source */}
          {news.source && (
            <View style={styles.sourceContainer}>
              <Text style={styles.sourceLabel}>Chanzo:</Text>
              <Text style={styles.sourceText}>{news.source}</Text>
            </View>
          )}

          {/* Related Articles */}
          {news.relatedArticles && news.relatedArticles.length > 0 && (
            <View style={styles.relatedContainer}>
              <Text style={styles.relatedTitle}>Habari Zinazohusiana</Text>
              {news.relatedArticles.map((article, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.relatedItem}
                  onPress={() => navigation.push('NewsDetails', { newsId: article.id })}
                >
                  <Image source={{ uri: article.imageUrl }} style={styles.relatedImage} />
                  <View style={styles.relatedContent}>
                    <Text style={styles.relatedItemTitle} numberOfLines={2}>
                      {article.title}
                    </Text>
                    <Text style={styles.relatedDate}>
                      {formatDate(article.publishedAt)}
                    </Text>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>
      </ScrollView>

      {/* Bottom Actions */}
      <View style={styles.bottomActions}>
        <TouchableOpacity style={styles.bottomAction} onPress={handleBookmark}>
          <Icon 
            name={isBookmarked ? "bookmark" : "bookmark-border"} 
            size={20} 
            color={isBookmarked ? colors.primary : colors.textSecondary} 
          />
          <Text style={[
            styles.bottomActionText,
            isBookmarked && { color: colors.primary }
          ]}>
            {isBookmarked ? 'Imehifadhiwa' : 'Hifadhi'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.bottomAction} onPress={handleShare}>
          <Icon name="share" size={20} color={colors.textSecondary} />
          <Text style={styles.bottomActionText}>Shiriki</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.bottomAction}>
          <Icon name="comment" size={20} color={colors.textSecondary} />
          <Text style={styles.bottomActionText}>Maoni</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.sm,
  },
  headerActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  actionButton: {
    padding: spacing.sm,
  },
  content: {
    flex: 1,
  },
  featuredImage: {
    width: '100%',
    height: 250,
    resizeMode: 'cover',
  },
  articleContainer: {
    padding: spacing.lg,
    backgroundColor: colors.white,
  },
  categoryBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    marginBottom: spacing.md,
  },
  categoryText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  title: {
    fontSize: typography.fontSize.xxl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
    lineHeight: 32,
    marginBottom: spacing.md,
  },
  metaInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.lg,
  },
  authorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  authorName: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginLeft: spacing.xs,
  },
  dateInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  publishDate: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginLeft: spacing.xs,
  },
  summaryContainer: {
    backgroundColor: colors.surface,
    padding: spacing.md,
    borderRadius: 8,
    marginBottom: spacing.lg,
  },
  summary: {
    fontSize: typography.fontSize.md,
    color: colors.text,
    lineHeight: 24,
    fontStyle: 'italic',
  },
  contentContainer: {
    marginBottom: spacing.lg,
  },
  articleContent: {
    fontSize: typography.fontSize.md,
    color: colors.text,
    lineHeight: 26,
  },
  tagsContainer: {
    marginBottom: spacing.lg,
  },
  tagsTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  tags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  tag: {
    backgroundColor: colors.primary + '20',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  tagText: {
    fontSize: typography.fontSize.sm,
    color: colors.primary,
  },
  sourceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  sourceLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginRight: spacing.sm,
  },
  sourceText: {
    fontSize: typography.fontSize.sm,
    color: colors.text,
    fontWeight: typography.fontWeight.medium,
  },
  relatedContainer: {
    marginTop: spacing.lg,
    paddingTop: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  relatedTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  relatedItem: {
    flexDirection: 'row',
    marginBottom: spacing.md,
    paddingBottom: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  relatedImage: {
    width: 80,
    height: 60,
    borderRadius: 8,
    marginRight: spacing.md,
  },
  relatedContent: {
    flex: 1,
  },
  relatedItemTitle: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
    lineHeight: 20,
    marginBottom: spacing.xs,
  },
  relatedDate: {
    fontSize: typography.fontSize.xs,
    color: colors.textSecondary,
  },
  bottomActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    backgroundColor: colors.white,
  },
  bottomAction: {
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  bottomActionText: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
});
