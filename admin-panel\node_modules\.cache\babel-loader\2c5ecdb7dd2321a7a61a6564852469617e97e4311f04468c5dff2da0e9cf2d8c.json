{"ast": null, "code": "/** Used as references for various `Number` constants. */\nvar MAX_SAFE_INTEGER = 9007199254740991;\n\n/** Used to detect unsigned integer values. */\nvar reIsUint = /^(?:0|[1-9]\\d*)$/;\n\n/**\n * Checks if `value` is a valid array-like index.\n *\n * @private\n * @param {*} value The value to check.\n * @param {number} [length=MAX_SAFE_INTEGER] The upper bounds of a valid index.\n * @returns {boolean} Returns `true` if `value` is a valid index, else `false`.\n */\nfunction isIndex(value, length) {\n  var type = typeof value;\n  length = length == null ? MAX_SAFE_INTEGER : length;\n  return !!length && (type == 'number' || type != 'symbol' && reIsUint.test(value)) && value > -1 && value % 1 == 0 && value < length;\n}\nmodule.exports = isIndex;", "map": {"version": 3, "names": ["MAX_SAFE_INTEGER", "reIsUint", "isIndex", "value", "length", "type", "test", "module", "exports"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/lodash/_isIndex.js"], "sourcesContent": ["/** Used as references for various `Number` constants. */\nvar MAX_SAFE_INTEGER = 9007199254740991;\n\n/** Used to detect unsigned integer values. */\nvar reIsUint = /^(?:0|[1-9]\\d*)$/;\n\n/**\n * Checks if `value` is a valid array-like index.\n *\n * @private\n * @param {*} value The value to check.\n * @param {number} [length=MAX_SAFE_INTEGER] The upper bounds of a valid index.\n * @returns {boolean} Returns `true` if `value` is a valid index, else `false`.\n */\nfunction isIndex(value, length) {\n  var type = typeof value;\n  length = length == null ? MAX_SAFE_INTEGER : length;\n\n  return !!length &&\n    (type == 'number' ||\n      (type != 'symbol' && reIsUint.test(value))) &&\n        (value > -1 && value % 1 == 0 && value < length);\n}\n\nmodule.exports = isIndex;\n"], "mappings": "AAAA;AACA,IAAIA,gBAAgB,GAAG,gBAAgB;;AAEvC;AACA,IAAIC,QAAQ,GAAG,kBAAkB;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC9B,IAAIC,IAAI,GAAG,OAAOF,KAAK;EACvBC,MAAM,GAAGA,MAAM,IAAI,IAAI,GAAGJ,gBAAgB,GAAGI,MAAM;EAEnD,OAAO,CAAC,CAACA,MAAM,KACZC,IAAI,IAAI,QAAQ,IACdA,IAAI,IAAI,QAAQ,IAAIJ,QAAQ,CAACK,IAAI,CAACH,KAAK,CAAE,CAAC,IACxCA,KAAK,GAAG,CAAC,CAAC,IAAIA,KAAK,GAAG,CAAC,IAAI,CAAC,IAAIA,KAAK,GAAGC,MAAO;AACxD;AAEAG,MAAM,CAACC,OAAO,GAAGN,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}