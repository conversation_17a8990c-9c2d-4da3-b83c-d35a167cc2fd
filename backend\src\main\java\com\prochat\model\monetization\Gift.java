package com.prochat.model.monetization;

import com.prochat.model.User;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "gifts")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Gift {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "name", nullable = false)
    private String name;
    
    @Column(name = "display_name", nullable = false)
    private String displayName;
    
    @Column(name = "icon_url")
    private String iconUrl;
    
    @Column(name = "animation_url")
    private String animationUrl;
    
    @Column(name = "price", nullable = false, precision = 10, scale = 2)
    private BigDecimal price;
    
    @Column(name = "currency", nullable = false)
    private String currency = "TZS";
    
    @Column(name = "recipient_percentage", nullable = false, precision = 5, scale = 2)
    private BigDecimal recipientPercentage = new BigDecimal("80.00"); // 80% kwa mpokeaji
    
    @Column(name = "platform_percentage", nullable = false, precision = 5, scale = 2)
    private BigDecimal platformPercentage = new BigDecimal("20.00"); // 20% kwa platform
    
    @Enumerated(EnumType.STRING)
    @Column(name = "category", nullable = false)
    private GiftCategory category;
    
    @Column(name = "description")
    private String description;
    
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;
    
    @Column(name = "is_premium", nullable = false)
    private Boolean isPremium = false;
    
    @Column(name = "minimum_level")
    private Integer minimumLevel = 1;
    
    @Column(name = "sort_order")
    private Integer sortOrder = 0;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @Column(name = "created_by")
    private Long createdBy;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    public enum GiftCategory {
        BASIC("Zawadi za Msingi", "coin, heart, thumbs_up"),
        FLOWERS("Maua", "rose, tulip, sunflower"),
        JEWELRY("Vito", "diamond, ring, necklace"),
        VEHICLES("Magari", "car, bike, plane"),
        ANIMALS("Wanyamapori", "lion, elephant, eagle"),
        SPECIAL("Maalum", "crown, trophy, star"),
        SEASONAL("Msimu", "christmas_tree, fireworks, cake");
        
        private final String displayName;
        private final String examples;
        
        GiftCategory(String displayName, String examples) {
            this.displayName = displayName;
            this.examples = examples;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public String getExamples() {
            return examples;
        }
    }
}

@Entity
@Table(name = "gift_transactions")
@Data
@NoArgsConstructor
@AllArgsConstructor
class GiftTransaction {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "gift_id", nullable = false)
    private Gift gift;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sender_id", nullable = false)
    private User sender;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "recipient_id", nullable = false)
    private User recipient;
    
    @Column(name = "quantity", nullable = false)
    private Integer quantity = 1;
    
    @Column(name = "total_amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal totalAmount;
    
    @Column(name = "recipient_amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal recipientAmount;
    
    @Column(name = "platform_amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal platformAmount;
    
    @Column(name = "source_type")
    private String sourceType; // POST, COMMENT, LIVE_STREAM, etc.
    
    @Column(name = "source_id")
    private Long sourceId;
    
    @Column(name = "message")
    private String message;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private TransactionStatus status = TransactionStatus.COMPLETED;
    
    @Column(name = "transaction_reference")
    private String transactionReference;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
    
    public enum TransactionStatus {
        PENDING("Inasubiri"),
        COMPLETED("Imekamilika"),
        FAILED("Imeshindwa"),
        REFUNDED("Imerudishwa");
        
        private final String displayName;
        
        TransactionStatus(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
}
