import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Image,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import * as ImagePicker from 'expo-image-picker';
import { colors, typography, spacing } from '../../theme/theme';
import { postsAPI, uploadAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

export default function CreatePostScreen({ navigation }) {
  const { user } = useAuth();
  const [content, setContent] = useState('');
  const [selectedImages, setSelectedImages] = useState([]);
  const [selectedVideo, setSelectedVideo] = useState(null);
  const [isPosting, setIsPosting] = useState(false);
  const [privacy, setPrivacy] = useState('PUBLIC'); // PUBLIC, FRIENDS, PRIVATE

  const handleImagePicker = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert('Ruhusa', 'Tunahitaji ruhusa ya kufikia picha zako');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsMultipleSelection: true,
        quality: 0.8,
        aspect: [1, 1],
      });

      if (!result.canceled) {
        setSelectedImages([...selectedImages, ...result.assets]);
      }
    } catch (error) {
      console.error('Image picker error:', error);
      Alert.alert('Hitilafu', 'Imeshindwa kuchagua picha');
    }
  };

  const handleVideoPicker = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert('Ruhusa', 'Tunahitaji ruhusa ya kufikia video zako');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Videos,
        allowsEditing: true,
        quality: 0.8,
      });

      if (!result.canceled) {
        setSelectedVideo(result.assets[0]);
      }
    } catch (error) {
      console.error('Video picker error:', error);
      Alert.alert('Hitilafu', 'Imeshindwa kuchagua video');
    }
  };

  const handleCameraPicker = async () => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert('Ruhusa', 'Tunahitaji ruhusa ya kutumia kamera');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        quality: 0.8,
        aspect: [1, 1],
      });

      if (!result.canceled) {
        setSelectedImages([...selectedImages, result.assets[0]]);
      }
    } catch (error) {
      console.error('Camera error:', error);
      Alert.alert('Hitilafu', 'Imeshindwa kupiga picha');
    }
  };

  const removeImage = (index) => {
    const newImages = selectedImages.filter((_, i) => i !== index);
    setSelectedImages(newImages);
  };

  const removeVideo = () => {
    setSelectedVideo(null);
  };

  const uploadMedia = async () => {
    const uploadedMedia = [];

    // Upload images
    for (const image of selectedImages) {
      try {
        const response = await uploadAPI.uploadImage(image);
        if (response.success) {
          uploadedMedia.push({
            type: 'image',
            url: response.data.url,
            thumbnail: response.data.thumbnail
          });
        }
      } catch (error) {
        console.error('Image upload error:', error);
      }
    }

    // Upload video
    if (selectedVideo) {
      try {
        const response = await uploadAPI.uploadVideo(selectedVideo);
        if (response.success) {
          uploadedMedia.push({
            type: 'video',
            url: response.data.url,
            thumbnail: response.data.thumbnail
          });
        }
      } catch (error) {
        console.error('Video upload error:', error);
      }
    }

    return uploadedMedia;
  };

  const handlePost = async () => {
    if (!content.trim() && selectedImages.length === 0 && !selectedVideo) {
      Alert.alert('Hitilafu', 'Andika kitu au chagua picha/video');
      return;
    }

    try {
      setIsPosting(true);

      // Upload media first
      const mediaUrls = await uploadMedia();

      // Create post
      const postData = {
        content: content.trim(),
        media: mediaUrls,
        privacy: privacy,
        location: null, // Add location if needed
        tags: [], // Add tags if needed
      };

      const response = await postsAPI.createPost(postData);

      if (response.success) {
        Alert.alert(
          'Mafanikio!',
          'Chapisho limechapishwa kwa mafanikio',
          [
            {
              text: 'Sawa',
              onPress: () => navigation.goBack()
            }
          ]
        );
      } else {
        Alert.alert('Hitilafu', response.message || 'Imeshindwa kuchapisha');
      }
    } catch (error) {
      console.error('Post creation error:', error);
      Alert.alert('Hitilafu', 'Imeshindwa kuchapisha chapisho');
    } finally {
      setIsPosting(false);
    }
  };

  const getPrivacyIcon = () => {
    switch (privacy) {
      case 'PUBLIC':
        return 'public';
      case 'FRIENDS':
        return 'people';
      case 'PRIVATE':
        return 'lock';
      default:
        return 'public';
    }
  };

  const getPrivacyText = () => {
    switch (privacy) {
      case 'PUBLIC':
        return 'Umma';
      case 'FRIENDS':
        return 'Marafiki';
      case 'PRIVATE':
        return 'Binafsi';
      default:
        return 'Umma';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.cancelText}>Ghairi</Text>
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Chapisho Jipya</Text>
        
        <TouchableOpacity
          style={[
            styles.postButton,
            (!content.trim() && selectedImages.length === 0 && !selectedVideo) && styles.postButtonDisabled
          ]}
          onPress={handlePost}
          disabled={isPosting || (!content.trim() && selectedImages.length === 0 && !selectedVideo)}
        >
          {isPosting ? (
            <ActivityIndicator size="small" color={colors.white} />
          ) : (
            <Text style={styles.postButtonText}>Chapisha</Text>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {/* User Info */}
        <View style={styles.userInfo}>
          <Image
            source={{ uri: user?.profilePicture || 'https://via.placeholder.com/50' }}
            style={styles.userAvatar}
          />
          <View style={styles.userDetails}>
            <Text style={styles.userName}>
              {user?.firstName} {user?.lastName}
            </Text>
            
            {/* Privacy Selector */}
            <TouchableOpacity style={styles.privacySelector}>
              <Icon name={getPrivacyIcon()} size={16} color={colors.textSecondary} />
              <Text style={styles.privacyText}>{getPrivacyText()}</Text>
              <Icon name="arrow-drop-down" size={16} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Content Input */}
        <TextInput
          style={styles.contentInput}
          placeholder="Unafikiria nini?"
          placeholderTextColor={colors.textSecondary}
          value={content}
          onChangeText={setContent}
          multiline
          textAlignVertical="top"
        />

        {/* Selected Images */}
        {selectedImages.length > 0 && (
          <View style={styles.mediaContainer}>
            <Text style={styles.mediaTitle}>Picha ({selectedImages.length})</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {selectedImages.map((image, index) => (
                <View key={index} style={styles.imageContainer}>
                  <Image source={{ uri: image.uri }} style={styles.selectedImage} />
                  <TouchableOpacity
                    style={styles.removeButton}
                    onPress={() => removeImage(index)}
                  >
                    <Icon name="close" size={16} color={colors.white} />
                  </TouchableOpacity>
                </View>
              ))}
            </ScrollView>
          </View>
        )}

        {/* Selected Video */}
        {selectedVideo && (
          <View style={styles.mediaContainer}>
            <Text style={styles.mediaTitle}>Video</Text>
            <View style={styles.videoContainer}>
              <Image source={{ uri: selectedVideo.uri }} style={styles.videoThumbnail} />
              <View style={styles.videoOverlay}>
                <Icon name="play-circle-filled" size={48} color={colors.white} />
              </View>
              <TouchableOpacity
                style={styles.removeButton}
                onPress={removeVideo}
              >
                <Icon name="close" size={16} color={colors.white} />
              </TouchableOpacity>
            </View>
          </View>
        )}
      </ScrollView>

      {/* Media Options */}
      <View style={styles.mediaOptions}>
        <TouchableOpacity style={styles.mediaOption} onPress={handleImagePicker}>
          <Icon name="photo-library" size={24} color={colors.success} />
          <Text style={styles.mediaOptionText}>Picha</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.mediaOption} onPress={handleVideoPicker}>
          <Icon name="videocam" size={24} color={colors.error} />
          <Text style={styles.mediaOptionText}>Video</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.mediaOption} onPress={handleCameraPicker}>
          <Icon name="camera-alt" size={24} color={colors.primary} />
          <Text style={styles.mediaOptionText}>Kamera</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.mediaOption}>
          <Icon name="location-on" size={24} color={colors.warning} />
          <Text style={styles.mediaOptionText}>Mahali</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.mediaOption}>
          <Icon name="local-offer" size={24} color={colors.info} />
          <Text style={styles.mediaOptionText}>Tag</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  cancelButton: {
    paddingVertical: spacing.sm,
  },
  cancelText: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
  },
  headerTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
  },
  postButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    borderRadius: 20,
  },
  postButtonDisabled: {
    backgroundColor: colors.gray,
  },
  postButtonText: {
    color: colors.white,
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
  },
  userAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: spacing.md,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  privacySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 15,
    alignSelf: 'flex-start',
  },
  privacyText: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginHorizontal: spacing.xs,
  },
  contentInput: {
    fontSize: typography.fontSize.lg,
    color: colors.text,
    minHeight: 120,
    textAlignVertical: 'top',
    marginBottom: spacing.lg,
  },
  mediaContainer: {
    marginBottom: spacing.lg,
  },
  mediaTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  imageContainer: {
    position: 'relative',
    marginRight: spacing.sm,
  },
  selectedImage: {
    width: 100,
    height: 100,
    borderRadius: 8,
  },
  videoContainer: {
    position: 'relative',
    width: 200,
    height: 150,
  },
  videoThumbnail: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  videoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 8,
  },
  removeButton: {
    position: 'absolute',
    top: 5,
    right: 5,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  mediaOptions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  mediaOption: {
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  mediaOptionText: {
    fontSize: typography.fontSize.xs,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
});
