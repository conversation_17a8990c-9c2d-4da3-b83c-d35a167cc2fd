package com.prochat.controller;

import com.prochat.model.Task;
import com.prochat.model.UserTask;
import com.prochat.service.TaskService;
import com.prochat.security.CurrentUser;
import com.prochat.security.UserPrincipal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

@RestController
@RequestMapping("/api/tasks")
@PreAuthorize("hasRole('USER')")
public class TaskController {

    @Autowired
    private TaskService taskService;

    @GetMapping("/available")
    public ResponseEntity<?> getAvailableTasks(@CurrentUser UserPrincipal currentUser) {
        try {
            List<Task> tasks = taskService.getAvailableTasks(currentUser.getId());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", tasks);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata kazi: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/user")
    public ResponseEntity<?> getUserTasks(@CurrentUser UserPrincipal currentUser) {
        try {
            List<UserTask> userTasks = taskService.getUserTasks(currentUser.getId());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", userTasks);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata kazi za mtumiaji: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/{taskId}/start")
    public ResponseEntity<?> startTask(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable Long taskId) {
        try {
            UserTask userTask = taskService.startTask(currentUser.getId(), taskId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Kazi imeanza kwa mafanikio");
            response.put("data", userTask);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kuanza kazi: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/user/{userTaskId}/complete")
    public ResponseEntity<?> completeTask(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable Long userTaskId) {
        try {
            UserTask completedTask = taskService.completeTask(currentUser.getId(), userTaskId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Hongera! Umekamilisha kazi");
            response.put("data", Map.of(
                "userTask", completedTask,
                "rewardAmount", completedTask.getRewardEarned(),
                "newBalance", completedTask.getUser().getWallet().getBalance()
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kukamilisha kazi: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/user/{userTaskId}/progress")
    public ResponseEntity<?> getTaskProgress(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable Long userTaskId) {
        try {
            UserTask userTask = taskService.getTaskProgress(currentUser.getId(), userTaskId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", Map.of(
                "currentProgress", userTask.getCurrentProgress(),
                "targetCount", userTask.getTask().getTargetCount(),
                "progressPercentage", (userTask.getCurrentProgress() * 100.0) / userTask.getTask().getTargetCount(),
                "status", userTask.getStatus(),
                "canComplete", userTask.getCurrentProgress() >= userTask.getTask().getTargetCount()
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata maendeleo: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/user/{userTaskId}/update-progress")
    public ResponseEntity<?> updateTaskProgress(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable Long userTaskId,
            @RequestBody UpdateProgressRequest request) {
        try {
            UserTask userTask = taskService.updateTaskProgress(
                currentUser.getId(), 
                userTaskId, 
                request.getProgressIncrement()
            );
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Maendeleo yamesasishwa");
            response.put("data", userTask);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kusasisha maendeleo: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/categories")
    public ResponseEntity<?> getTaskCategories() {
        try {
            List<String> categories = taskService.getTaskCategories();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", categories);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata makundi: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/leaderboard")
    public ResponseEntity<?> getTaskLeaderboard(
            @RequestParam(defaultValue = "weekly") String period,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            List<Map<String, Object>> leaderboard = taskService.getTaskLeaderboard(period, page, size);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", leaderboard);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata jedwali la ushindi: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/stats")
    public ResponseEntity<?> getUserTaskStats(@CurrentUser UserPrincipal currentUser) {
        try {
            Map<String, Object> stats = taskService.getUserTaskStats(currentUser.getId());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", stats);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata takwimu: " + e.getMessage()
            ));
        }
    }

    // Admin endpoints
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> createTask(@Valid @RequestBody CreateTaskRequest request) {
        try {
            Task task = taskService.createTask(request);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Kazi imeundwa kwa mafanikio");
            response.put("data", task);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kuunda kazi: " + e.getMessage()
            ));
        }
    }

    @PutMapping("/{taskId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateTask(
            @PathVariable Long taskId,
            @Valid @RequestBody UpdateTaskRequest request) {
        try {
            Task task = taskService.updateTask(taskId, request);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Kazi imesasishwa kwa mafanikio");
            response.put("data", task);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kusasisha kazi: " + e.getMessage()
            ));
        }
    }

    @DeleteMapping("/{taskId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteTask(@PathVariable Long taskId) {
        try {
            taskService.deleteTask(taskId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Kazi imefutwa kwa mafanikio");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kufuta kazi: " + e.getMessage()
            ));
        }
    }

    // Request DTOs
    public static class UpdateProgressRequest {
        private Integer progressIncrement;
        
        public Integer getProgressIncrement() { return progressIncrement; }
        public void setProgressIncrement(Integer progressIncrement) { this.progressIncrement = progressIncrement; }
    }

    public static class CreateTaskRequest {
        private String taskType;
        private String title;
        private String description;
        private Integer targetCount;
        private Double rewardAmount;
        private Boolean isDaily;
        private Boolean isWeekly;
        private Boolean isMonthly;
        private String startDate;
        private String endDate;
        
        // Getters and setters
        public String getTaskType() { return taskType; }
        public void setTaskType(String taskType) { this.taskType = taskType; }
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public Integer getTargetCount() { return targetCount; }
        public void setTargetCount(Integer targetCount) { this.targetCount = targetCount; }
        public Double getRewardAmount() { return rewardAmount; }
        public void setRewardAmount(Double rewardAmount) { this.rewardAmount = rewardAmount; }
        public Boolean getIsDaily() { return isDaily; }
        public void setIsDaily(Boolean isDaily) { this.isDaily = isDaily; }
        public Boolean getIsWeekly() { return isWeekly; }
        public void setIsWeekly(Boolean isWeekly) { this.isWeekly = isWeekly; }
        public Boolean getIsMonthly() { return isMonthly; }
        public void setIsMonthly(Boolean isMonthly) { this.isMonthly = isMonthly; }
        public String getStartDate() { return startDate; }
        public void setStartDate(String startDate) { this.startDate = startDate; }
        public String getEndDate() { return endDate; }
        public void setEndDate(String endDate) { this.endDate = endDate; }
    }

    public static class UpdateTaskRequest {
        private String title;
        private String description;
        private Integer targetCount;
        private Double rewardAmount;
        private Boolean isActive;
        private String endDate;
        
        // Getters and setters
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public Integer getTargetCount() { return targetCount; }
        public void setTargetCount(Integer targetCount) { this.targetCount = targetCount; }
        public Double getRewardAmount() { return rewardAmount; }
        public void setRewardAmount(Double rewardAmount) { this.rewardAmount = rewardAmount; }
        public Boolean getIsActive() { return isActive; }
        public void setIsActive(Boolean isActive) { this.isActive = isActive; }
        public String getEndDate() { return endDate; }
        public void setEndDate(String endDate) { this.endDate = endDate; }
    }
}
