import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  TextInput,
  Alert,
  Dimensions,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors, typography, spacing } from '../../theme/theme';
import { liveStreamAPI, giftsAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import { useSocket } from '../../contexts/SocketContext';

const { width, height } = Dimensions.get('window');

export default function LiveStreamScreen({ navigation, route }) {
  const { streamId } = route.params;
  const { user } = useAuth();
  const { socket, joinLiveStream, leaveLiveStream, sendStreamComment, sendStreamGift } = useSocket();
  
  const [stream, setStream] = useState(null);
  const [comments, setComments] = useState([]);
  const [newComment, setNewComment] = useState('');
  const [viewers, setViewers] = useState(0);
  const [gifts, setGifts] = useState([]);
  const [showGifts, setShowGifts] = useState(false);
  const [loading, setLoading] = useState(true);
  const [isLiked, setIsLiked] = useState(false);
  const [isFollowing, setIsFollowing] = useState(false);
  
  const commentsRef = useRef();

  useEffect(() => {
    loadStream();
    loadGifts();
    
    if (socket) {
      joinLiveStream(streamId);
      
      // Listen for stream events
      socket.on('stream-comment', handleNewComment);
      socket.on('stream-gift', handleNewGift);
      socket.on('stream-viewers-update', handleViewersUpdate);
      socket.on('stream-ended', handleStreamEnded);
    }

    return () => {
      if (socket) {
        leaveLiveStream(streamId);
        socket.off('stream-comment');
        socket.off('stream-gift');
        socket.off('stream-viewers-update');
        socket.off('stream-ended');
      }
    };
  }, [streamId, socket]);

  const loadStream = async () => {
    try {
      const response = await liveStreamAPI.getStreamById(streamId);
      if (response.success) {
        setStream(response.data);
        setComments(response.data.comments || []);
        setViewers(response.data.viewersCount || 0);
        setIsLiked(response.data.isUserLiked || false);
        setIsFollowing(response.data.isUserFollowing || false);
      }
    } catch (error) {
      console.error('Error loading stream:', error);
      Alert.alert('Hitilafu', 'Imeshindwa kupakia live stream');
    } finally {
      setLoading(false);
    }
  };

  const loadGifts = async () => {
    try {
      const response = await giftsAPI.getAvailableGifts();
      if (response.success) {
        setGifts(response.data.filter(gift => gift.category === 'LIVE_STREAM'));
      }
    } catch (error) {
      console.error('Error loading gifts:', error);
    }
  };

  const handleNewComment = (comment) => {
    setComments(prev => [...prev, comment]);
    // Auto scroll to bottom
    setTimeout(() => {
      commentsRef.current?.scrollToEnd({ animated: true });
    }, 100);
  };

  const handleNewGift = (giftData) => {
    // Show gift animation
    setComments(prev => [...prev, {
      id: Date.now(),
      type: 'gift',
      senderName: giftData.senderName,
      giftName: giftData.giftName,
      giftEmoji: giftData.giftEmoji,
      quantity: giftData.quantity,
      timestamp: new Date().toISOString()
    }]);
  };

  const handleViewersUpdate = (count) => {
    setViewers(count);
  };

  const handleStreamEnded = () => {
    Alert.alert(
      'Live Stream Imeisha',
      'Mzungumzaji amemaliza live stream',
      [
        {
          text: 'Sawa',
          onPress: () => navigation.goBack()
        }
      ]
    );
  };

  const sendComment = () => {
    if (!newComment.trim()) return;
    
    if (!user) {
      Alert.alert('Ingia Kwanza', 'Unahitaji kuingia ili kuandika maoni');
      return;
    }

    sendStreamComment(streamId, newComment.trim());
    setNewComment('');
  };

  const handleSendGift = (gift) => {
    if (!user) {
      Alert.alert('Ingia Kwanza', 'Unahitaji kuingia ili kutuma zawadi');
      return;
    }

    Alert.alert(
      'Thibitisha Zawadi',
      `Unataka kutuma ${gift.displayName} kwa ${stream.streamerName}?`,
      [
        { text: 'Ghairi', style: 'cancel' },
        {
          text: 'Tuma',
          onPress: () => {
            sendStreamGift(streamId, gift.id, 1);
            setShowGifts(false);
          }
        }
      ]
    );
  };

  const handleLike = async () => {
    try {
      const response = await liveStreamAPI.likeStream(streamId);
      if (response.success) {
        setIsLiked(!isLiked);
      }
    } catch (error) {
      console.error('Error liking stream:', error);
    }
  };

  const handleFollow = async () => {
    try {
      const response = await liveStreamAPI.followStreamer(stream.streamerId);
      if (response.success) {
        setIsFollowing(!isFollowing);
      }
    } catch (error) {
      console.error('Error following streamer:', error);
    }
  };

  const formatViewers = (count) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  const renderComment = ({ item }) => {
    if (item.type === 'gift') {
      return (
        <View style={styles.giftComment}>
          <Text style={styles.giftText}>
            {item.giftEmoji} <Text style={styles.giftSender}>{item.senderName}</Text> alituma {item.giftName}
            {item.quantity > 1 && ` x${item.quantity}`}
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.comment}>
        <Text style={styles.commentText}>
          <Text style={styles.commentSender}>{item.senderName}: </Text>
          {item.message}
        </Text>
      </View>
    );
  };

  const renderGift = ({ item }) => (
    <TouchableOpacity
      style={styles.giftItem}
      onPress={() => handleSendGift(item)}
    >
      <Text style={styles.giftEmoji}>{item.emoji || '🎁'}</Text>
      <Text style={styles.giftName}>{item.displayName}</Text>
      <Text style={styles.giftPrice}>{item.price} TZS</Text>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Inapakia live stream...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!stream) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Live stream haipatikani</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar hidden />
      
      {/* Video Player Area */}
      <View style={styles.videoContainer}>
        <View style={styles.videoPlaceholder}>
          <Icon name="play-circle-filled" size={64} color={colors.white} />
          <Text style={styles.videoText}>Live Stream Video</Text>
        </View>
        
        {/* Stream Info Overlay */}
        <View style={styles.streamOverlay}>
          <View style={styles.topOverlay}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.goBack()}
            >
              <Icon name="arrow-back" size={24} color={colors.white} />
            </TouchableOpacity>
            
            <View style={styles.liveIndicator}>
              <View style={styles.liveDot} />
              <Text style={styles.liveText}>LIVE</Text>
            </View>
            
            <View style={styles.viewersCount}>
              <Icon name="visibility" size={16} color={colors.white} />
              <Text style={styles.viewersText}>{formatViewers(viewers)}</Text>
            </View>
          </View>
          
          <View style={styles.bottomOverlay}>
            <View style={styles.streamerInfo}>
              <Text style={styles.streamerName}>{stream.streamerName}</Text>
              <Text style={styles.streamTitle}>{stream.title}</Text>
            </View>
            
            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleLike}
              >
                <Icon 
                  name={isLiked ? "favorite" : "favorite-border"} 
                  size={24} 
                  color={isLiked ? colors.error : colors.white} 
                />
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.actionButton,
                  isFollowing && styles.followingButton
                ]}
                onPress={handleFollow}
              >
                <Icon 
                  name={isFollowing ? "person-remove" : "person-add"} 
                  size={24} 
                  color={colors.white} 
                />
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => setShowGifts(true)}
              >
                <Icon name="card-giftcard" size={24} color={colors.white} />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>

      {/* Comments Section */}
      <View style={styles.commentsSection}>
        <FlatList
          ref={commentsRef}
          data={comments}
          renderItem={renderComment}
          keyExtractor={(item, index) => `${item.id || index}`}
          style={styles.commentsList}
          showsVerticalScrollIndicator={false}
          onContentSizeChange={() => commentsRef.current?.scrollToEnd({ animated: true })}
        />
        
        {/* Comment Input */}
        <View style={styles.commentInput}>
          <TextInput
            style={styles.commentTextInput}
            placeholder="Andika maoni..."
            placeholderTextColor={colors.textSecondary}
            value={newComment}
            onChangeText={setNewComment}
            multiline
            maxLength={200}
          />
          <TouchableOpacity
            style={[
              styles.sendButton,
              !newComment.trim() && styles.sendButtonDisabled
            ]}
            onPress={sendComment}
            disabled={!newComment.trim()}
          >
            <Icon name="send" size={20} color={colors.white} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Gifts Modal */}
      {showGifts && (
        <View style={styles.giftsModal}>
          <View style={styles.giftsHeader}>
            <Text style={styles.giftsTitle}>Chagua Zawadi</Text>
            <TouchableOpacity onPress={() => setShowGifts(false)}>
              <Icon name="close" size={24} color={colors.text} />
            </TouchableOpacity>
          </View>
          
          <FlatList
            data={gifts}
            renderItem={renderGift}
            keyExtractor={(item) => item.id.toString()}
            numColumns={4}
            style={styles.giftsList}
          />
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: colors.white,
    fontSize: typography.fontSize.md,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: colors.white,
    fontSize: typography.fontSize.md,
  },
  videoContainer: {
    flex: 1,
    position: 'relative',
  },
  videoPlaceholder: {
    flex: 1,
    backgroundColor: colors.black,
    justifyContent: 'center',
    alignItems: 'center',
  },
  videoText: {
    color: colors.white,
    fontSize: typography.fontSize.lg,
    marginTop: spacing.md,
  },
  streamOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'space-between',
  },
  topOverlay: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.xl,
  },
  backButton: {
    padding: spacing.sm,
  },
  liveIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.error,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  liveDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: colors.white,
    marginRight: spacing.xs,
  },
  liveText: {
    color: colors.white,
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.bold,
  },
  viewersCount: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  viewersText: {
    color: colors.white,
    fontSize: typography.fontSize.sm,
    marginLeft: spacing.xs,
  },
  bottomOverlay: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.lg,
  },
  streamerInfo: {
    flex: 1,
  },
  streamerName: {
    color: colors.white,
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    marginBottom: spacing.xs,
  },
  streamTitle: {
    color: colors.white,
    fontSize: typography.fontSize.md,
    opacity: 0.8,
  },
  actionButtons: {
    alignItems: 'center',
    gap: spacing.md,
  },
  actionButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  followingButton: {
    backgroundColor: colors.primary,
  },
  commentsSection: {
    height: height * 0.3,
    backgroundColor: colors.white,
  },
  commentsList: {
    flex: 1,
    paddingHorizontal: spacing.md,
    paddingTop: spacing.md,
  },
  comment: {
    marginBottom: spacing.sm,
  },
  commentText: {
    fontSize: typography.fontSize.sm,
    color: colors.text,
  },
  commentSender: {
    fontWeight: typography.fontWeight.semiBold,
    color: colors.primary,
  },
  giftComment: {
    backgroundColor: colors.warning + '20',
    borderRadius: 8,
    padding: spacing.sm,
    marginBottom: spacing.sm,
  },
  giftText: {
    fontSize: typography.fontSize.sm,
    color: colors.warning,
  },
  giftSender: {
    fontWeight: typography.fontWeight.semiBold,
  },
  commentInput: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  commentTextInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 20,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: typography.fontSize.md,
    color: colors.text,
    maxHeight: 80,
    marginRight: spacing.sm,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: colors.gray,
  },
  giftsModal: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    maxHeight: height * 0.5,
  },
  giftsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  giftsTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
  },
  giftsList: {
    padding: spacing.md,
  },
  giftItem: {
    flex: 1,
    alignItems: 'center',
    padding: spacing.md,
    margin: spacing.xs,
    borderRadius: 8,
    backgroundColor: colors.surface,
  },
  giftEmoji: {
    fontSize: 24,
    marginBottom: spacing.xs,
  },
  giftName: {
    fontSize: typography.fontSize.xs,
    color: colors.text,
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
  giftPrice: {
    fontSize: typography.fontSize.xs,
    color: colors.primary,
    fontWeight: typography.fontWeight.medium,
  },
});
