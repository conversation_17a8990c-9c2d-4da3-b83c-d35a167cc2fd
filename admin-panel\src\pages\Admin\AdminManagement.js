import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON>ton,
  Grid,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  InputAdornment,
  IconButton,
  Menu,
  MenuItem,
  Avatar,
} from '@mui/material';
import {
  Search,
  AdminPanelSettings,
  Edit,
  Delete,
  MoreVert,
  Visibility,
  Security,
  Add,
} from '@mui/icons-material';

export default function AdminManagement() {
  const [admins] = useState([
    {
      id: 1,
      firstName: 'Super',
      lastName: 'Admin',
      email: '<EMAIL>',
      username: 'superadmin',
      adminRole: 'SUPER_ADMIN',
      isActive: true,
      lastLogin: '2024-01-20T10:30:00',
      createdAt: '2024-01-01T00:00:00',
    },
    {
      id: 2,
      firstName: 'John',
      lastName: 'Moderator',
      email: '<EMAIL>',
      username: 'john<PERSON><PERSON>',
      adminRole: 'MODERATOR',
      isActive: true,
      lastLogin: '2024-01-20T09:15:00',
      createdAt: '2024-01-05T00:00:00',
    },
    {
      id: 3,
      firstName: 'Mary',
      lastName: 'Finance',
      email: '<EMAIL>',
      username: 'maryfinance',
      adminRole: 'FINANCE_OFFICER',
      isActive: true,
      lastLogin: '2024-01-19T16:45:00',
      createdAt: '2024-01-10T00:00:00',
    },
  ]);

  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchQuery, setSearchQuery] = useState('');
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedAdmin, setSelectedAdmin] = useState(null);

  const handleActionClick = (event, admin) => {
    setSelectedAdmin(admin);
    setAnchorEl(event.currentTarget);
  };

  const handleActionClose = () => {
    setAnchorEl(null);
    setSelectedAdmin(null);
  };

  const getRoleColor = (role) => {
    const colors = {
      SUPER_ADMIN: 'error',
      MODERATOR: 'primary',
      FINANCE_OFFICER: 'success',
      SUPPORT_TEAM: 'info',
      RECRUITER: 'warning',
      EVENT_OFFICER: 'secondary',
      JOURNALIST_ADMIN: 'default',
    };
    return colors[role] || 'default';
  };

  const getRoleLabel = (role) => {
    const labels = {
      SUPER_ADMIN: 'Super Admin',
      MODERATOR: 'Moderator',
      FINANCE_OFFICER: 'Finance Officer',
      SUPPORT_TEAM: 'Support Team',
      RECRUITER: 'Recruiter',
      EVENT_OFFICER: 'Event Officer',
      JOURNALIST_ADMIN: 'Journalist',
    };
    return labels[role] || role;
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Uongozi wa Waongozi
        </Typography>
        <Button variant="contained" startIcon={<Add />}>
          Ongeza Msimamizi
        </Button>
      </Box>

      {/* Stats */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Waongozi wa Jumla
              </Typography>
              <Typography variant="h4" color="primary">
                {admins.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Hai
              </Typography>
              <Typography variant="h4" color="success.main">
                {admins.filter(a => a.isActive).length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Super Admins
              </Typography>
              <Typography variant="h4" color="error.main">
                {admins.filter(a => a.adminRole === 'SUPER_ADMIN').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Moderators
              </Typography>
              <Typography variant="h4" color="info.main">
                {admins.filter(a => a.adminRole === 'MODERATOR').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <TextField
            fullWidth
            placeholder="Tafuta waongozi..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
          />
        </CardContent>
      </Card>

      {/* Admins Table */}
      <Card>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Msimamizi</TableCell>
                <TableCell>Jukumu</TableCell>
                <TableCell>Hali</TableCell>
                <TableCell>Mwisho Kuingia</TableCell>
                <TableCell>Tarehe ya Kuunda</TableCell>
                <TableCell>Vitendo</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {admins
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((admin) => (
                  <TableRow key={admin.id}>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar sx={{ mr: 2 }}>
                          {admin.firstName[0]}{admin.lastName[0]}
                        </Avatar>
                        <Box>
                          <Typography variant="subtitle2">
                            {admin.firstName} {admin.lastName}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {admin.email}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            @{admin.username}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getRoleLabel(admin.adminRole)}
                        color={getRoleColor(admin.adminRole)}
                        size="small"
                        icon={<AdminPanelSettings />}
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={admin.isActive ? 'Hai' : 'Haijafungwa'}
                        color={admin.isActive ? 'success' : 'error'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(admin.lastLogin).toLocaleDateString('sw-TZ')}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {new Date(admin.lastLogin).toLocaleTimeString('sw-TZ')}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(admin.createdAt).toLocaleDateString('sw-TZ')}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <IconButton onClick={(e) => handleActionClick(e, admin)}>
                        <MoreVert />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={admins.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={(e, newPage) => setPage(newPage)}
          onRowsPerPageChange={(e) => {
            setRowsPerPage(parseInt(e.target.value, 10));
            setPage(0);
          }}
        />
      </Card>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleActionClose}
      >
        <MenuItem onClick={handleActionClose}>
          <Visibility sx={{ mr: 1 }} /> Angalia
        </MenuItem>
        <MenuItem onClick={handleActionClose}>
          <Edit sx={{ mr: 1 }} /> Hariri
        </MenuItem>
        <MenuItem onClick={handleActionClose}>
          <Security sx={{ mr: 1 }} /> Ruhusa
        </MenuItem>
        <MenuItem onClick={handleActionClose}>
          <Delete sx={{ mr: 1 }} /> Futa
        </MenuItem>
      </Menu>
    </Box>
  );
}
