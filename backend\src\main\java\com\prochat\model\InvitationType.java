package com.prochat.model;

public enum InvitationType {
    WEDDING,
    BIRTH<PERSON>Y,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    BAB<PERSON>_SHOWER,
    ANNIVERSARY,
    ENGAGEMENT,
    HOUSEWARMING,
    RETIREMENT,
    <PERSON>UNER<PERSON>,
    MEMORIAL,
    B<PERSON><PERSON><PERSON>S_EVENT,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    D<PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    H<PERSON>IDAY,
    RELIGIOUS,
    C<PERSON><PERSON><PERSON><PERSON>,
    <PERSON>OR<PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>ISER,
    REUNION,
    FESTIVAL,
    CONCERT,
    EXH<PERSON><PERSON>ION,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>
}
