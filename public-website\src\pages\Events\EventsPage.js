import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Chip,
  Button,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
} from '@mui/material';
import {
  Search,
  Event,
  LocationOn,
  CalendarToday,
  People,
  AttachMoney,
} from '@mui/icons-material';

export default function EventsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [locationFilter, setLocationFilter] = useState('');

  const events = [
    {
      id: 1,
      title: '<PERSON><PERSON><PERSON> wa Bongo Flava - Diamond Platnumz Live',
      description: 'Onyesho la muziki wa Diamond Platnumz na wageni wake...',
      organizer: 'Wasafi Events',
      organizerAvatar: '/images/wasafi-logo.jpg',
      date: '2024-02-15T19:00:00',
      location: 'Mlimani City, Dar es Salaam',
      category: '<PERSON><PERSON><PERSON>',
      price: 25000,
      currency: 'TSH',
      imageUrl: '/images/diamond-concert.jpg',
      attendees: 1250,
      maxAttendees: 2000,
      featured: true,
    },
    {
      id: 2,
      title: 'Semina ya Biashara na Uongozi',
      description: 'Semina ya siku moja kuhusu jinsi ya kuanza na kuongoza biashara...',
      organizer: 'TBC Bank',
      organizerAvatar: '/images/tbc-logo.jpg',
      date: '2024-02-20T09:00:00',
      location: 'Arusha International Conference Centre',
      category: 'Biashara',
      price: 50000,
      currency: 'TSH',
      imageUrl: '/images/business-seminar.jpg',
      attendees: 80,
      maxAttendees: 200,
      featured: false,
    },
    {
      id: 3,
      title: 'Mchezo wa Mpira - Simba vs Yanga',
      description: 'Mchezo mkuu wa derby ya Dar es Salaam...',
      organizer: 'Tanzania Football Federation',
      organizerAvatar: '/images/tff-logo.jpg',
      date: '2024-02-25T16:00:00',
      location: 'Uwanja wa Taifa, Dar es Salaam',
      category: 'Michezo',
      price: 10000,
      currency: 'TSH',
      imageUrl: '/images/derby-match.jpg',
      attendees: 15000,
      maxAttendees: 60000,
      featured: true,
    },
    {
      id: 4,
      title: 'Maonesho ya Sanaa na Utamaduni',
      description: 'Maonesho ya sanaa za asili za Tanzania...',
      organizer: 'Baraza la Sanaa Tanzania',
      organizerAvatar: '/images/arts-council.jpg',
      date: '2024-03-01T10:00:00',
      location: 'Nyumba ya Sanaa, Dar es Salaam',
      category: 'Sanaa',
      price: 5000,
      currency: 'TSH',
      imageUrl: '/images/arts-exhibition.jpg',
      attendees: 120,
      maxAttendees: 500,
      featured: false,
    },
  ];

  const categories = ['Muziki', 'Biashara', 'Michezo', 'Sanaa', 'Elimu', 'Teknolojia'];
  const locations = ['Dar es Salaam', 'Arusha', 'Mwanza', 'Dodoma', 'Mbeya'];

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString('sw-TZ', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      }),
      time: date.toLocaleTimeString('sw-TZ', { 
        hour: '2-digit', 
        minute: '2-digit' 
      }),
    };
  };

  const getCategoryColor = (category) => {
    const colors = {
      'Muziki': 'primary',
      'Biashara': 'success',
      'Michezo': 'warning',
      'Sanaa': 'secondary',
      'Elimu': 'info',
      'Teknolojia': 'error',
    };
    return colors[category] || 'default';
  };

  const filteredEvents = events.filter(event => {
    const matchesSearch = event.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         event.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         event.organizer.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = !categoryFilter || event.category === categoryFilter;
    const matchesLocation = !locationFilter || event.location.includes(locationFilter);
    
    return matchesSearch && matchesCategory && matchesLocation;
  });

  const featuredEvents = filteredEvents.filter(event => event.featured);
  const regularEvents = filteredEvents.filter(event => !event.featured);

  return (
    <Box sx={{ py: 4 }}>
      <Container maxWidth="lg">
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Typography variant="h3" component="h1" gutterBottom>
            Matukio ya ProChat
          </Typography>
          <Typography variant="h6" color="text.secondary" sx={{ mb: 3 }}>
            Gundua na shiriki matukio ya kuvutia karibu nawe
          </Typography>
        </Box>

        {/* Search and Filters */}
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  placeholder="Tafuta matukio..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Aina ya Tukio</InputLabel>
                  <Select
                    value={categoryFilter}
                    label="Aina ya Tukio"
                    onChange={(e) => setCategoryFilter(e.target.value)}
                  >
                    <MenuItem value="">Vyote</MenuItem>
                    {categories.map((category) => (
                      <MenuItem key={category} value={category}>
                        {category}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Mahali</InputLabel>
                  <Select
                    value={locationFilter}
                    label="Mahali"
                    onChange={(e) => setLocationFilter(e.target.value)}
                  >
                    <MenuItem value="">Mahali Pote</MenuItem>
                    {locations.map((location) => (
                      <MenuItem key={location} value={location}>
                        {location}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Featured Events */}
        {featuredEvents.length > 0 && (
          <Box sx={{ mb: 4 }}>
            <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              <Event sx={{ mr: 1 }} />
              Matukio ya Maalum
            </Typography>
            <Grid container spacing={3}>
              {featuredEvents.map((event) => {
                const { date, time } = formatDate(event.date);
                return (
                  <Grid item xs={12} md={6} key={event.id}>
                    <Card 
                      sx={{ 
                        height: '100%', 
                        cursor: 'pointer',
                        border: '2px solid',
                        borderColor: 'primary.main',
                        '&:hover': { 
                          transform: 'translateY(-4px)',
                          boxShadow: 4,
                        }, 
                        transition: 'all 0.3s' 
                      }}
                    >
                      <CardMedia
                        component="img"
                        height="250"
                        image={event.imageUrl}
                        alt={event.title}
                        sx={{ objectFit: 'cover' }}
                      />
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                          <Chip 
                            label={event.category} 
                            color={getCategoryColor(event.category)} 
                            size="small" 
                          />
                          <Chip label="MAALUM" color="error" size="small" />
                        </Box>
                        
                        <Typography variant="h5" component="h3" gutterBottom>
                          {event.title}
                        </Typography>
                        
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          {event.description}
                        </Typography>

                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <Avatar src={event.organizerAvatar} sx={{ width: 24, height: 24, mr: 1 }}>
                            {event.organizer[0]}
                          </Avatar>
                          <Typography variant="body2" color="text.secondary">
                            {event.organizer}
                          </Typography>
                        </Box>

                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <CalendarToday sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body2" color="text.secondary">
                            {date} - {time}
                          </Typography>
                        </Box>

                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <LocationOn sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body2" color="text.secondary">
                            {event.location}
                          </Typography>
                        </Box>

                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <People sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body2" color="text.secondary">
                            {event.attendees.toLocaleString()} / {event.maxAttendees.toLocaleString()} watu
                          </Typography>
                        </Box>

                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Typography variant="h6" color="primary.main" fontWeight="bold">
                            {formatCurrency(event.price)}
                          </Typography>
                          <Button variant="contained" size="large">
                            Nunua Tiketi
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                );
              })}
            </Grid>
          </Box>
        )}

        {/* Regular Events */}
        <Box>
          <Typography variant="h4" gutterBottom>
            Matukio Mengine
          </Typography>
          <Grid container spacing={3}>
            {regularEvents.map((event) => {
              const { date, time } = formatDate(event.date);
              return (
                <Grid item xs={12} md={6} lg={4} key={event.id}>
                  <Card 
                    sx={{ 
                      height: '100%', 
                      cursor: 'pointer',
                      '&:hover': { 
                        transform: 'translateY(-4px)',
                        boxShadow: 3,
                      }, 
                      transition: 'all 0.3s' 
                    }}
                  >
                    <CardMedia
                      component="img"
                      height="200"
                      image={event.imageUrl}
                      alt={event.title}
                      sx={{ objectFit: 'cover' }}
                    />
                    <CardContent>
                      <Chip 
                        label={event.category} 
                        color={getCategoryColor(event.category)} 
                        size="small" 
                        sx={{ mb: 2 }}
                      />
                      
                      <Typography variant="h6" component="h3" gutterBottom>
                        {event.title}
                      </Typography>
                      
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        {event.description.length > 100 
                          ? `${event.description.substring(0, 100)}...` 
                          : event.description
                        }
                      </Typography>

                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <CalendarToday sx={{ fontSize: 14, mr: 1, color: 'text.secondary' }} />
                        <Typography variant="caption" color="text.secondary">
                          {date}
                        </Typography>
                      </Box>

                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <LocationOn sx={{ fontSize: 14, mr: 1, color: 'text.secondary' }} />
                        <Typography variant="caption" color="text.secondary">
                          {event.location}
                        </Typography>
                      </Box>

                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="subtitle1" color="primary.main" fontWeight="bold">
                          {formatCurrency(event.price)}
                        </Typography>
                        <Button variant="contained" size="small">
                          Angalia
                        </Button>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              );
            })}
          </Grid>
        </Box>

        {/* Load More */}
        <Box sx={{ textAlign: 'center', mt: 4 }}>
          <Button variant="outlined" size="large">
            Pakia Matukio Zaidi
          </Button>
        </Box>
      </Container>
    </Box>
  );
}
