package com.prochat.controller;

import com.prochat.model.Job;
import com.prochat.model.JobApplication;
import com.prochat.service.JobService;
import com.prochat.security.CurrentUser;
import com.prochat.security.UserPrincipal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

@RestController
@RequestMapping("/api/jobs")
@PreAuthorize("hasRole('USER')")
public class JobController {

    @Autowired
    private JobService jobService;

    @GetMapping
    public ResponseEntity<?> getJobs(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String location,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String experience,
            @RequestParam(required = false) String salaryMin,
            @RequestParam(required = false) String salaryMax,
            @RequestParam(required = false) String keywords) {
        try {
            List<Job> jobs = jobService.getJobs(page, size, category, location, type, experience, salaryMin, salaryMax, keywords);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", jobs);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata kazi: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/{jobId}")
    public ResponseEntity<?> getJobById(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable Long jobId) {
        try {
            Job job = jobService.getJobById(jobId, currentUser.getId());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", job);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata kazi: " + e.getMessage()
            ));
        }
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('EMPLOYER')")
    public ResponseEntity<?> createJob(
            @CurrentUser UserPrincipal currentUser,
            @Valid @RequestBody CreateJobRequest request) {
        try {
            Job job = jobService.createJob(currentUser.getId(), request);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Kazi imeundwa kwa mafanikio");
            response.put("data", job);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kuunda kazi: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/apply")
    public ResponseEntity<?> applyForJob(
            @CurrentUser UserPrincipal currentUser,
            @Valid @RequestBody JobApplicationRequest request) {
        try {
            JobApplication application = jobService.applyForJob(currentUser.getId(), request);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Ombi lako limepokewa kwa mafanikio");
            response.put("data", application);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kuomba kazi: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/{jobId}/save")
    public ResponseEntity<?> saveJob(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable Long jobId) {
        try {
            boolean isSaved = jobService.toggleSaveJob(currentUser.getId(), jobId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", isSaved ? "Kazi imehifadhiwa" : "Kazi imeondolewa kwenye zilizohifadhiwa");
            response.put("data", Map.of("isSaved", isSaved));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kuhifadhi kazi: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/saved")
    public ResponseEntity<?> getSavedJobs(
            @CurrentUser UserPrincipal currentUser,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            List<Job> jobs = jobService.getSavedJobs(currentUser.getId(), page, size);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", jobs);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata kazi zilizohifadhiwa: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/applications")
    public ResponseEntity<?> getUserApplications(
            @CurrentUser UserPrincipal currentUser,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            List<JobApplication> applications = jobService.getUserApplications(currentUser.getId(), page, size);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", applications);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata maombi yako: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/categories")
    public ResponseEntity<?> getJobCategories() {
        try {
            List<String> categories = jobService.getJobCategories();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", categories);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata makundi: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/trending")
    public ResponseEntity<?> getTrendingJobs(
            @RequestParam(defaultValue = "10") int limit) {
        try {
            List<Job> jobs = jobService.getTrendingJobs(limit);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", jobs);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata kazi maarufu: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/recommended")
    public ResponseEntity<?> getRecommendedJobs(
            @CurrentUser UserPrincipal currentUser,
            @RequestParam(defaultValue = "10") int limit) {
        try {
            List<Job> jobs = jobService.getRecommendedJobs(currentUser.getId(), limit);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", jobs);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata kazi zilizopendekezwa: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/company/{companyId}")
    public ResponseEntity<?> getJobsByCompany(
            @PathVariable Long companyId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            List<Job> jobs = jobService.getJobsByCompany(companyId, page, size);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", jobs);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata kazi za kampuni: " + e.getMessage()
            ));
        }
    }

    // Admin endpoints
    @PutMapping("/{jobId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateJob(
            @PathVariable Long jobId,
            @Valid @RequestBody UpdateJobRequest request) {
        try {
            Job job = jobService.updateJob(jobId, request);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Kazi imesasishwa");
            response.put("data", job);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kusasisha: " + e.getMessage()
            ));
        }
    }

    @DeleteMapping("/{jobId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteJob(@PathVariable Long jobId) {
        try {
            jobService.deleteJob(jobId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Kazi imefutwa");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kufuta: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/{jobId}/applications")
    @PreAuthorize("hasRole('ADMIN') or hasRole('EMPLOYER')")
    public ResponseEntity<?> getJobApplications(
            @PathVariable Long jobId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            List<JobApplication> applications = jobService.getJobApplications(jobId, page, size);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", applications);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata maombi: " + e.getMessage()
            ));
        }
    }

    // Request DTOs
    public static class CreateJobRequest {
        private String title;
        private String description;
        private String company;
        private String location;
        private String type;
        private String category;
        private String experience;
        private Double salaryMin;
        private Double salaryMax;
        private String requirements;
        private String benefits;
        private String applicationDeadline;
        private String contactEmail;
        private String contactPhone;
        private Boolean isUrgent;
        
        // Getters and setters
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getCompany() { return company; }
        public void setCompany(String company) { this.company = company; }
        public String getLocation() { return location; }
        public void setLocation(String location) { this.location = location; }
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }
        public String getExperience() { return experience; }
        public void setExperience(String experience) { this.experience = experience; }
        public Double getSalaryMin() { return salaryMin; }
        public void setSalaryMin(Double salaryMin) { this.salaryMin = salaryMin; }
        public Double getSalaryMax() { return salaryMax; }
        public void setSalaryMax(Double salaryMax) { this.salaryMax = salaryMax; }
        public String getRequirements() { return requirements; }
        public void setRequirements(String requirements) { this.requirements = requirements; }
        public String getBenefits() { return benefits; }
        public void setBenefits(String benefits) { this.benefits = benefits; }
        public String getApplicationDeadline() { return applicationDeadline; }
        public void setApplicationDeadline(String applicationDeadline) { this.applicationDeadline = applicationDeadline; }
        public String getContactEmail() { return contactEmail; }
        public void setContactEmail(String contactEmail) { this.contactEmail = contactEmail; }
        public String getContactPhone() { return contactPhone; }
        public void setContactPhone(String contactPhone) { this.contactPhone = contactPhone; }
        public Boolean getIsUrgent() { return isUrgent; }
        public void setIsUrgent(Boolean isUrgent) { this.isUrgent = isUrgent; }
    }

    public static class UpdateJobRequest {
        private String title;
        private String description;
        private String location;
        private String type;
        private String experience;
        private Double salaryMin;
        private Double salaryMax;
        private String requirements;
        private String benefits;
        private String applicationDeadline;
        private Boolean isUrgent;
        
        // Getters and setters
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getLocation() { return location; }
        public void setLocation(String location) { this.location = location; }
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public String getExperience() { return experience; }
        public void setExperience(String experience) { this.experience = experience; }
        public Double getSalaryMin() { return salaryMin; }
        public void setSalaryMin(Double salaryMin) { this.salaryMin = salaryMin; }
        public Double getSalaryMax() { return salaryMax; }
        public void setSalaryMax(Double salaryMax) { this.salaryMax = salaryMax; }
        public String getRequirements() { return requirements; }
        public void setRequirements(String requirements) { this.requirements = requirements; }
        public String getBenefits() { return benefits; }
        public void setBenefits(String benefits) { this.benefits = benefits; }
        public String getApplicationDeadline() { return applicationDeadline; }
        public void setApplicationDeadline(String applicationDeadline) { this.applicationDeadline = applicationDeadline; }
        public Boolean getIsUrgent() { return isUrgent; }
        public void setIsUrgent(Boolean isUrgent) { this.isUrgent = isUrgent; }
    }

    public static class JobApplicationRequest {
        private Long jobId;
        private String coverLetter;
        private String resumeUrl;
        
        // Getters and setters
        public Long getJobId() { return jobId; }
        public void setJobId(Long jobId) { this.jobId = jobId; }
        public String getCoverLetter() { return coverLetter; }
        public void setCoverLetter(String coverLetter) { this.coverLetter = coverLetter; }
        public String getResumeUrl() { return resumeUrl; }
        public void setResumeUrl(String resumeUrl) { this.resumeUrl = resumeUrl; }
    }
}
