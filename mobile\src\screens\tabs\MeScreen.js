import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

export default function MeScreen({ navigation }) {
  const [user] = useState({
    name: '<PERSON>',
    username: '@john<PERSON><PERSON>i',
    userId: 'PC001234',
    avatar: 'https://via.placeholder.com/80',
    isVerified: true,
    walletBalance: '1,250,000',
    currency: 'TSH',
  });

  const handleLogout = () => {
    Alert.alert(
      'Toka',
      'Una uhakika unataka kutoka?',
      [
        { text: 'Hapana', style: 'cancel' },
        { text: 'Ndio', onPress: () => console.log('Logout') },
      ]
    );
  };

  const menuItems = [
    {
      id: 'propay',
      title: 'ProPay Wallet',
      subtitle: `${user.walletBalance} ${user.currency}`,
      icon: 'wallet',
      color: '#4CAF50',
      onPress: () => console.log('ProPay'),
    },
    {
      id: 'bonance',
      title: 'Bonance & Reward',
      subtitle: 'Zawadi na bonasi',
      icon: 'gift',
      color: '#FF9800',
      onPress: () => console.log('Bonance'),
    },
    {
      id: 'notifications',
      title: 'Notification',
      subtitle: 'Arifa za app',
      icon: 'notifications',
      color: '#2196F3',
      onPress: () => console.log('Notifications'),
    },
    {
      id: 'ads',
      title: 'Advertisement and Aids',
      subtitle: 'Matangazo yako',
      icon: 'megaphone',
      color: '#9C27B0',
      onPress: () => console.log('Ads'),
    },
    {
      id: 'settings',
      title: 'Settings and Privacy',
      subtitle: 'Mipangilio ya app',
      icon: 'settings',
      color: '#607D8B',
      onPress: () => console.log('Settings'),
    },
    {
      id: 'invite',
      title: 'Invite Friends',
      subtitle: 'Alika marafiki',
      icon: 'person-add',
      color: '#FF5722',
      onPress: () => console.log('Invite'),
    },
    {
      id: 'help',
      title: 'Help & Support',
      subtitle: 'Msaada na maswali',
      icon: 'help-circle',
      color: '#795548',
      onPress: () => console.log('Help'),
    },
  ];

  const renderMenuItem = (item) => (
    <TouchableOpacity
      key={item.id}
      style={styles.menuItem}
      onPress={item.onPress}
    >
      <View style={[styles.menuIcon, { backgroundColor: item.color }]}>
        <Ionicons name={item.icon} size={24} color="#FFFFFF" />
      </View>
      <View style={styles.menuContent}>
        <Text style={styles.menuTitle}>{item.title}</Text>
        <Text style={styles.menuSubtitle}>{item.subtitle}</Text>
      </View>
      <Ionicons name="chevron-forward" size={20} color="#999" />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Mimi</Text>
          <TouchableOpacity style={styles.scanButton}>
            <Ionicons name="qr-code-outline" size={24} color="#007AFF" />
          </TouchableOpacity>
        </View>

        {/* Profile Section */}
        <View style={styles.profileSection}>
          <View style={styles.profileHeader}>
            <Image source={{ uri: user.avatar }} style={styles.avatar} />
            <View style={styles.profileInfo}>
              <View style={styles.nameContainer}>
                <Text style={styles.userName}>{user.name}</Text>
                {user.isVerified && (
                  <Ionicons name="checkmark-circle" size={20} color="#007AFF" />
                )}
              </View>
              <Text style={styles.userHandle}>{user.username}</Text>
              <Text style={styles.userId}>ID: {user.userId}</Text>
            </View>
          </View>
          
          <TouchableOpacity style={styles.seeProfileButton}>
            <Text style={styles.seeProfileText}>Ona Profile</Text>
            <Ionicons name="chevron-forward" size={16} color="#007AFF" />
          </TouchableOpacity>
        </View>

        {/* ProPay Quick Actions */}
        <View style={styles.quickActionsSection}>
          <Text style={styles.sectionTitle}>ProPay - Haraka</Text>
          <View style={styles.quickActionsGrid}>
            <TouchableOpacity style={styles.quickAction}>
              <View style={[styles.quickActionIcon, { backgroundColor: '#4CAF50' }]}>
                <Ionicons name="send" size={20} color="#FFFFFF" />
              </View>
              <Text style={styles.quickActionText}>Tuma</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.quickAction}>
              <View style={[styles.quickActionIcon, { backgroundColor: '#2196F3' }]}>
                <Ionicons name="download" size={20} color="#FFFFFF" />
              </View>
              <Text style={styles.quickActionText}>Toa</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.quickAction}>
              <View style={[styles.quickActionIcon, { backgroundColor: '#FF9800' }]}>
                <Ionicons name="add-circle" size={20} color="#FFFFFF" />
              </View>
              <Text style={styles.quickActionText}>Weka</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.quickAction}>
              <View style={[styles.quickActionIcon, { backgroundColor: '#9C27B0' }]}>
                <Ionicons name="card" size={20} color="#FFFFFF" />
              </View>
              <Text style={styles.quickActionText}>Lipa</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Menu Items */}
        <View style={styles.menuSection}>
          {menuItems.map(renderMenuItem)}
        </View>

        {/* Logout Button */}
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Ionicons name="log-out-outline" size={24} color="#FF3B30" />
          <Text style={styles.logoutText}>Toka</Text>
        </TouchableOpacity>

        {/* App Version */}
        <View style={styles.versionSection}>
          <Text style={styles.versionText}>ProChat v1.0.0</Text>
          <Text style={styles.copyrightText}>© 2024 ProChat. Haki zote zimehifadhiwa.</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  scanButton: {
    padding: 5,
  },
  profileSection: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingVertical: 20,
    marginBottom: 10,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginRight: 15,
  },
  profileInfo: {
    flex: 1,
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  userName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginRight: 8,
  },
  userHandle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 4,
  },
  userId: {
    fontSize: 14,
    color: '#999',
  },
  seeProfileButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
  },
  seeProfileText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
    marginRight: 4,
  },
  quickActionsSection: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingVertical: 20,
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 15,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickAction: {
    alignItems: 'center',
    flex: 1,
  },
  quickActionIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  quickActionText: {
    fontSize: 12,
    color: '#333',
    fontWeight: '500',
  },
  menuSection: {
    backgroundColor: '#FFFFFF',
    marginBottom: 10,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  menuIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  menuContent: {
    flex: 1,
  },
  menuTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  menuSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
    paddingVertical: 15,
    marginBottom: 10,
  },
  logoutText: {
    fontSize: 16,
    color: '#FF3B30',
    fontWeight: '600',
    marginLeft: 8,
  },
  versionSection: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  versionText: {
    fontSize: 14,
    color: '#999',
    marginBottom: 4,
  },
  copyrightText: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
  },
});
