package com.prochat.controller;

import com.prochat.model.Donation;
import com.prochat.model.DonationCampaign;
import com.prochat.service.DonationService;
import com.prochat.service.TaxService;
import com.prochat.security.CurrentUser;
import com.prochat.security.UserPrincipal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

@RestController
@RequestMapping("/api/donations")
@PreAuthorize("hasRole('USER')")
public class DonationController {

    @Autowired
    private DonationService donationService;

    @Autowired
    private TaxService taxService;

    @GetMapping("/campaigns")
    public ResponseEntity<?> getCampaigns(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String status) {
        try {
            List<DonationCampaign> campaigns = donationService.getCampaigns(page, size, category, status);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", campaigns);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata kampeni: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/campaigns/{campaignId}")
    public ResponseEntity<?> getCampaignById(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable Long campaignId) {
        try {
            DonationCampaign campaign = donationService.getCampaignById(campaignId, currentUser.getId());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", campaign);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata kampeni: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/donate")
    public ResponseEntity<?> makeDonation(
            @CurrentUser UserPrincipal currentUser,
            @Valid @RequestBody DonationRequest request) {
        try {
            // Calculate VAT
            TaxService.TaxCalculationResult vatResult = taxService.calculateVAT(
                request.getAmount(), "DONATION");
            
            // Process donation
            Donation donation = donationService.makeDonation(
                currentUser.getId(),
                request.getCampaignId(),
                request.getAmount(),
                request.getMessage(),
                request.getIsAnonymous()
            );
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Asante kwa mchango wako!");
            response.put("data", Map.of(
                "donation", donation,
                "grossAmount", vatResult.getGrossAmount(),
                "vatAmount", vatResult.getTaxAmount(),
                "netAmount", vatResult.getNetAmount(),
                "newTotal", donation.getCampaign().getCurrentAmount()
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kutoa mchango: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/history")
    public ResponseEntity<?> getDonationHistory(
            @CurrentUser UserPrincipal currentUser,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            List<Donation> donations = donationService.getUserDonations(currentUser.getId(), page, size);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", donations);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata historia: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/campaigns")
    public ResponseEntity<?> createCampaign(
            @CurrentUser UserPrincipal currentUser,
            @Valid @RequestBody CreateCampaignRequest request) {
        try {
            DonationCampaign campaign = donationService.createCampaign(currentUser.getId(), request);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Kampeni imeundwa kwa mafanikio");
            response.put("data", campaign);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kuunda kampeni: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/campaigns/{campaignId}/donations")
    public ResponseEntity<?> getCampaignDonations(
            @PathVariable Long campaignId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "false") boolean includeAnonymous) {
        try {
            List<Donation> donations = donationService.getCampaignDonations(
                campaignId, page, size, includeAnonymous);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", donations);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata michango: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/campaigns/{campaignId}/stats")
    public ResponseEntity<?> getCampaignStats(@PathVariable Long campaignId) {
        try {
            Map<String, Object> stats = donationService.getCampaignStats(campaignId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", stats);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata takwimu: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/campaigns/{campaignId}/share")
    public ResponseEntity<?> shareCampaign(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable Long campaignId) {
        try {
            String shareUrl = donationService.generateShareUrl(campaignId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", Map.of(
                "shareUrl", shareUrl,
                "message", "Shiriki kampeni hii na marafiki zako!"
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kushiriki: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/categories")
    public ResponseEntity<?> getCampaignCategories() {
        try {
            List<String> categories = donationService.getCampaignCategories();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", categories);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata makundi: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/trending")
    public ResponseEntity<?> getTrendingCampaigns(
            @RequestParam(defaultValue = "10") int limit) {
        try {
            List<DonationCampaign> campaigns = donationService.getTrendingCampaigns(limit);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", campaigns);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata kampeni maarufu: " + e.getMessage()
            ));
        }
    }

    // Admin endpoints
    @PutMapping("/campaigns/{campaignId}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateCampaignStatus(
            @PathVariable Long campaignId,
            @RequestBody UpdateCampaignStatusRequest request) {
        try {
            DonationCampaign campaign = donationService.updateCampaignStatus(
                campaignId, request.getStatus(), request.getReason());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Hali ya kampeni imesasishwa");
            response.put("data", campaign);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kusasisha hali: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/admin/pending")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> getPendingCampaigns(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            List<DonationCampaign> campaigns = donationService.getPendingCampaigns(page, size);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", campaigns);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata kampeni zinazosubiri: " + e.getMessage()
            ));
        }
    }

    // Request DTOs
    public static class DonationRequest {
        private Long campaignId;
        private BigDecimal amount;
        private String message;
        private Boolean isAnonymous;
        
        // Getters and setters
        public Long getCampaignId() { return campaignId; }
        public void setCampaignId(Long campaignId) { this.campaignId = campaignId; }
        public BigDecimal getAmount() { return amount; }
        public void setAmount(BigDecimal amount) { this.amount = amount; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public Boolean getIsAnonymous() { return isAnonymous; }
        public void setIsAnonymous(Boolean isAnonymous) { this.isAnonymous = isAnonymous; }
    }

    public static class CreateCampaignRequest {
        private String title;
        private String description;
        private BigDecimal targetAmount;
        private String category;
        private String imageUrl;
        private String endDate;
        private String beneficiaryInfo;
        
        // Getters and setters
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public BigDecimal getTargetAmount() { return targetAmount; }
        public void setTargetAmount(BigDecimal targetAmount) { this.targetAmount = targetAmount; }
        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }
        public String getImageUrl() { return imageUrl; }
        public void setImageUrl(String imageUrl) { this.imageUrl = imageUrl; }
        public String getEndDate() { return endDate; }
        public void setEndDate(String endDate) { this.endDate = endDate; }
        public String getBeneficiaryInfo() { return beneficiaryInfo; }
        public void setBeneficiaryInfo(String beneficiaryInfo) { this.beneficiaryInfo = beneficiaryInfo; }
    }

    public static class UpdateCampaignStatusRequest {
        private String status;
        private String reason;
        
        // Getters and setters
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }
}
