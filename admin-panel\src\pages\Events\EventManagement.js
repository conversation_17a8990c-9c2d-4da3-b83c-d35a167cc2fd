import React, { useState } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Grid,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  InputAdornment,
  IconButton,
  Menu,
  MenuItem,
} from '@mui/material';
import {
  Search,
  Event,
  CheckCircle,
  Cancel,
  MoreVert,
  Visibility,
} from '@mui/icons-material';

export default function EventManagement() {
  const [events] = useState([
    {
      id: 1,
      title: '<PERSON><PERSON><PERSON> wa Bongo Flava',
      organizer: 'Diamond Platnumz',
      date: '2024-02-15',
      location: 'Dar es Salaam',
      status: 'PENDING',
      ticketsSold: 150,
      totalTickets: 500,
      revenue: 750000,
    },
    {
      id: 2,
      title: '<PERSON><PERSON>',
      organizer: 'TBC Bank',
      date: '2024-02-20',
      location: 'Arusha',
      status: 'APPROVED',
      ticketsSold: 80,
      totalTickets: 100,
      revenue: 400000,
    },
  ]);

  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchQuery, setSearchQuery] = useState('');
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedEvent, setSelectedEvent] = useState(null);

  const handleActionClick = (event, eventItem) => {
    setSelectedEvent(eventItem);
    setAnchorEl(event.currentTarget);
  };

  const handleActionClose = () => {
    setAnchorEl(null);
    setSelectedEvent(null);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'APPROVED': return 'success';
      case 'REJECTED': return 'error';
      case 'PENDING': return 'warning';
      default: return 'default';
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'APPROVED': return 'Imeidhinishwa';
      case 'REJECTED': return 'Imekataliwa';
      case 'PENDING': return 'Inasubiri';
      default: return status;
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Uongozi wa Matukio
        </Typography>
        <Button variant="contained" startIcon={<Event />}>
          Ongeza Tukio
        </Button>
      </Box>

      {/* Stats */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Matukio ya Jumla
              </Typography>
              <Typography variant="h4" color="primary">
                {events.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Yameidhinishwa
              </Typography>
              <Typography variant="h4" color="success.main">
                {events.filter(e => e.status === 'APPROVED').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Yanasubiri
              </Typography>
              <Typography variant="h4" color="warning.main">
                {events.filter(e => e.status === 'PENDING').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Mapato ya Jumla
              </Typography>
              <Typography variant="h4" color="info.main">
                {formatCurrency(events.reduce((sum, e) => sum + e.revenue, 0))}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <TextField
            fullWidth
            placeholder="Tafuta matukio..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
          />
        </CardContent>
      </Card>

      {/* Events Table */}
      <Card>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Jina la Tukio</TableCell>
                <TableCell>Mpangaji</TableCell>
                <TableCell>Tarehe</TableCell>
                <TableCell>Mahali</TableCell>
                <TableCell>Hali</TableCell>
                <TableCell>Tiketi</TableCell>
                <TableCell>Mapato</TableCell>
                <TableCell>Vitendo</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {events
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((event) => (
                  <TableRow key={event.id}>
                    <TableCell>
                      <Typography variant="subtitle2">
                        {event.title}
                      </Typography>
                    </TableCell>
                    <TableCell>{event.organizer}</TableCell>
                    <TableCell>
                      {new Date(event.date).toLocaleDateString('sw-TZ')}
                    </TableCell>
                    <TableCell>{event.location}</TableCell>
                    <TableCell>
                      <Chip
                        label={getStatusLabel(event.status)}
                        color={getStatusColor(event.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {event.ticketsSold}/{event.totalTickets}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {formatCurrency(event.revenue)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <IconButton onClick={(e) => handleActionClick(e, event)}>
                        <MoreVert />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={events.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={(e, newPage) => setPage(newPage)}
          onRowsPerPageChange={(e) => {
            setRowsPerPage(parseInt(e.target.value, 10));
            setPage(0);
          }}
        />
      </Card>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleActionClose}
      >
        <MenuItem onClick={handleActionClose}>
          <Visibility sx={{ mr: 1 }} /> Angalia
        </MenuItem>
        <MenuItem onClick={handleActionClose}>
          <CheckCircle sx={{ mr: 1 }} /> Idhinisha
        </MenuItem>
        <MenuItem onClick={handleActionClose}>
          <Cancel sx={{ mr: 1 }} /> Kataa
        </MenuItem>
      </Menu>
    </Box>
  );
}
