package com.prochat.model;

import jakarta.persistence.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "live_streams")
@EntityListeners(AuditingEntityListener.class)
public class LiveStream {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "streamer_id", nullable = false)
    private User streamer;
    
    @Column(name = "title", nullable = false)
    private String title;
    
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    @Column(name = "stream_key", unique = true)
    private String streamKey;
    
    @Column(name = "stream_url")
    private String streamUrl;
    
    @Column(name = "thumbnail_url")
    private String thumbnailUrl;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "category")
    private StreamCategory category;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private StreamStatus status = StreamStatus.SCHEDULED;
    
    @Column(name = "is_private")
    private Boolean isPrivate = false;
    
    @Column(name = "password")
    private String password; // For private streams
    
    @Column(name = "max_viewers")
    private Integer maxViewers = 1000;
    
    @Column(name = "current_viewers")
    private Integer currentViewers = 0;
    
    @Column(name = "total_viewers")
    private Integer totalViewers = 0;
    
    @Column(name = "peak_viewers")
    private Integer peakViewers = 0;
    
    @Column(name = "likes_count")
    private Long likesCount = 0L;
    
    @Column(name = "comments_count")
    private Long commentsCount = 0L;
    
    @Column(name = "gifts_count")
    private Long giftsCount = 0L;
    
    @Column(name = "total_gifts_value", precision = 15, scale = 2)
    private BigDecimal totalGiftsValue = BigDecimal.ZERO;
    
    @Column(name = "scheduled_start")
    private LocalDateTime scheduledStart;
    
    @Column(name = "actual_start")
    private LocalDateTime actualStart;
    
    @Column(name = "ended_at")
    private LocalDateTime endedAt;
    
    @Column(name = "duration_minutes")
    private Integer durationMinutes = 0;
    
    @Column(name = "is_recorded")
    private Boolean isRecorded = true;
    
    @Column(name = "recording_url")
    private String recordingUrl;
    
    @Column(name = "quality_settings")
    private String qualitySettings; // JSON string with quality options
    
    @Column(name = "chat_enabled")
    private Boolean chatEnabled = true;
    
    @Column(name = "gifts_enabled")
    private Boolean giftsEnabled = true;
    
    @Column(name = "moderation_enabled")
    private Boolean moderationEnabled = true;
    
    @Column(name = "tags")
    private String tags; // Comma-separated tags
    
    @Column(name = "language")
    private String language = "sw"; // Default to Swahili
    
    @Column(name = "age_restriction")
    private String ageRestriction = "ALL"; // ALL, 13+, 16+, 18+
    
    @Column(name = "monetization_enabled")
    private Boolean monetizationEnabled = false;
    
    @Column(name = "subscription_required")
    private Boolean subscriptionRequired = false;
    
    @Column(name = "subscription_price", precision = 10, scale = 2)
    private BigDecimal subscriptionPrice = BigDecimal.ZERO;
    
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Constructors
    public LiveStream() {}
    
    public LiveStream(User streamer, String title, String description) {
        this.streamer = streamer;
        this.title = title;
        this.description = description;
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public User getStreamer() { return streamer; }
    public void setStreamer(User streamer) { this.streamer = streamer; }
    
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getStreamKey() { return streamKey; }
    public void setStreamKey(String streamKey) { this.streamKey = streamKey; }
    
    public String getStreamUrl() { return streamUrl; }
    public void setStreamUrl(String streamUrl) { this.streamUrl = streamUrl; }
    
    public String getThumbnailUrl() { return thumbnailUrl; }
    public void setThumbnailUrl(String thumbnailUrl) { this.thumbnailUrl = thumbnailUrl; }
    
    public StreamCategory getCategory() { return category; }
    public void setCategory(StreamCategory category) { this.category = category; }
    
    public StreamStatus getStatus() { return status; }
    public void setStatus(StreamStatus status) { this.status = status; }
    
    public Boolean getIsPrivate() { return isPrivate; }
    public void setIsPrivate(Boolean isPrivate) { this.isPrivate = isPrivate; }
    
    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }
    
    public Integer getMaxViewers() { return maxViewers; }
    public void setMaxViewers(Integer maxViewers) { this.maxViewers = maxViewers; }
    
    public Integer getCurrentViewers() { return currentViewers; }
    public void setCurrentViewers(Integer currentViewers) { 
        this.currentViewers = currentViewers;
        if (currentViewers > this.peakViewers) {
            this.peakViewers = currentViewers;
        }
    }
    
    public Integer getTotalViewers() { return totalViewers; }
    public void setTotalViewers(Integer totalViewers) { this.totalViewers = totalViewers; }
    
    public Integer getPeakViewers() { return peakViewers; }
    public void setPeakViewers(Integer peakViewers) { this.peakViewers = peakViewers; }
    
    public Long getLikesCount() { return likesCount; }
    public void setLikesCount(Long likesCount) { this.likesCount = likesCount; }
    
    public Long getCommentsCount() { return commentsCount; }
    public void setCommentsCount(Long commentsCount) { this.commentsCount = commentsCount; }
    
    public Long getGiftsCount() { return giftsCount; }
    public void setGiftsCount(Long giftsCount) { this.giftsCount = giftsCount; }
    
    public BigDecimal getTotalGiftsValue() { return totalGiftsValue; }
    public void setTotalGiftsValue(BigDecimal totalGiftsValue) { this.totalGiftsValue = totalGiftsValue; }
    
    public LocalDateTime getScheduledStart() { return scheduledStart; }
    public void setScheduledStart(LocalDateTime scheduledStart) { this.scheduledStart = scheduledStart; }
    
    public LocalDateTime getActualStart() { return actualStart; }
    public void setActualStart(LocalDateTime actualStart) { this.actualStart = actualStart; }
    
    public LocalDateTime getEndedAt() { return endedAt; }
    public void setEndedAt(LocalDateTime endedAt) { this.endedAt = endedAt; }
    
    public Integer getDurationMinutes() { return durationMinutes; }
    public void setDurationMinutes(Integer durationMinutes) { this.durationMinutes = durationMinutes; }
    
    public Boolean getIsRecorded() { return isRecorded; }
    public void setIsRecorded(Boolean isRecorded) { this.isRecorded = isRecorded; }
    
    public String getRecordingUrl() { return recordingUrl; }
    public void setRecordingUrl(String recordingUrl) { this.recordingUrl = recordingUrl; }
    
    public String getQualitySettings() { return qualitySettings; }
    public void setQualitySettings(String qualitySettings) { this.qualitySettings = qualitySettings; }
    
    public Boolean getChatEnabled() { return chatEnabled; }
    public void setChatEnabled(Boolean chatEnabled) { this.chatEnabled = chatEnabled; }
    
    public Boolean getGiftsEnabled() { return giftsEnabled; }
    public void setGiftsEnabled(Boolean giftsEnabled) { this.giftsEnabled = giftsEnabled; }
    
    public Boolean getModerationEnabled() { return moderationEnabled; }
    public void setModerationEnabled(Boolean moderationEnabled) { this.moderationEnabled = moderationEnabled; }
    
    public String getTags() { return tags; }
    public void setTags(String tags) { this.tags = tags; }
    
    public String getLanguage() { return language; }
    public void setLanguage(String language) { this.language = language; }
    
    public String getAgeRestriction() { return ageRestriction; }
    public void setAgeRestriction(String ageRestriction) { this.ageRestriction = ageRestriction; }
    
    public Boolean getMonetizationEnabled() { return monetizationEnabled; }
    public void setMonetizationEnabled(Boolean monetizationEnabled) { this.monetizationEnabled = monetizationEnabled; }
    
    public Boolean getSubscriptionRequired() { return subscriptionRequired; }
    public void setSubscriptionRequired(Boolean subscriptionRequired) { this.subscriptionRequired = subscriptionRequired; }
    
    public BigDecimal getSubscriptionPrice() { return subscriptionPrice; }
    public void setSubscriptionPrice(BigDecimal subscriptionPrice) { this.subscriptionPrice = subscriptionPrice; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
