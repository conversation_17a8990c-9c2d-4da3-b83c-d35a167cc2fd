package com.prochat.model;

import jakarta.persistence.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

@Entity
@Table(name = "channels")
@EntityListeners(AuditingEntityListener.class)
public class Channel {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "owner_id", nullable = false)
    private User owner;
    
    @Column(name = "name", nullable = false)
    private String name;
    
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    @Column(name = "channel_handle", unique = true)
    private String channelHandle; // @prochat_news
    
    @Column(name = "avatar_url")
    private String avatarUrl;
    
    @Column(name = "cover_image_url")
    private String coverImageUrl;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "category")
    private ChannelCategory category;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "privacy_type")
    private ChannelPrivacy privacyType = ChannelPrivacy.PUBLIC;
    
    @Column(name = "is_verified")
    private Boolean isVerified = false;
    
    @Column(name = "is_official")
    private Boolean isOfficial = false;
    
    @Column(name = "subscribers_count")
    private Long subscribersCount = 0L;
    
    @Column(name = "posts_count")
    private Long postsCount = 0L;
    
    @Column(name = "total_views")
    private Long totalViews = 0L;
    
    @Column(name = "is_monetized")
    private Boolean isMonetized = false;
    
    @Column(name = "subscription_fee", precision = 10, scale = 2)
    private java.math.BigDecimal subscriptionFee = java.math.BigDecimal.ZERO;
    
    @Column(name = "invite_link")
    private String inviteLink;
    
    @Column(name = "rules", columnDefinition = "TEXT")
    private String rules;
    
    @Column(name = "contact_info")
    private String contactInfo;
    
    @Column(name = "website_url")
    private String websiteUrl;
    
    @Column(name = "location")
    private String location;
    
    @Column(name = "language")
    private String language = "sw";
    
    @Column(name = "tags")
    private String tags; // Comma-separated
    
    @Column(name = "is_active")
    private Boolean isActive = true;
    
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Constructors
    public Channel() {}
    
    public Channel(User owner, String name, String description) {
        this.owner = owner;
        this.name = name;
        this.description = description;
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public User getOwner() { return owner; }
    public void setOwner(User owner) { this.owner = owner; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getChannelHandle() { return channelHandle; }
    public void setChannelHandle(String channelHandle) { this.channelHandle = channelHandle; }
    
    public String getAvatarUrl() { return avatarUrl; }
    public void setAvatarUrl(String avatarUrl) { this.avatarUrl = avatarUrl; }
    
    public String getCoverImageUrl() { return coverImageUrl; }
    public void setCoverImageUrl(String coverImageUrl) { this.coverImageUrl = coverImageUrl; }
    
    public ChannelCategory getCategory() { return category; }
    public void setCategory(ChannelCategory category) { this.category = category; }
    
    public ChannelPrivacy getPrivacyType() { return privacyType; }
    public void setPrivacyType(ChannelPrivacy privacyType) { this.privacyType = privacyType; }
    
    public Boolean getIsVerified() { return isVerified; }
    public void setIsVerified(Boolean isVerified) { this.isVerified = isVerified; }
    
    public Boolean getIsOfficial() { return isOfficial; }
    public void setIsOfficial(Boolean isOfficial) { this.isOfficial = isOfficial; }
    
    public Long getSubscribersCount() { return subscribersCount; }
    public void setSubscribersCount(Long subscribersCount) { this.subscribersCount = subscribersCount; }
    
    public Long getPostsCount() { return postsCount; }
    public void setPostsCount(Long postsCount) { this.postsCount = postsCount; }
    
    public Long getTotalViews() { return totalViews; }
    public void setTotalViews(Long totalViews) { this.totalViews = totalViews; }
    
    public Boolean getIsMonetized() { return isMonetized; }
    public void setIsMonetized(Boolean isMonetized) { this.isMonetized = isMonetized; }
    
    public java.math.BigDecimal getSubscriptionFee() { return subscriptionFee; }
    public void setSubscriptionFee(java.math.BigDecimal subscriptionFee) { this.subscriptionFee = subscriptionFee; }
    
    public String getInviteLink() { return inviteLink; }
    public void setInviteLink(String inviteLink) { this.inviteLink = inviteLink; }
    
    public String getRules() { return rules; }
    public void setRules(String rules) { this.rules = rules; }
    
    public String getContactInfo() { return contactInfo; }
    public void setContactInfo(String contactInfo) { this.contactInfo = contactInfo; }
    
    public String getWebsiteUrl() { return websiteUrl; }
    public void setWebsiteUrl(String websiteUrl) { this.websiteUrl = websiteUrl; }
    
    public String getLocation() { return location; }
    public void setLocation(String location) { this.location = location; }
    
    public String getLanguage() { return language; }
    public void setLanguage(String language) { this.language = language; }
    
    public String getTags() { return tags; }
    public void setTags(String tags) { this.tags = tags; }
    
    public Boolean getIsActive() { return isActive; }
    public void setIsActive(Boolean isActive) { this.isActive = isActive; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
