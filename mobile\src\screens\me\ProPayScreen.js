import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  RefreshControl,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors, typography, spacing } from '../../theme/theme';
import { walletAPI, transactionAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

export default function ProPayScreen({ navigation }) {
  const { user } = useAuth();
  const [wallet, setWallet] = useState(null);
  const [recentTransactions, setRecentTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadWalletData();
  }, []);

  const loadWalletData = async () => {
    try {
      const [walletResponse, transactionsResponse] = await Promise.all([
        walletAPI.getBalance(),
        transactionAPI.getRecentTransactions(5)
      ]);

      if (walletResponse.success) {
        setWallet(walletResponse.data);
      }

      if (transactionsResponse.success) {
        setRecentTransactions(transactionsResponse.data);
      }
    } catch (error) {
      console.error('Error loading wallet data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadWalletData();
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getTransactionIcon = (type) => {
    switch (type) {
      case 'DEPOSIT': return 'add-circle';
      case 'WITHDRAWAL': return 'remove-circle';
      case 'TRANSFER': return 'swap-horiz';
      case 'PAYMENT': return 'payment';
      case 'GIFT': return 'card-giftcard';
      case 'REWARD': return 'emoji-events';
      default: return 'account-balance-wallet';
    }
  };

  const getTransactionColor = (type) => {
    switch (type) {
      case 'DEPOSIT':
      case 'REWARD': return colors.success;
      case 'WITHDRAWAL':
      case 'PAYMENT': return colors.error;
      case 'TRANSFER': return colors.info;
      case 'GIFT': return colors.warning;
      default: return colors.gray;
    }
  };

  const quickActions = [
    {
      title: 'Tuma Pesa',
      icon: 'send',
      color: colors.primary,
      onPress: () => navigation.navigate('SendMoney'),
    },
    {
      title: 'Pokea Pesa',
      icon: 'qr-code',
      color: colors.success,
      onPress: () => navigation.navigate('ReceiveMoney'),
    },
    {
      title: 'Lipa Bill',
      icon: 'receipt',
      color: colors.warning,
      onPress: () => navigation.navigate('PayBill'),
    },
    {
      title: 'Nunua Airtime',
      icon: 'phone',
      color: colors.info,
      onPress: () => navigation.navigate('BuyAirtime'),
    },
  ];

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Inapakia ProPay...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color={colors.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>ProPay Wallet</Text>
        <TouchableOpacity style={styles.settingsButton}>
          <Icon name="settings" size={24} color={colors.white} />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Balance Card */}
        <View style={styles.balanceCard}>
          <View style={styles.balanceHeader}>
            <Text style={styles.balanceLabel}>Salio Lako</Text>
            <TouchableOpacity>
              <Icon name="visibility" size={20} color={colors.white} />
            </TouchableOpacity>
          </View>
          
          <Text style={styles.balanceAmount}>
            {formatCurrency(wallet?.balance || 0)}
          </Text>
          
          <View style={styles.balanceFooter}>
            <Text style={styles.accountNumber}>
              Akaunti: {wallet?.accountNumber || 'N/A'}
            </Text>
            <View style={styles.verificationBadge}>
              <Icon name="verified" size={16} color={colors.success} />
              <Text style={styles.verificationText}>Imethibitishwa</Text>
            </View>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActionsSection}>
          <Text style={styles.sectionTitle}>Vitendo vya Haraka</Text>
          <View style={styles.quickActions}>
            {quickActions.map((action, index) => (
              <TouchableOpacity
                key={index}
                style={styles.quickAction}
                onPress={action.onPress}
              >
                <View style={[
                  styles.quickActionIcon,
                  { backgroundColor: action.color + '20' }
                ]}>
                  <Icon name={action.icon} size={24} color={action.color} />
                </View>
                <Text style={styles.quickActionText}>{action.title}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Recent Transactions */}
        <View style={styles.transactionsSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Miamala ya Hivi Karibuni</Text>
            <TouchableOpacity
              onPress={() => navigation.navigate('TransactionHistory')}
            >
              <Text style={styles.seeAllText}>Ona Zote</Text>
            </TouchableOpacity>
          </View>

          {recentTransactions.length > 0 ? (
            recentTransactions.map((transaction, index) => (
              <View key={index} style={styles.transactionItem}>
                <View style={[
                  styles.transactionIcon,
                  { backgroundColor: getTransactionColor(transaction.type) + '20' }
                ]}>
                  <Icon 
                    name={getTransactionIcon(transaction.type)} 
                    size={20} 
                    color={getTransactionColor(transaction.type)} 
                  />
                </View>
                
                <View style={styles.transactionDetails}>
                  <Text style={styles.transactionTitle}>
                    {transaction.description}
                  </Text>
                  <Text style={styles.transactionDate}>
                    {new Date(transaction.createdAt).toLocaleDateString('sw-TZ')}
                  </Text>
                </View>
                
                <Text style={[
                  styles.transactionAmount,
                  { 
                    color: transaction.type === 'DEPOSIT' || transaction.type === 'REWARD' 
                      ? colors.success 
                      : colors.error 
                  }
                ]}>
                  {transaction.type === 'DEPOSIT' || transaction.type === 'REWARD' ? '+' : '-'}
                  {formatCurrency(transaction.amount)}
                </Text>
              </View>
            ))
          ) : (
            <View style={styles.emptyTransactions}>
              <Icon name="receipt-long" size={48} color={colors.gray} />
              <Text style={styles.emptyText}>Hakuna miamala bado</Text>
            </View>
          )}
        </View>

        {/* Services */}
        <View style={styles.servicesSection}>
          <Text style={styles.sectionTitle}>Huduma za ProPay</Text>
          
          <TouchableOpacity style={styles.serviceItem}>
            <Icon name="savings" size={24} color={colors.primary} />
            <View style={styles.serviceContent}>
              <Text style={styles.serviceTitle}>Akiba ya ProPay</Text>
              <Text style={styles.serviceDescription}>
                Okoa pesa na upate riba ya 5% kwa mwaka
              </Text>
            </View>
            <Icon name="chevron-right" size={24} color={colors.gray} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.serviceItem}>
            <Icon name="credit-card" size={24} color={colors.primary} />
            <View style={styles.serviceContent}>
              <Text style={styles.serviceTitle}>Kadi ya ProPay</Text>
              <Text style={styles.serviceDescription}>
                Omba kadi yako ya ProPay kwa matumizi ya kila siku
              </Text>
            </View>
            <Icon name="chevron-right" size={24} color={colors.gray} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.serviceItem}>
            <Icon name="trending-up" size={24} color={colors.primary} />
            <View style={styles.serviceContent}>
              <Text style={styles.serviceTitle}>Uwekezaji wa ProPay</Text>
              <Text style={styles.serviceDescription}>
                Wekeza pesa zako na upate mapato ya ziada
              </Text>
            </View>
            <Icon name="chevron-right" size={24} color={colors.gray} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.serviceItem}>
            <Icon name="security" size={24} color={colors.primary} />
            <View style={styles.serviceContent}>
              <Text style={styles.serviceTitle}>Bima ya ProPay</Text>
              <Text style={styles.serviceDescription}>
                Linda pesa zako na familia yako
              </Text>
            </View>
            <Icon name="chevron-right" size={24} color={colors.gray} />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
    backgroundColor: colors.primary,
  },
  backButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.white,
  },
  settingsButton: {
    padding: spacing.sm,
  },
  content: {
    flex: 1,
  },
  balanceCard: {
    backgroundColor: colors.primary,
    margin: spacing.lg,
    padding: spacing.xl,
    borderRadius: 16,
    elevation: 4,
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  balanceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  balanceLabel: {
    fontSize: typography.fontSize.md,
    color: colors.white,
    opacity: 0.9,
  },
  balanceAmount: {
    fontSize: typography.fontSize.xxxl,
    fontWeight: typography.fontWeight.bold,
    color: colors.white,
    marginBottom: spacing.lg,
  },
  balanceFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  accountNumber: {
    fontSize: typography.fontSize.sm,
    color: colors.white,
    opacity: 0.8,
  },
  verificationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  verificationText: {
    fontSize: typography.fontSize.xs,
    color: colors.white,
    marginLeft: spacing.xs,
  },
  quickActionsSection: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginBottom: spacing.sm,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickAction: {
    alignItems: 'center',
    flex: 1,
  },
  quickActionIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  quickActionText: {
    fontSize: typography.fontSize.sm,
    color: colors.text,
    textAlign: 'center',
  },
  transactionsSection: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginBottom: spacing.sm,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  seeAllText: {
    fontSize: typography.fontSize.sm,
    color: colors.primary,
    fontWeight: typography.fontWeight.medium,
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  transactionDetails: {
    flex: 1,
  },
  transactionTitle: {
    fontSize: typography.fontSize.md,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  transactionDate: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
  transactionAmount: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
  },
  emptyTransactions: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  emptyText: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
    marginTop: spacing.sm,
  },
  servicesSection: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginBottom: spacing.xl,
  },
  serviceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  serviceContent: {
    flex: 1,
    marginLeft: spacing.md,
  },
  serviceTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  serviceDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    lineHeight: 18,
  },
});
