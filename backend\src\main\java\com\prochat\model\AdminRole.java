package com.prochat.model;

public enum AdminRole {
    SUPER_ADMIN,        // Full system control
    MODERATOR,          // Content review and user reports
    FINANCE_OFFICER,    // Manages financial operations (ProPay)
    SUPPORT_TEAM,       // Handles help tickets and abuse reports
    RECRUITER,          // Manages job/tender posts and applications
    EVENT_OFFICER,      // Oversees event submissions and ticketing
    JOURNALIST_ADMIN,   // Reviews and approves news content
    SYSTEM_ADMIN,       // Technical system management
    MARKETING_ADMIN,    // Advertisement and promotion management
    ANALYTICS_ADMIN     // Data analysis and reporting
}
