import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  IconButton,
  Button,
} from '@mui/material';
import {
  People,
  AccountBalance,
  Event,
  Support,
  TrendingUp,
  TrendingDown,
  Refresh,
  Warning,
  CheckCircle,
  Error,
  Info,
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Bar<PERSON>hart, Bar, <PERSON>C<PERSON>, Pie, Cell } from 'recharts';

// Mock data - replace with actual API calls
const mockStats = {
  totalUsers: 125430,
  newUsersToday: 234,
  totalRevenue: ********,
  revenueToday: 123450,
  totalEvents: 1234,
  activeEvents: 89,
  pendingTickets: 23,
  resolvedTickets: 156,
};

const mockUserGrowth = [
  { month: 'Jan', users: 10000, revenue: 2000000 },
  { month: 'Feb', users: 15000, revenue: 3200000 },
  { month: 'Mar', users: 22000, revenue: 4800000 },
  { month: 'Apr', users: 35000, revenue: 7200000 },
  { month: 'May', users: 48000, revenue: 9800000 },
  { month: 'Jun', users: 65000, revenue: ******** },
];

const mockTransactionTypes = [
  { name: 'Miamala ya ProPay', value: 45, color: '#007AFF' },
  { name: 'Tiketi za Matukio', value: 25, color: '#4CAF50' },
  { name: 'Zawadi za Live Stream', value: 20, color: '#FF9800' },
  { name: 'Matangazo', value: 10, color: '#9C27B0' },
];

const mockRecentActivities = [
  { id: 1, type: 'user', message: 'Mtumiaji mpya amesajiliwa: John Doe', time: '2 dakika zilizopita', severity: 'success' },
  { id: 2, type: 'transaction', message: 'Muamala mkubwa: TSH 500,000', time: '5 dakika zilizopita', severity: 'info' },
  { id: 3, type: 'event', message: 'Tukio jipya limeongezwa: Muziki wa Bongo', time: '10 dakika zilizopita', severity: 'success' },
  { id: 4, type: 'support', message: 'Tiketi ya msaada: Tatizo la malipo', time: '15 dakika zilizopita', severity: 'warning' },
  { id: 5, type: 'security', message: 'Jaribio la kuingia kisicho halali', time: '20 dakika zilizopita', severity: 'error' },
];

export default function Dashboard() {
  const [stats, setStats] = useState(mockStats);
  const [loading, setLoading] = useState(false);

  const refreshData = async () => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setStats({
        ...mockStats,
        newUsersToday: Math.floor(Math.random() * 500) + 100,
        revenueToday: Math.floor(Math.random() * 200000) + 50000,
      });
      setLoading(false);
    }, 1000);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatNumber = (number) => {
    return new Intl.NumberFormat('sw-TZ').format(number);
  };

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'success': return <CheckCircle color="success" />;
      case 'warning': return <Warning color="warning" />;
      case 'error': return <Error color="error" />;
      default: return <Info color="info" />;
    }
  };

  const StatCard = ({ title, value, subtitle, icon, trend, color = 'primary' }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography color="textSecondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div" color={`${color}.main`}>
              {value}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              {trend > 0 ? (
                <TrendingUp color="success" sx={{ mr: 0.5 }} />
              ) : (
                <TrendingDown color="error" sx={{ mr: 0.5 }} />
              )}
              <Typography variant="body2" color={trend > 0 ? 'success.main' : 'error.main'}>
                {subtitle}
              </Typography>
            </Box>
          </Box>
          <Box sx={{ color: `${color}.main` }}>
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Dashboard
        </Typography>
        <Button
          variant="outlined"
          startIcon={<Refresh />}
          onClick={refreshData}
          disabled={loading}
        >
          Sasisha Data
        </Button>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Jumla ya Watumiaji"
            value={formatNumber(stats.totalUsers)}
            subtitle={`+${stats.newUsersToday} leo`}
            icon={<People sx={{ fontSize: 40 }} />}
            trend={1}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Mapato ya Jumla"
            value={formatCurrency(stats.totalRevenue)}
            subtitle={`+${formatCurrency(stats.revenueToday)} leo`}
            icon={<AccountBalance sx={{ fontSize: 40 }} />}
            trend={1}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Matukio"
            value={formatNumber(stats.totalEvents)}
            subtitle={`${stats.activeEvents} yanayoendelea`}
            icon={<Event sx={{ fontSize: 40 }} />}
            trend={1}
            color="warning"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Tiketi za Msaada"
            value={stats.pendingTickets}
            subtitle={`${stats.resolvedTickets} zimetatuliwa`}
            icon={<Support sx={{ fontSize: 40 }} />}
            trend={-1}
            color="error"
          />
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {/* User Growth Chart */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Ukuaji wa Watumiaji na Mapato
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={mockUserGrowth}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip 
                  formatter={(value, name) => [
                    name === 'users' ? formatNumber(value) : formatCurrency(value),
                    name === 'users' ? 'Watumiaji' : 'Mapato'
                  ]}
                />
                <Line yAxisId="left" type="monotone" dataKey="users" stroke="#007AFF" strokeWidth={3} />
                <Line yAxisId="right" type="monotone" dataKey="revenue" stroke="#4CAF50" strokeWidth={3} />
              </LineChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Transaction Types */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Aina za Miamala
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={mockTransactionTypes}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {mockTransactionTypes.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>

      {/* Recent Activities */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Shughuli za Hivi Karibuni
        </Typography>
        <List>
          {mockRecentActivities.map((activity) => (
            <ListItem key={activity.id} divider>
              <ListItemIcon>
                {getSeverityIcon(activity.severity)}
              </ListItemIcon>
              <ListItemText
                primary={activity.message}
                secondary={activity.time}
              />
              <Chip
                label={activity.type}
                size="small"
                color={activity.severity === 'error' ? 'error' : 'default'}
              />
            </ListItem>
          ))}
        </List>
      </Paper>
    </Box>
  );
}
