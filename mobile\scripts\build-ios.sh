#!/bin/bash

# ProChat iOS Build Script
# This script builds ProChat for iOS

set -e

echo "🍎 Starting ProChat iOS Build Process..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    print_warning "iOS builds are recommended on macOS for best results"
fi

# Check if we're in the mobile directory
if [ ! -f "package.json" ]; then
    print_error "Please run this script from the mobile directory"
    exit 1
fi

# Check if EAS CLI is installed
if ! command -v eas &> /dev/null; then
    print_warning "EAS CLI not found. Installing..."
    npm install -g @expo/eas-cli
fi

# Login to EAS (if not already logged in)
print_status "Checking EAS authentication..."
if ! eas whoami &> /dev/null; then
    print_warning "Please login to EAS:"
    eas login
fi

# Install dependencies
print_status "Installing dependencies..."
npm install

# Clean previous builds
print_status "Cleaning previous builds..."
rm -rf dist/
mkdir -p dist/

# Set production environment
export NODE_ENV=production
cp .env.production .env

print_status "Building ProChat for iOS..."

# Build options
BUILD_TYPE=${1:-"production"}

case $BUILD_TYPE in
    "production")
        print_status "Building iOS app for App Store distribution..."
        eas build --platform ios --profile production
        ;;
    "preview")
        print_status "Building iOS app for internal testing..."
        eas build --platform ios --profile preview
        ;;
    "development")
        print_status "Building development client for iOS..."
        eas build --platform ios --profile development
        ;;
    *)
        print_error "Invalid build type. Use: production, preview, or development"
        exit 1
        ;;
esac

print_success "iOS build process completed!"

# Download builds
print_status "Downloading builds to dist/ folder..."
eas build:list --platform ios --limit 5

print_status "To download your builds, use:"
echo "eas build:download [BUILD_ID]"

# Generate build info
cat > dist/ios-build-info.txt << EOF
ProChat iOS Build Information
============================

Build Date: $(date)
Build Type: $BUILD_TYPE
Bundle Identifier: com.prochat.app
Version: 1.0.0
Build Number: 1.0.0

Files Generated:
- ProChat.ipa (for App Store or TestFlight)

Installation Instructions:
1. For App Store: Upload .ipa to App Store Connect
2. For TestFlight: Upload .ipa to TestFlight for beta testing
3. For device install: Use Xcode or Apple Configurator

Build Commands Used:
- Production: eas build --platform ios --profile production
- Preview: eas build --platform ios --profile preview

iOS Requirements:
- Minimum iOS version: 13.0
- Supported devices: iPhone 8 and newer, iPad (6th gen) and newer
- Required capabilities: Camera, Microphone, Location, Push Notifications

App Store Connect Setup:
1. Create app record in App Store Connect
2. Configure app information and metadata
3. Upload screenshots and app preview video
4. Set pricing and availability
5. Submit for review

TestFlight Distribution:
1. Upload build to App Store Connect
2. Add internal/external testers
3. Distribute beta versions for testing

Next Steps:
1. Download your .ipa file
2. Test on physical iOS devices
3. Upload to App Store Connect
4. Configure app store listing
5. Submit for App Store review

Support: <EMAIL>
Documentation: https://docs.expo.dev/build/introduction/
EOF

print_success "iOS build information saved to dist/ios-build-info.txt"

echo ""
echo "🎉 ProChat iOS build completed successfully!"
echo "📱 Your app is ready for App Store distribution!"
echo ""
echo "Next steps:"
echo "1. Download your .ipa using: eas build:download [BUILD_ID]"
echo "2. Test on iOS devices"
echo "3. Upload to App Store Connect"
echo "4. Configure app store listing"
echo "5. Submit for review"
echo ""
echo "For help: https://docs.expo.dev/submit/ios/"
