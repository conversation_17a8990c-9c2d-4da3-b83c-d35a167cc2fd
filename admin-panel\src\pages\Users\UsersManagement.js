import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON>graphy,
  Button,
  TextField,
  Grid,
  Chip,
  Avatar,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  InputAdornment,
} from '@mui/material';
import {
  Search,
  FilterList,
  MoreVert,
  Block,
  CheckCircle,
  Person,
  Business,
  Verified,
  Edit,
  Delete,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

export default function UsersManagement() {
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [selectedUser, setSelectedUser] = useState(null);
  const [actionMenuAnchor, setActionMenuAnchor] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const { hasPermission } = useAuth();

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockUsers = [
      {
        id: 1,
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phoneNumber: '+255123456789',
        isVerified: true,
        isActive: true,
        userType: 'REGULAR',
        walletBalance: 150000,
        joinedDate: '2024-01-15',
        lastActive: '2024-01-20',
        profileImageUrl: null,
      },
      {
        id: 2,
        firstName: 'Mary',
        lastName: 'Johnson',
        email: '<EMAIL>',
        phoneNumber: '+255987654321',
        isVerified: true,
        isActive: true,
        userType: 'BUSINESS',
        walletBalance: 500000,
        joinedDate: '2024-01-10',
        lastActive: '2024-01-20',
        profileImageUrl: null,
      },
      {
        id: 3,
        firstName: 'Peter',
        lastName: 'Smith',
        email: '<EMAIL>',
        phoneNumber: '+255555666777',
        isVerified: false,
        isActive: false,
        userType: 'REGULAR',
        walletBalance: 25000,
        joinedDate: '2024-01-18',
        lastActive: '2024-01-19',
        profileImageUrl: null,
      },
    ];
    setUsers(mockUsers);
    setFilteredUsers(mockUsers);
  }, []);

  useEffect(() => {
    const filtered = users.filter(user =>
      user.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.phoneNumber.includes(searchQuery)
    );
    setFilteredUsers(filtered);
  }, [searchQuery, users]);

  const handleActionClick = (event, user) => {
    setSelectedUser(user);
    setActionMenuAnchor(event.currentTarget);
  };

  const handleActionClose = () => {
    setActionMenuAnchor(null);
    setSelectedUser(null);
  };

  const handleUserAction = (action) => {
    if (!selectedUser) return;
    
    switch (action) {
      case 'verify':
        // Implement verify user
        console.log('Verify user:', selectedUser.id);
        break;
      case 'block':
        // Implement block user
        console.log('Block user:', selectedUser.id);
        break;
      case 'edit':
        setDialogOpen(true);
        break;
      case 'delete':
        // Implement delete user
        console.log('Delete user:', selectedUser.id);
        break;
      default:
        break;
    }
    handleActionClose();
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getUserTypeColor = (type) => {
    switch (type) {
      case 'BUSINESS': return 'primary';
      case 'AGENT': return 'secondary';
      case 'MERCHANT': return 'success';
      default: return 'default';
    }
  };

  const getUserTypeIcon = (type) => {
    switch (type) {
      case 'BUSINESS': return <Business />;
      case 'AGENT': return <Person />;
      case 'MERCHANT': return <Business />;
      default: return <Person />;
    }
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Uongozi wa Watumiaji
        </Typography>
        {hasPermission('EDIT_USERS') && (
          <Button variant="contained" startIcon={<Person />}>
            Ongeza Mtumiaji
          </Button>
        )}
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Jumla ya Watumiaji
              </Typography>
              <Typography variant="h4" color="primary">
                {users.length.toLocaleString()}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Watumiaji Hai
              </Typography>
              <Typography variant="h4" color="success.main">
                {users.filter(u => u.isActive).length.toLocaleString()}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Waliothibitishwa
              </Typography>
              <Typography variant="h4" color="info.main">
                {users.filter(u => u.isVerified).length.toLocaleString()}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Wafanyabiashara
              </Typography>
              <Typography variant="h4" color="warning.main">
                {users.filter(u => u.userType === 'BUSINESS').length.toLocaleString()}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Search and Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Tafuta watumiaji..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Button
                variant="outlined"
                startIcon={<FilterList />}
                sx={{ mr: 2 }}
              >
                Chuja
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Mtumiaji</TableCell>
                <TableCell>Aina</TableCell>
                <TableCell>Hali</TableCell>
                <TableCell>Salio la Wallet</TableCell>
                <TableCell>Tarehe ya Kujiunga</TableCell>
                <TableCell>Vitendo</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredUsers
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar
                          src={user.profileImageUrl}
                          sx={{ mr: 2 }}
                        >
                          {user.firstName[0]}{user.lastName[0]}
                        </Avatar>
                        <Box>
                          <Typography variant="subtitle2">
                            {user.firstName} {user.lastName}
                            {user.isVerified && (
                              <Verified sx={{ ml: 1, fontSize: 16, color: 'primary.main' }} />
                            )}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {user.email}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {user.phoneNumber}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        icon={getUserTypeIcon(user.userType)}
                        label={user.userType}
                        color={getUserTypeColor(user.userType)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={user.isActive ? 'Hai' : 'Haijafungwa'}
                        color={user.isActive ? 'success' : 'error'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {formatCurrency(user.walletBalance)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(user.joinedDate).toLocaleDateString('sw-TZ')}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <IconButton
                        onClick={(e) => handleActionClick(e, user)}
                        disabled={!hasPermission('EDIT_USERS')}
                      >
                        <MoreVert />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={filteredUsers.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={(e, newPage) => setPage(newPage)}
          onRowsPerPageChange={(e) => {
            setRowsPerPage(parseInt(e.target.value, 10));
            setPage(0);
          }}
        />
      </Card>

      {/* Action Menu */}
      <Menu
        anchorEl={actionMenuAnchor}
        open={Boolean(actionMenuAnchor)}
        onClose={handleActionClose}
      >
        <MenuItem onClick={() => handleUserAction('edit')}>
          <Edit sx={{ mr: 1 }} /> Hariri
        </MenuItem>
        <MenuItem onClick={() => handleUserAction('verify')}>
          <CheckCircle sx={{ mr: 1 }} /> Thibitisha
        </MenuItem>
        <MenuItem onClick={() => handleUserAction('block')}>
          <Block sx={{ mr: 1 }} /> Zuia
        </MenuItem>
        <MenuItem onClick={() => handleUserAction('delete')}>
          <Delete sx={{ mr: 1 }} /> Futa
        </MenuItem>
      </Menu>

      {/* Edit User Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Hariri Mtumiaji</DialogTitle>
        <DialogContent>
          {/* Add user edit form here */}
          <Typography>Fomu ya kuhariri mtumiaji itaongezwa hapa</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Ghairi</Button>
          <Button variant="contained">Hifadhi</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
