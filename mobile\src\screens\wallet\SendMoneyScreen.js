import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors, typography, spacing } from '../../theme/theme';
import { walletAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

export default function SendMoneyScreen({ navigation }) {
  const { user } = useAuth();
  const [amount, setAmount] = useState('');
  const [recipientWallet, setRecipientWallet] = useState('');
  const [description, setDescription] = useState('');
  const [vatCalculation, setVatCalculation] = useState(null);
  const [loading, setLoading] = useState(false);
  const [calculating, setCalculating] = useState(false);

  useEffect(() => {
    if (amount && parseFloat(amount) >= 1000) {
      calculateVAT();
    } else {
      setVatCalculation(null);
    }
  }, [amount]);

  const calculateVAT = async () => {
    try {
      setCalculating(true);
      const response = await walletAPI.calculateVAT(
        parseFloat(amount), 
        'SEND_MONEY'
      );
      
      if (response.success) {
        setVatCalculation(response.data);
      }
    } catch (error) {
      console.error('VAT calculation error:', error);
    } finally {
      setCalculating(false);
    }
  };

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(value);
  };

  const validateForm = () => {
    if (!amount || parseFloat(amount) < 1000) {
      Alert.alert('Hitilafu', 'Kiwango cha chini ni 1,000 TZS');
      return false;
    }
    
    if (!recipientWallet || recipientWallet.length < 10) {
      Alert.alert('Hitilafu', 'Ingiza namba sahihi ya wallet');
      return false;
    }
    
    return true;
  };

  const handleSendMoney = async () => {
    if (!validateForm()) return;

    Alert.alert(
      'Thibitisha Muamala',
      `Unataka kutuma ${formatCurrency(parseFloat(amount))} kwenda ${recipientWallet}?\n\n` +
      `VAT: ${vatCalculation ? formatCurrency(vatCalculation.vatAmount) : '0'}\n` +
      `Atapokea: ${vatCalculation ? formatCurrency(vatCalculation.netAmount) : formatCurrency(parseFloat(amount))}`,
      [
        { text: 'Ghairi', style: 'cancel' },
        { text: 'Thibitisha', onPress: processSendMoney }
      ]
    );
  };

  const processSendMoney = async () => {
    try {
      setLoading(true);
      
      const response = await walletAPI.sendMoney({
        receiverWalletNumber: recipientWallet,
        amount: parseFloat(amount),
        description: description || 'Kutuma pesa'
      });

      if (response.success) {
        Alert.alert(
          'Mafanikio!',
          `Pesa imetumwa kwa mafanikio!\n\nReference: ${response.data.reference}`,
          [
            {
              text: 'Sawa',
              onPress: () => navigation.goBack()
            }
          ]
        );
      } else {
        Alert.alert('Hitilafu', response.message || 'Imeshindwa kutuma pesa');
      }
    } catch (error) {
      console.error('Send money error:', error);
      Alert.alert('Hitilafu', error.message || 'Hitilafu ya mtandao');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Tuma Pesa</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content}>
        {/* Recipient Input */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Namba ya Wallet ya Mpokeaji</Text>
          <View style={styles.inputWrapper}>
            <Icon name="account-balance-wallet" size={20} color={colors.gray} />
            <TextInput
              style={styles.input}
              placeholder="PW123456789"
              value={recipientWallet}
              onChangeText={setRecipientWallet}
              keyboardType="default"
              maxLength={15}
            />
          </View>
        </View>

        {/* Amount Input */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Kiasi (TZS)</Text>
          <View style={styles.inputWrapper}>
            <Icon name="money" size={20} color={colors.gray} />
            <TextInput
              style={styles.input}
              placeholder="10,000"
              value={amount}
              onChangeText={setAmount}
              keyboardType="numeric"
            />
          </View>
          <Text style={styles.helperText}>Kiwango cha chini: 1,000 TZS</Text>
        </View>

        {/* VAT Calculation Display */}
        {calculating && (
          <View style={styles.calculatingContainer}>
            <ActivityIndicator size="small" color={colors.primary} />
            <Text style={styles.calculatingText}>Inahesabu VAT...</Text>
          </View>
        )}

        {vatCalculation && (
          <View style={styles.vatContainer}>
            <Text style={styles.vatTitle}>Muhtasari wa Muamala</Text>
            
            <View style={styles.vatRow}>
              <Text style={styles.vatLabel}>Kiasi ulichoingiza:</Text>
              <Text style={styles.vatValue}>
                {formatCurrency(vatCalculation.grossAmount)}
              </Text>
            </View>
            
            <View style={styles.vatRow}>
              <Text style={styles.vatLabel}>VAT ({vatCalculation.vatRate}%):</Text>
              <Text style={[styles.vatValue, { color: colors.error }]}>
                -{formatCurrency(vatCalculation.vatAmount)}
              </Text>
            </View>
            
            <View style={styles.divider} />
            
            <View style={styles.vatRow}>
              <Text style={styles.vatLabelTotal}>Atapokea:</Text>
              <Text style={styles.vatValueTotal}>
                {formatCurrency(vatCalculation.netAmount)}
              </Text>
            </View>
          </View>
        )}

        {/* Description Input */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Maelezo (si lazima)</Text>
          <View style={styles.inputWrapper}>
            <Icon name="description" size={20} color={colors.gray} />
            <TextInput
              style={styles.input}
              placeholder="Maelezo ya muamala..."
              value={description}
              onChangeText={setDescription}
              multiline
              numberOfLines={2}
            />
          </View>
        </View>

        {/* Send Button */}
        <TouchableOpacity
          style={[
            styles.sendButton,
            (!amount || parseFloat(amount) < 1000 || !recipientWallet) && styles.sendButtonDisabled
          ]}
          onPress={handleSendMoney}
          disabled={loading || !amount || parseFloat(amount) < 1000 || !recipientWallet}
        >
          {loading ? (
            <ActivityIndicator size="small" color={colors.white} />
          ) : (
            <>
              <Icon name="send" size={20} color={colors.white} />
              <Text style={styles.sendButtonText}>Tuma Pesa</Text>
            </>
          )}
        </TouchableOpacity>

        {/* Info Card */}
        <View style={styles.infoCard}>
          <Icon name="info" size={20} color={colors.info} />
          <View style={styles.infoContent}>
            <Text style={styles.infoTitle}>Taarifa Muhimu</Text>
            <Text style={styles.infoText}>
              • VAT ya 18% itatozwa kwa miamala yote{'\n'}
              • Pesa itafika kwa dakika chache{'\n'}
              • Hakikisha namba ya wallet ni sahihi{'\n'}
              • Muamala hauwezi kughairiwa
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  inputContainer: {
    marginTop: spacing.lg,
  },
  label: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 12,
    paddingHorizontal: spacing.md,
    backgroundColor: colors.white,
  },
  input: {
    flex: 1,
    paddingVertical: spacing.md,
    paddingLeft: spacing.sm,
    fontSize: typography.fontSize.md,
    color: colors.text,
  },
  helperText: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  calculatingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
  },
  calculatingText: {
    marginLeft: spacing.sm,
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
  vatContainer: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: spacing.lg,
    marginTop: spacing.lg,
    borderWidth: 1,
    borderColor: colors.border,
  },
  vatTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  vatRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  vatLabel: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
  },
  vatValue: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
  },
  vatLabelTotal: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
  },
  vatValueTotal: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.success,
  },
  divider: {
    height: 1,
    backgroundColor: colors.border,
    marginVertical: spacing.sm,
  },
  sendButton: {
    backgroundColor: colors.primary,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.lg,
    borderRadius: 12,
    marginTop: spacing.xl,
  },
  sendButtonDisabled: {
    backgroundColor: colors.gray,
  },
  sendButtonText: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.white,
    marginLeft: spacing.sm,
  },
  infoCard: {
    flexDirection: 'row',
    backgroundColor: colors.info + '10',
    borderRadius: 12,
    padding: spacing.lg,
    marginTop: spacing.lg,
    marginBottom: spacing.xl,
  },
  infoContent: {
    flex: 1,
    marginLeft: spacing.md,
  },
  infoTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.info,
    marginBottom: spacing.xs,
  },
  infoText: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    lineHeight: 20,
  },
});
