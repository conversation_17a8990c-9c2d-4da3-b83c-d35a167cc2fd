import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Appearance } from 'react-native';
import { theme, darkTheme } from '../theme/theme';

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [currentTheme, setCurrentTheme] = useState(theme);

  useEffect(() => {
    loadThemePreference();
    
    // Listen to system theme changes
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      if (colorScheme === 'dark') {
        setDarkMode(true);
      } else {
        setDarkMode(false);
      }
    });

    return () => subscription?.remove();
  }, []);

  const loadThemePreference = async () => {
    try {
      const savedTheme = await AsyncStorage.getItem('themePreference');
      if (savedTheme !== null) {
        const isDark = savedTheme === 'dark';
        setIsDarkMode(isDark);
        setCurrentTheme(isDark ? darkTheme : theme);
      } else {
        // Use system preference
        const systemColorScheme = Appearance.getColorScheme();
        const isDark = systemColorScheme === 'dark';
        setIsDarkMode(isDark);
        setCurrentTheme(isDark ? darkTheme : theme);
      }
    } catch (error) {
      console.error('Error loading theme preference:', error);
    }
  };

  const setDarkMode = (isDark) => {
    setIsDarkMode(isDark);
    setCurrentTheme(isDark ? darkTheme : theme);
    saveThemePreference(isDark ? 'dark' : 'light');
  };

  const saveThemePreference = async (themeMode) => {
    try {
      await AsyncStorage.setItem('themePreference', themeMode);
    } catch (error) {
      console.error('Error saving theme preference:', error);
    }
  };

  const toggleTheme = () => {
    setDarkMode(!isDarkMode);
  };

  const value = {
    isDarkMode,
    currentTheme,
    theme: currentTheme,
    toggleTheme,
    setDarkMode,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
