{"ast": null, "code": "import { timeInterval } from \"./interval.js\";\nimport { durationSecond } from \"./duration.js\";\nexport const second = timeInterval(date => {\n  date.setTime(date - date.getMilliseconds());\n}, (date, step) => {\n  date.setTime(+date + step * durationSecond);\n}, (start, end) => {\n  return (end - start) / durationSecond;\n}, date => {\n  return date.getUTCSeconds();\n});\nexport const seconds = second.range;", "map": {"version": 3, "names": ["timeInterval", "durationSecond", "second", "date", "setTime", "getMilliseconds", "step", "start", "end", "getUTCSeconds", "seconds", "range"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/d3-time/src/second.js"], "sourcesContent": ["import {timeInterval} from \"./interval.js\";\nimport {durationSecond} from \"./duration.js\";\n\nexport const second = timeInterval((date) => {\n  date.setTime(date - date.getMilliseconds());\n}, (date, step) => {\n  date.setTime(+date + step * durationSecond);\n}, (start, end) => {\n  return (end - start) / durationSecond;\n}, (date) => {\n  return date.getUTCSeconds();\n});\n\nexport const seconds = second.range;\n"], "mappings": "AAAA,SAAQA,YAAY,QAAO,eAAe;AAC1C,SAAQC,cAAc,QAAO,eAAe;AAE5C,OAAO,MAAMC,MAAM,GAAGF,YAAY,CAAEG,IAAI,IAAK;EAC3CA,IAAI,CAACC,OAAO,CAACD,IAAI,GAAGA,IAAI,CAACE,eAAe,CAAC,CAAC,CAAC;AAC7C,CAAC,EAAE,CAACF,IAAI,EAAEG,IAAI,KAAK;EACjBH,IAAI,CAACC,OAAO,CAAC,CAACD,IAAI,GAAGG,IAAI,GAAGL,cAAc,CAAC;AAC7C,CAAC,EAAE,CAACM,KAAK,EAAEC,GAAG,KAAK;EACjB,OAAO,CAACA,GAAG,GAAGD,KAAK,IAAIN,cAAc;AACvC,CAAC,EAAGE,IAAI,IAAK;EACX,OAAOA,IAAI,CAACM,aAAa,CAAC,CAAC;AAC7B,CAAC,CAAC;AAEF,OAAO,MAAMC,OAAO,GAAGR,MAAM,CAACS,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}