package com.prochat.model.monetization;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "earning_rates")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EarningRate {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, unique = true)
    private EarningType type;
    
    @Column(nullable = false, precision = 10, scale = 4)
    private BigDecimal rate;
    
    @Column(name = "currency", nullable = false)
    private String currency = "TZS";
    
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;
    
    @Column(name = "description")
    private String description;
    
    @Column(name = "minimum_threshold", precision = 10, scale = 4)
    private BigDecimal minimumThreshold;
    
    @Column(name = "maximum_per_day", precision = 10, scale = 4)
    private BigDecimal maximumPerDay;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @Column(name = "created_by")
    private Long createdBy;
    
    @Column(name = "updated_by")
    private Long updatedBy;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    public enum EarningType {
        LIKE("Like", "Mapato kwa kila like"),
        DOWNLOAD("Download", "Mapato kwa kila MB iliyopakuliwa"),
        GIFT("Gift", "Mapato kutoka zawadi"),
        REPOST("Repost/Share", "Mapato kwa kushare maudhui"),
        INVITE_FRIEND("Invite Friend", "Bonasi ya kualika rafiki"),
        TICKET_SALE("Ticket Sale", "Mapato ya mauzo ya tiketi"),
        DONATION("Donation", "Mapato ya michango"),
        VERIFICATION_BONUS("Verification Bonus", "Bonasi ya kuthibitishwa"),
        AGENT_COMMISSION("Agent Commission", "Kamisheni ya wakala"),
        MERCHANT_PAYMENT("Merchant Payment", "Malipo ya biashara"),
        CASHBACK("Cashback", "Mapato ya cashback"),
        TASK_BONUS("Task Bonus", "Bonasi ya kazi");
        
        private final String displayName;
        private final String description;
        
        EarningType(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
