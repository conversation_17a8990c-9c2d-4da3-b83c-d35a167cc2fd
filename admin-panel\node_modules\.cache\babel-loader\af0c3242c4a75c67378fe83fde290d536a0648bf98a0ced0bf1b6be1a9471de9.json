{"ast": null, "code": "import { ticks, tickIncrement } from \"d3-array\";\nimport continuous, { copy } from \"./continuous.js\";\nimport { initRange } from \"./init.js\";\nimport tickFormat from \"./tickFormat.js\";\nexport function linearish(scale) {\n  var domain = scale.domain;\n  scale.ticks = function (count) {\n    var d = domain();\n    return ticks(d[0], d[d.length - 1], count == null ? 10 : count);\n  };\n  scale.tickFormat = function (count, specifier) {\n    var d = domain();\n    return tickFormat(d[0], d[d.length - 1], count == null ? 10 : count, specifier);\n  };\n  scale.nice = function (count) {\n    if (count == null) count = 10;\n    var d = domain();\n    var i0 = 0;\n    var i1 = d.length - 1;\n    var start = d[i0];\n    var stop = d[i1];\n    var prestep;\n    var step;\n    var maxIter = 10;\n    if (stop < start) {\n      step = start, start = stop, stop = step;\n      step = i0, i0 = i1, i1 = step;\n    }\n    while (maxIter-- > 0) {\n      step = tickIncrement(start, stop, count);\n      if (step === prestep) {\n        d[i0] = start;\n        d[i1] = stop;\n        return domain(d);\n      } else if (step > 0) {\n        start = Math.floor(start / step) * step;\n        stop = Math.ceil(stop / step) * step;\n      } else if (step < 0) {\n        start = Math.ceil(start * step) / step;\n        stop = Math.floor(stop * step) / step;\n      } else {\n        break;\n      }\n      prestep = step;\n    }\n    return scale;\n  };\n  return scale;\n}\nexport default function linear() {\n  var scale = continuous();\n  scale.copy = function () {\n    return copy(scale, linear());\n  };\n  initRange.apply(scale, arguments);\n  return linearish(scale);\n}", "map": {"version": 3, "names": ["ticks", "tickIncrement", "continuous", "copy", "initRange", "tickFormat", "linearish", "scale", "domain", "count", "d", "length", "specifier", "nice", "i0", "i1", "start", "stop", "prestep", "step", "maxIter", "Math", "floor", "ceil", "linear", "apply", "arguments"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/d3-scale/src/linear.js"], "sourcesContent": ["import {ticks, tickIncrement} from \"d3-array\";\nimport continuous, {copy} from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\nimport tickFormat from \"./tickFormat.js\";\n\nexport function linearish(scale) {\n  var domain = scale.domain;\n\n  scale.ticks = function(count) {\n    var d = domain();\n    return ticks(d[0], d[d.length - 1], count == null ? 10 : count);\n  };\n\n  scale.tickFormat = function(count, specifier) {\n    var d = domain();\n    return tickFormat(d[0], d[d.length - 1], count == null ? 10 : count, specifier);\n  };\n\n  scale.nice = function(count) {\n    if (count == null) count = 10;\n\n    var d = domain();\n    var i0 = 0;\n    var i1 = d.length - 1;\n    var start = d[i0];\n    var stop = d[i1];\n    var prestep;\n    var step;\n    var maxIter = 10;\n\n    if (stop < start) {\n      step = start, start = stop, stop = step;\n      step = i0, i0 = i1, i1 = step;\n    }\n    \n    while (maxIter-- > 0) {\n      step = tickIncrement(start, stop, count);\n      if (step === prestep) {\n        d[i0] = start\n        d[i1] = stop\n        return domain(d);\n      } else if (step > 0) {\n        start = Math.floor(start / step) * step;\n        stop = Math.ceil(stop / step) * step;\n      } else if (step < 0) {\n        start = Math.ceil(start * step) / step;\n        stop = Math.floor(stop * step) / step;\n      } else {\n        break;\n      }\n      prestep = step;\n    }\n\n    return scale;\n  };\n\n  return scale;\n}\n\nexport default function linear() {\n  var scale = continuous();\n\n  scale.copy = function() {\n    return copy(scale, linear());\n  };\n\n  initRange.apply(scale, arguments);\n\n  return linearish(scale);\n}\n"], "mappings": "AAAA,SAAQA,KAAK,EAAEC,aAAa,QAAO,UAAU;AAC7C,OAAOC,UAAU,IAAGC,IAAI,QAAO,iBAAiB;AAChD,SAAQC,SAAS,QAAO,WAAW;AACnC,OAAOC,UAAU,MAAM,iBAAiB;AAExC,OAAO,SAASC,SAASA,CAACC,KAAK,EAAE;EAC/B,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;EAEzBD,KAAK,CAACP,KAAK,GAAG,UAASS,KAAK,EAAE;IAC5B,IAAIC,CAAC,GAAGF,MAAM,CAAC,CAAC;IAChB,OAAOR,KAAK,CAACU,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAACA,CAAC,CAACC,MAAM,GAAG,CAAC,CAAC,EAAEF,KAAK,IAAI,IAAI,GAAG,EAAE,GAAGA,KAAK,CAAC;EACjE,CAAC;EAEDF,KAAK,CAACF,UAAU,GAAG,UAASI,KAAK,EAAEG,SAAS,EAAE;IAC5C,IAAIF,CAAC,GAAGF,MAAM,CAAC,CAAC;IAChB,OAAOH,UAAU,CAACK,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAACA,CAAC,CAACC,MAAM,GAAG,CAAC,CAAC,EAAEF,KAAK,IAAI,IAAI,GAAG,EAAE,GAAGA,KAAK,EAAEG,SAAS,CAAC;EACjF,CAAC;EAEDL,KAAK,CAACM,IAAI,GAAG,UAASJ,KAAK,EAAE;IAC3B,IAAIA,KAAK,IAAI,IAAI,EAAEA,KAAK,GAAG,EAAE;IAE7B,IAAIC,CAAC,GAAGF,MAAM,CAAC,CAAC;IAChB,IAAIM,EAAE,GAAG,CAAC;IACV,IAAIC,EAAE,GAAGL,CAAC,CAACC,MAAM,GAAG,CAAC;IACrB,IAAIK,KAAK,GAAGN,CAAC,CAACI,EAAE,CAAC;IACjB,IAAIG,IAAI,GAAGP,CAAC,CAACK,EAAE,CAAC;IAChB,IAAIG,OAAO;IACX,IAAIC,IAAI;IACR,IAAIC,OAAO,GAAG,EAAE;IAEhB,IAAIH,IAAI,GAAGD,KAAK,EAAE;MAChBG,IAAI,GAAGH,KAAK,EAAEA,KAAK,GAAGC,IAAI,EAAEA,IAAI,GAAGE,IAAI;MACvCA,IAAI,GAAGL,EAAE,EAAEA,EAAE,GAAGC,EAAE,EAAEA,EAAE,GAAGI,IAAI;IAC/B;IAEA,OAAOC,OAAO,EAAE,GAAG,CAAC,EAAE;MACpBD,IAAI,GAAGlB,aAAa,CAACe,KAAK,EAAEC,IAAI,EAAER,KAAK,CAAC;MACxC,IAAIU,IAAI,KAAKD,OAAO,EAAE;QACpBR,CAAC,CAACI,EAAE,CAAC,GAAGE,KAAK;QACbN,CAAC,CAACK,EAAE,CAAC,GAAGE,IAAI;QACZ,OAAOT,MAAM,CAACE,CAAC,CAAC;MAClB,CAAC,MAAM,IAAIS,IAAI,GAAG,CAAC,EAAE;QACnBH,KAAK,GAAGK,IAAI,CAACC,KAAK,CAACN,KAAK,GAAGG,IAAI,CAAC,GAAGA,IAAI;QACvCF,IAAI,GAAGI,IAAI,CAACE,IAAI,CAACN,IAAI,GAAGE,IAAI,CAAC,GAAGA,IAAI;MACtC,CAAC,MAAM,IAAIA,IAAI,GAAG,CAAC,EAAE;QACnBH,KAAK,GAAGK,IAAI,CAACE,IAAI,CAACP,KAAK,GAAGG,IAAI,CAAC,GAAGA,IAAI;QACtCF,IAAI,GAAGI,IAAI,CAACC,KAAK,CAACL,IAAI,GAAGE,IAAI,CAAC,GAAGA,IAAI;MACvC,CAAC,MAAM;QACL;MACF;MACAD,OAAO,GAAGC,IAAI;IAChB;IAEA,OAAOZ,KAAK;EACd,CAAC;EAED,OAAOA,KAAK;AACd;AAEA,eAAe,SAASiB,MAAMA,CAAA,EAAG;EAC/B,IAAIjB,KAAK,GAAGL,UAAU,CAAC,CAAC;EAExBK,KAAK,CAACJ,IAAI,GAAG,YAAW;IACtB,OAAOA,IAAI,CAACI,KAAK,EAAEiB,MAAM,CAAC,CAAC,CAAC;EAC9B,CAAC;EAEDpB,SAAS,CAACqB,KAAK,CAAClB,KAAK,EAAEmB,SAAS,CAAC;EAEjC,OAAOpB,SAAS,CAACC,KAAK,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}