import { DefaultTheme } from 'react-native-paper';

// ProChat Color Palette
export const colors = {
  // Primary Colors
  primary: '#007AFF',
  primaryLight: '#4DA3FF',
  primaryDark: '#0056CC',
  
  // Secondary Colors
  secondary: '#4CAF50',
  secondaryLight: '#7BC142',
  secondaryDark: '#2E7D32',
  
  // ProPay Colors
  propayGreen: '#00C853',
  propayBlue: '#2196F3',
  propayOrange: '#FF9800',
  
  // Status Colors
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',
  
  // Neutral Colors
  white: '#FFFFFF',
  black: '#000000',
  gray: '#9E9E9E',
  lightGray: '#F5F5F5',
  darkGray: '#424242',
  
  // Background Colors
  background: '#FFFFFF',
  surface: '#F8F9FA',
  card: '#FFFFFF',
  
  // Text Colors
  text: '#212121',
  textSecondary: '#757575',
  textLight: '#FFFFFF',
  
  // Chat Colors
  chatBubbleSent: '#007AFF',
  chatBubbleReceived: '#E5E5EA',
  chatTextSent: '#FFFFFF',
  chatTextReceived: '#000000',
  
  // Social Media Colors
  like: '#F44336',
  comment: '#2196F3',
  share: '#4CAF50',
  
  // Border Colors
  border: '#E0E0E0',
  divider: '#EEEEEE',
  
  // Overlay Colors
  overlay: 'rgba(0, 0, 0, 0.5)',
  overlayLight: 'rgba(0, 0, 0, 0.3)',
};

// Typography
export const typography = {
  // Font Families
  fontFamily: {
    regular: 'System',
    medium: 'System',
    bold: 'System',
  },
  
  // Font Sizes
  fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 32,
  },
  
  // Font Weights
  fontWeight: {
    light: '300',
    regular: '400',
    medium: '500',
    semiBold: '600',
    bold: '700',
  },
  
  // Line Heights
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
  },
};

// Spacing
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  xxxl: 64,
};

// Border Radius
export const borderRadius = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  xxl: 24,
  round: 50,
};

// Shadows
export const shadows = {
  small: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,
    elevation: 1,
  },
  medium: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  large: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.30,
    shadowRadius: 4.65,
    elevation: 8,
  },
};

// React Native Paper Theme
export const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: colors.primary,
    accent: colors.secondary,
    background: colors.background,
    surface: colors.surface,
    text: colors.text,
    disabled: colors.gray,
    placeholder: colors.textSecondary,
    backdrop: colors.overlay,
    onSurface: colors.text,
    notification: colors.error,
  },
  fonts: {
    ...DefaultTheme.fonts,
    regular: {
      fontFamily: typography.fontFamily.regular,
      fontWeight: typography.fontWeight.regular,
    },
    medium: {
      fontFamily: typography.fontFamily.medium,
      fontWeight: typography.fontWeight.medium,
    },
    light: {
      fontFamily: typography.fontFamily.regular,
      fontWeight: typography.fontWeight.light,
    },
    thin: {
      fontFamily: typography.fontFamily.regular,
      fontWeight: typography.fontWeight.light,
    },
  },
  roundness: borderRadius.md,
};

// Dark Theme
export const darkTheme = {
  ...theme,
  dark: true,
  colors: {
    ...theme.colors,
    primary: colors.primaryLight,
    background: '#121212',
    surface: '#1E1E1E',
    text: '#FFFFFF',
    disabled: '#666666',
    placeholder: '#AAAAAA',
    backdrop: 'rgba(255, 255, 255, 0.1)',
    onSurface: '#FFFFFF',
  },
};

// Component Styles
export const componentStyles = {
  // Button Styles
  button: {
    primary: {
      backgroundColor: colors.primary,
      borderRadius: borderRadius.lg,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.lg,
    },
    secondary: {
      backgroundColor: colors.secondary,
      borderRadius: borderRadius.lg,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.lg,
    },
    outline: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: colors.primary,
      borderRadius: borderRadius.lg,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.lg,
    },
  },
  
  // Input Styles
  input: {
    default: {
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: borderRadius.md,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.md,
      fontSize: typography.fontSize.md,
      backgroundColor: colors.white,
    },
    focused: {
      borderColor: colors.primary,
      borderWidth: 2,
    },
    error: {
      borderColor: colors.error,
    },
  },
  
  // Card Styles
  card: {
    default: {
      backgroundColor: colors.white,
      borderRadius: borderRadius.lg,
      padding: spacing.md,
      ...shadows.medium,
    },
    elevated: {
      backgroundColor: colors.white,
      borderRadius: borderRadius.lg,
      padding: spacing.lg,
      ...shadows.large,
    },
  },
  
  // Chat Bubble Styles
  chatBubble: {
    sent: {
      backgroundColor: colors.chatBubbleSent,
      borderRadius: borderRadius.lg,
      borderBottomRightRadius: borderRadius.sm,
      padding: spacing.md,
      marginLeft: spacing.xl,
      alignSelf: 'flex-end',
    },
    received: {
      backgroundColor: colors.chatBubbleReceived,
      borderRadius: borderRadius.lg,
      borderBottomLeftRadius: borderRadius.sm,
      padding: spacing.md,
      marginRight: spacing.xl,
      alignSelf: 'flex-start',
    },
  },
};

// Animation Durations
export const animations = {
  fast: 150,
  normal: 300,
  slow: 500,
};

// Screen Dimensions Helper
export const layout = {
  window: {
    width: 0, // Will be set by Dimensions
    height: 0, // Will be set by Dimensions
  },
  isSmallDevice: false, // Will be calculated
};

export default {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  theme,
  darkTheme,
  componentStyles,
  animations,
  layout,
};
