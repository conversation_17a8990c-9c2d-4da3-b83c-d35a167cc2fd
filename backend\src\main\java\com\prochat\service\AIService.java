package com.prochat.service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Service
public class AIService {
    
    @Value("${ai.openai.api-key:}")
    private String openAiApiKey;
    
    @Value("${ai.translation.enabled:true}")
    private boolean translationEnabled;
    
    private final RestTemplate restTemplate = new RestTemplate();
    
    /**
     * AI-powered content moderation
     * Checks if content is appropriate for the platform
     */
    public boolean moderateContent(String content) {
        try {
            // Check for inappropriate content using AI
            Map<String, Object> request = new HashMap<>();
            request.put("input", content);
            request.put("model", "text-moderation-latest");
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(openAiApiKey);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(request, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(
                "https://api.openai.com/v1/moderations", entity, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                Map<String, Object> result = response.getBody();
                Map<String, Object> results = (Map<String, Object>) ((java.util.List<?>) result.get("results")).get(0);
                return !(Boolean) results.get("flagged");
            }
        } catch (Exception e) {
            // If AI service fails, use basic keyword filtering
            return basicContentFilter(content);
        }
        
        return true; // Allow content if AI check fails
    }
    
    /**
     * Basic content filtering as fallback
     */
    private boolean basicContentFilter(String content) {
        String[] inappropriateWords = {
            "spam", "scam", "fraud", "hack", "illegal", "drugs", 
            "violence", "hate", "terrorism", "pornography"
        };
        
        String lowerContent = content.toLowerCase();
        for (String word : inappropriateWords) {
            if (lowerContent.contains(word)) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * AI-powered language translation for meetings
     */
    public String translateText(String text, String fromLanguage, String toLanguage) {
        if (!translationEnabled) {
            return text;
        }
        
        try {
            Map<String, Object> request = new HashMap<>();
            request.put("model", "gpt-3.5-turbo");
            request.put("messages", new Object[]{
                Map.of("role", "system", "content", 
                    "You are a professional translator. Translate the following text from " + 
                    fromLanguage + " to " + toLanguage + ". Only return the translation."),
                Map.of("role", "user", "content", text)
            });
            request.put("max_tokens", 1000);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(openAiApiKey);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(request, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(
                "https://api.openai.com/v1/chat/completions", entity, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                Map<String, Object> result = response.getBody();
                java.util.List<?> choices = (java.util.List<?>) result.get("choices");
                Map<String, Object> choice = (Map<String, Object>) choices.get(0);
                Map<String, Object> message = (Map<String, Object>) choice.get("message");
                return (String) message.get("content");
            }
        } catch (Exception e) {
            System.err.println("Translation failed: " + e.getMessage());
        }
        
        return text; // Return original text if translation fails
    }
    
    /**
     * AI-powered text-to-speech for news articles
     */
    public String generateSpeech(String text, String language) {
        try {
            Map<String, Object> request = new HashMap<>();
            request.put("model", "tts-1");
            request.put("input", text);
            request.put("voice", "alloy");
            request.put("response_format", "mp3");
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(openAiApiKey);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(request, headers);
            
            ResponseEntity<byte[]> response = restTemplate.postForEntity(
                "https://api.openai.com/v1/audio/speech", entity, byte[].class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                // Save audio file and return URL
                // Implementation depends on your file storage system
                return "audio_url_here";
            }
        } catch (Exception e) {
            System.err.println("Text-to-speech failed: " + e.getMessage());
        }
        
        return null;
    }
    
    /**
     * AI-powered chat support
     */
    public String getChatSupport(String userMessage, String userLanguage) {
        try {
            String systemPrompt = userLanguage.equals("sw") ? 
                "Wewe ni msaidizi wa huduma kwa wateja wa ProChat. Jibu maswali kwa Kiswahili kwa urafiki na uwazi." :
                "You are a ProChat customer support assistant. Answer questions helpfully and clearly in English.";
            
            Map<String, Object> request = new HashMap<>();
            request.put("model", "gpt-3.5-turbo");
            request.put("messages", new Object[]{
                Map.of("role", "system", "content", systemPrompt),
                Map.of("role", "user", "content", userMessage)
            });
            request.put("max_tokens", 500);
            request.put("temperature", 0.7);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(openAiApiKey);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(request, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(
                "https://api.openai.com/v1/chat/completions", entity, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                Map<String, Object> result = response.getBody();
                java.util.List<?> choices = (java.util.List<?>) result.get("choices");
                Map<String, Object> choice = (Map<String, Object>) choices.get(0);
                Map<String, Object> message = (Map<String, Object>) choice.get("message");
                return (String) message.get("content");
            }
        } catch (Exception e) {
            System.err.println("AI chat support failed: " + e.getMessage());
        }
        
        return userLanguage.equals("sw") ? 
            "Samahani, sina jibu kwa sasa. Tafadhali wasiliana na timu yetu ya msaada." :
            "Sorry, I don't have an answer right now. Please contact our support team.";
    }
    
    /**
     * AI-powered fraud detection for transactions
     */
    public boolean detectFraud(String userId, double amount, String transactionType, String location) {
        try {
            // Simple rule-based fraud detection
            // In production, this would use machine learning models
            
            // Check for unusually large amounts
            if (amount > 1000000) { // 1M TSH
                return true;
            }
            
            // Check for rapid successive transactions
            // This would require checking recent transaction history
            
            // Check for unusual location patterns
            // This would require user's typical location data
            
            // For now, return false (no fraud detected)
            return false;
            
        } catch (Exception e) {
            System.err.println("Fraud detection failed: " + e.getMessage());
            return false; // Don't block transaction if detection fails
        }
    }
    
    /**
     * AI-powered content recommendation
     */
    public java.util.List<String> getContentRecommendations(String userId, String contentType) {
        // This would use user behavior analysis and machine learning
        // For now, return empty list
        return new java.util.ArrayList<>();
    }
    
    /**
     * AI-powered smart notifications
     */
    public String generateSmartNotification(String eventType, Map<String, Object> eventData, String userLanguage) {
        try {
            String prompt = userLanguage.equals("sw") ? 
                "Tengeneza ujumbe mfupi wa arifa kwa Kiswahili kuhusu: " + eventType :
                "Generate a short notification message in English about: " + eventType;
            
            Map<String, Object> request = new HashMap<>();
            request.put("model", "gpt-3.5-turbo");
            request.put("messages", new Object[]{
                Map.of("role", "system", "content", prompt),
                Map.of("role", "user", "content", eventData.toString())
            });
            request.put("max_tokens", 100);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(openAiApiKey);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(request, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(
                "https://api.openai.com/v1/chat/completions", entity, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                Map<String, Object> result = response.getBody();
                java.util.List<?> choices = (java.util.List<?>) result.get("choices");
                Map<String, Object> choice = (Map<String, Object>) choices.get(0);
                Map<String, Object> message = (Map<String, Object>) choice.get("message");
                return (String) message.get("content");
            }
        } catch (Exception e) {
            System.err.println("Smart notification generation failed: " + e.getMessage());
        }
        
        // Fallback to basic notification
        return userLanguage.equals("sw") ? "Una arifa mpya" : "You have a new notification";
    }
}
