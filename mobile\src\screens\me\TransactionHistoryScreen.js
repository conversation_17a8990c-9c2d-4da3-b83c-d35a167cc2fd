import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors, typography, spacing } from '../../theme/theme';
import { walletAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

export default function TransactionHistoryScreen({ navigation }) {
  const { user } = useAuth();
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    loadTransactions();
  }, []);

  const loadTransactions = async (pageNum = 0, refresh = false) => {
    try {
      if (refresh) {
        setRefreshing(true);
        setPage(0);
      }

      const response = await walletAPI.getTransactions(pageNum, 20);
      
      if (response.success) {
        const newTransactions = response.data;
        
        if (refresh || pageNum === 0) {
          setTransactions(newTransactions);
        } else {
          setTransactions(prev => [...prev, ...newTransactions]);
        }
        
        setHasMore(newTransactions.length === 20);
        setPage(pageNum);
      }
    } catch (error) {
      console.error('Error loading transactions:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    loadTransactions(0, true);
  };

  const loadMore = () => {
    if (!loading && hasMore) {
      loadTransactions(page + 1);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('sw-TZ', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getTransactionIcon = (type, status) => {
    switch (type) {
      case 'SEND_MONEY':
        return { name: 'send', color: colors.error };
      case 'RECEIVE_MONEY':
        return { name: 'call-received', color: colors.success };
      case 'WITHDRAWAL':
        return { name: 'account-balance', color: colors.warning };
      case 'DEPOSIT':
        return { name: 'add-circle', color: colors.success };
      case 'GIFT':
        return { name: 'card-giftcard', color: colors.secondary };
      case 'DONATION':
        return { name: 'favorite', color: colors.info };
      default:
        return { name: 'swap-horiz', color: colors.gray };
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'COMPLETED':
        return colors.success;
      case 'PENDING':
        return colors.warning;
      case 'FAILED':
        return colors.error;
      case 'CANCELLED':
        return colors.gray;
      default:
        return colors.gray;
    }
  };

  const getTransactionTitle = (transaction) => {
    switch (transaction.transactionType) {
      case 'SEND_MONEY':
        return `Kutuma kwa ${transaction.recipientName || 'Mtumiaji'}`;
      case 'RECEIVE_MONEY':
        return `Kupokea kutoka ${transaction.senderName || 'Mtumiaji'}`;
      case 'WITHDRAWAL':
        return 'Kutoa Pesa';
      case 'DEPOSIT':
        return 'Kuongeza Pesa';
      case 'GIFT':
        return 'Zawadi';
      case 'DONATION':
        return 'Mchango';
      default:
        return 'Muamala';
    }
  };

  const renderTransaction = ({ item }) => {
    const icon = getTransactionIcon(item.transactionType, item.status);
    const isDebit = ['SEND_MONEY', 'WITHDRAWAL', 'GIFT'].includes(item.transactionType);
    
    return (
      <TouchableOpacity
        style={styles.transactionItem}
        onPress={() => navigation.navigate('TransactionDetails', { transaction: item })}
      >
        <View style={[styles.iconContainer, { backgroundColor: icon.color + '20' }]}>
          <Icon name={icon.name} size={24} color={icon.color} />
        </View>
        
        <View style={styles.transactionContent}>
          <View style={styles.transactionHeader}>
            <Text style={styles.transactionTitle}>
              {getTransactionTitle(item)}
            </Text>
            <Text style={[
              styles.transactionAmount,
              { color: isDebit ? colors.error : colors.success }
            ]}>
              {isDebit ? '-' : '+'}{formatCurrency(item.amount)}
            </Text>
          </View>
          
          <View style={styles.transactionDetails}>
            <Text style={styles.transactionDate}>
              {formatDate(item.createdAt)}
            </Text>
            <View style={[
              styles.statusBadge,
              { backgroundColor: getStatusColor(item.status) + '20' }
            ]}>
              <Text style={[
                styles.statusText,
                { color: getStatusColor(item.status) }
              ]}>
                {item.status === 'COMPLETED' ? 'Imekamilika' :
                 item.status === 'PENDING' ? 'Inasubiri' :
                 item.status === 'FAILED' ? 'Imeshindwa' : 'Imeghairiwa'}
              </Text>
            </View>
          </View>
          
          {item.vatAmount && item.vatAmount > 0 && (
            <Text style={styles.vatInfo}>
              VAT: {formatCurrency(item.vatAmount)}
            </Text>
          )}
          
          {item.description && (
            <Text style={styles.transactionDescription} numberOfLines={1}>
              {item.description}
            </Text>
          )}
        </View>
        
        <Icon name="chevron-right" size={20} color={colors.gray} />
      </TouchableOpacity>
    );
  };

  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <Icon name="receipt" size={64} color={colors.gray} />
      <Text style={styles.emptyTitle}>Hakuna Miamala</Text>
      <Text style={styles.emptyText}>
        Miamala yako ya ProPay itaonekana hapa
      </Text>
    </View>
  );

  const renderFooter = () => {
    if (!loading || page === 0) return null;
    
    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color={colors.primary} />
      </View>
    );
  };

  if (loading && page === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Historia ya Miamala</Text>
          <View style={styles.placeholder} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Inapakia miamala...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Historia ya Miamala</Text>
        <TouchableOpacity style={styles.filterButton}>
          <Icon name="filter-list" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      {/* Transactions List */}
      <FlatList
        data={transactions}
        renderItem={renderTransaction}
        keyExtractor={(item) => item.id.toString()}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        onEndReached={loadMore}
        onEndReachedThreshold={0.1}
        ListEmptyComponent={renderEmpty}
        ListFooterComponent={renderFooter}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={transactions.length === 0 ? styles.emptyList : null}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
  },
  filterButton: {
    padding: spacing.sm,
  },
  placeholder: {
    width: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    backgroundColor: colors.white,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  transactionContent: {
    flex: 1,
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  transactionTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
    flex: 1,
  },
  transactionAmount: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
  },
  transactionDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  transactionDate: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: 2,
    borderRadius: 12,
  },
  statusText: {
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
  },
  vatInfo: {
    fontSize: typography.fontSize.xs,
    color: colors.error,
    marginBottom: spacing.xs,
  },
  transactionDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },
  emptyList: {
    flexGrow: 1,
  },
  emptyTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptyText: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  footerLoader: {
    paddingVertical: spacing.lg,
    alignItems: 'center',
  },
});
