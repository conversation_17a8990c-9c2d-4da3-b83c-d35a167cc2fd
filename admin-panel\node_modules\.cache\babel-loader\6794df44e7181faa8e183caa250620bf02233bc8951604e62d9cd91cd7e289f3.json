{"ast": null, "code": "export function shouldThrowError(suspense, _useErrorBoundary, params) {\n  // Allow useErrorBoundary function to override throwing behavior on a per-error basis\n  if (typeof _useErrorBoundary === 'function') {\n    return _useErrorBoundary.apply(void 0, params);\n  } // Allow useErrorBoundary to override suspense's throwing behavior\n\n  if (typeof _useErrorBoundary === 'boolean') return _useErrorBoundary; // If suspense is enabled default to throwing errors\n\n  return !!suspense;\n}", "map": {"version": 3, "names": ["shouldThrowError", "suspense", "_useErrorBoundary", "params", "apply"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/react-query/es/react/utils.js"], "sourcesContent": ["export function shouldThrowError(suspense, _useErrorBoundary, params) {\n  // Allow useErrorBoundary function to override throwing behavior on a per-error basis\n  if (typeof _useErrorBoundary === 'function') {\n    return _useErrorBoundary.apply(void 0, params);\n  } // Allow useErrorBoundary to override suspense's throwing behavior\n\n\n  if (typeof _useErrorBoundary === 'boolean') return _useErrorBoundary; // If suspense is enabled default to throwing errors\n\n  return !!suspense;\n}"], "mappings": "AAAA,OAAO,SAASA,gBAAgBA,CAACC,QAAQ,EAAEC,iBAAiB,EAAEC,MAAM,EAAE;EACpE;EACA,IAAI,OAAOD,iBAAiB,KAAK,UAAU,EAAE;IAC3C,OAAOA,iBAAiB,CAACE,KAAK,CAAC,KAAK,CAAC,EAAED,MAAM,CAAC;EAChD,CAAC,CAAC;;EAGF,IAAI,OAAOD,iBAAiB,KAAK,SAAS,EAAE,OAAOA,iBAAiB,CAAC,CAAC;;EAEtE,OAAO,CAAC,CAACD,QAAQ;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}