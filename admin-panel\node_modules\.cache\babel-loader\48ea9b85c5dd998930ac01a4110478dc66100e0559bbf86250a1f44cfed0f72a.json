{"ast": null, "code": "import { tickIncrement } from \"./ticks.js\";\nexport default function nice(start, stop, count) {\n  let prestep;\n  while (true) {\n    const step = tickIncrement(start, stop, count);\n    if (step === prestep || step === 0 || !isFinite(step)) {\n      return [start, stop];\n    } else if (step > 0) {\n      start = Math.floor(start / step) * step;\n      stop = Math.ceil(stop / step) * step;\n    } else if (step < 0) {\n      start = Math.ceil(start * step) / step;\n      stop = Math.floor(stop * step) / step;\n    }\n    prestep = step;\n  }\n}", "map": {"version": 3, "names": ["tickIncrement", "nice", "start", "stop", "count", "prestep", "step", "isFinite", "Math", "floor", "ceil"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/d3-array/src/nice.js"], "sourcesContent": ["import {tickIncrement} from \"./ticks.js\";\n\nexport default function nice(start, stop, count) {\n  let prestep;\n  while (true) {\n    const step = tickIncrement(start, stop, count);\n    if (step === prestep || step === 0 || !isFinite(step)) {\n      return [start, stop];\n    } else if (step > 0) {\n      start = Math.floor(start / step) * step;\n      stop = Math.ceil(stop / step) * step;\n    } else if (step < 0) {\n      start = Math.ceil(start * step) / step;\n      stop = Math.floor(stop * step) / step;\n    }\n    prestep = step;\n  }\n}\n"], "mappings": "AAAA,SAAQA,aAAa,QAAO,YAAY;AAExC,eAAe,SAASC,IAAIA,CAACC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAE;EAC/C,IAAIC,OAAO;EACX,OAAO,IAAI,EAAE;IACX,MAAMC,IAAI,GAAGN,aAAa,CAACE,KAAK,EAAEC,IAAI,EAAEC,KAAK,CAAC;IAC9C,IAAIE,IAAI,KAAKD,OAAO,IAAIC,IAAI,KAAK,CAAC,IAAI,CAACC,QAAQ,CAACD,IAAI,CAAC,EAAE;MACrD,OAAO,CAACJ,KAAK,EAAEC,IAAI,CAAC;IACtB,CAAC,MAAM,IAAIG,IAAI,GAAG,CAAC,EAAE;MACnBJ,KAAK,GAAGM,IAAI,CAACC,KAAK,CAACP,KAAK,GAAGI,IAAI,CAAC,GAAGA,IAAI;MACvCH,IAAI,GAAGK,IAAI,CAACE,IAAI,CAACP,IAAI,GAAGG,IAAI,CAAC,GAAGA,IAAI;IACtC,CAAC,MAAM,IAAIA,IAAI,GAAG,CAAC,EAAE;MACnBJ,KAAK,GAAGM,IAAI,CAACE,IAAI,CAACR,KAAK,GAAGI,IAAI,CAAC,GAAGA,IAAI;MACtCH,IAAI,GAAGK,IAAI,CAACC,KAAK,CAACN,IAAI,GAAGG,IAAI,CAAC,GAAGA,IAAI;IACvC;IACAD,OAAO,GAAGC,IAAI;EAChB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}