import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  InputAdornment,
  Tabs,
  Tab,
  Alert,
  LinearProgress,
} from '@mui/material';
import {
  Search,
  FilterList,
  MoreVert,
  CheckCircle,
  Cancel,
  AccountBalance,
  TrendingUp,
  TrendingDown,
  Warning,
  Download,
  Upload,
  SwapHoriz,
  CreditCard,
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';
import { useAuth } from '../../contexts/AuthContext';

export default function FinancePanel() {
  const [currentTab, setCurrentTab] = useState(0);
  const [transactions, setTransactions] = useState([]);
  const [filteredTransactions, setFilteredTransactions] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [selectedTransaction, setSelectedTransaction] = useState(null);
  const [actionMenuAnchor, setActionMenuAnchor] = useState(null);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const { hasPermission } = useAuth();

  const tabs = [
    { label: 'Miamala Yote', value: 'all', icon: <SwapHoriz /> },
    { label: 'Uwekaji', value: 'deposits', icon: <Download /> },
    { label: 'Utolewa', value: 'withdrawals', icon: <Upload /> },
    { label: 'Malipo', value: 'payments', icon: <CreditCard /> },
  ];

  // Mock financial data
  const [financialStats, setFinancialStats] = useState({
    totalRevenue: 45678900,
    todayRevenue: 123450,
    totalTransactions: 156789,
    pendingWithdrawals: 23,
    pendingDeposits: 12,
    totalCommissions: 2345678,
    fraudAlerts: 5,
  });

  const [revenueData] = useState([
    { month: 'Jan', revenue: 2000000, transactions: 1200 },
    { month: 'Feb', revenue: 3200000, transactions: 1800 },
    { month: 'Mar', revenue: 4800000, transactions: 2400 },
    { month: 'Apr', revenue: 7200000, transactions: 3200 },
    { month: 'May', revenue: 9800000, transactions: 4100 },
    { month: 'Jun', revenue: 12500000, transactions: 5200 },
  ]);

  // Mock transactions data
  useEffect(() => {
    const mockTransactions = [
      {
        id: 1,
        type: 'DEPOSIT',
        amount: 50000,
        currency: 'TSH',
        status: 'COMPLETED',
        fromUser: 'John Doe',
        toUser: 'ProPay System',
        description: 'Uwekaji wa pesa',
        createdAt: '2024-01-20T10:30:00',
        reference: 'DEP001234',
        paymentMethod: 'M-Pesa',
        fees: 500,
      },
      {
        id: 2,
        type: 'WITHDRAWAL',
        amount: 25000,
        currency: 'TSH',
        status: 'PENDING',
        fromUser: 'Mary Johnson',
        toUser: 'Bank Account',
        description: 'Utolewa wa pesa',
        createdAt: '2024-01-20T09:15:00',
        reference: 'WTH001235',
        paymentMethod: 'Bank Transfer',
        fees: 1000,
      },
      {
        id: 3,
        type: 'PAYMENT',
        amount: 15000,
        currency: 'TSH',
        status: 'COMPLETED',
        fromUser: 'Peter Smith',
        toUser: 'Event Organizer',
        description: 'Malipo ya tiketi za tukio',
        createdAt: '2024-01-20T08:00:00',
        reference: 'PAY001236',
        paymentMethod: 'ProPay Wallet',
        fees: 150,
      },
      {
        id: 4,
        type: 'TRANSFER',
        amount: 75000,
        currency: 'TSH',
        status: 'FLAGGED',
        fromUser: 'Alice Brown',
        toUser: 'Bob Wilson',
        description: 'Uhamisho wa pesa',
        createdAt: '2024-01-20T07:30:00',
        reference: 'TRF001237',
        paymentMethod: 'ProPay Wallet',
        fees: 750,
      },
    ];
    setTransactions(mockTransactions);
    setFilteredTransactions(mockTransactions);
  }, []);

  useEffect(() => {
    const currentTabValue = tabs[currentTab].value;
    let filtered = transactions;
    
    if (currentTabValue !== 'all') {
      const typeMap = {
        'deposits': ['DEPOSIT'],
        'withdrawals': ['WITHDRAWAL'],
        'payments': ['PAYMENT', 'TRANSFER'],
      };
      filtered = transactions.filter(t => typeMap[currentTabValue]?.includes(t.type));
    }
    
    filtered = filtered.filter(t =>
      t.fromUser.toLowerCase().includes(searchQuery.toLowerCase()) ||
      t.toUser.toLowerCase().includes(searchQuery.toLowerCase()) ||
      t.reference.toLowerCase().includes(searchQuery.toLowerCase()) ||
      t.description.toLowerCase().includes(searchQuery.toLowerCase())
    );
    
    setFilteredTransactions(filtered);
  }, [currentTab, searchQuery, transactions]);

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
    setPage(0);
  };

  const handleActionClick = (event, transaction) => {
    setSelectedTransaction(transaction);
    setActionMenuAnchor(event.currentTarget);
  };

  const handleActionClose = () => {
    setActionMenuAnchor(null);
    setSelectedTransaction(null);
  };

  const handleTransactionAction = (action) => {
    if (!selectedTransaction) return;
    
    switch (action) {
      case 'approve':
        console.log('Approve transaction:', selectedTransaction.id);
        break;
      case 'reject':
        console.log('Reject transaction:', selectedTransaction.id);
        break;
      case 'flag':
        console.log('Flag transaction:', selectedTransaction.id);
        break;
      case 'details':
        setDetailsDialogOpen(true);
        break;
      default:
        break;
    }
    handleActionClose();
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'COMPLETED': return 'success';
      case 'PENDING': return 'warning';
      case 'FAILED': return 'error';
      case 'FLAGGED': return 'error';
      default: return 'default';
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'COMPLETED': return 'Imekamilika';
      case 'PENDING': return 'Inasubiri';
      case 'FAILED': return 'Imeshindwa';
      case 'FLAGGED': return 'Imebandikwa';
      default: return status;
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'DEPOSIT': return 'success';
      case 'WITHDRAWAL': return 'warning';
      case 'PAYMENT': return 'primary';
      case 'TRANSFER': return 'info';
      default: return 'default';
    }
  };

  const getTypeLabel = (type) => {
    switch (type) {
      case 'DEPOSIT': return 'Uwekaji';
      case 'WITHDRAWAL': return 'Utolewa';
      case 'PAYMENT': return 'Malipo';
      case 'TRANSFER': return 'Uhamisho';
      default: return type;
    }
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          ProPay Finance Panel
        </Typography>
        <Button variant="contained" startIcon={<Download />}>
          Pakua Ripoti
        </Button>
      </Box>

      {/* Financial Stats */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Mapato ya Jumla
                  </Typography>
                  <Typography variant="h5" color="success.main">
                    {formatCurrency(financialStats.totalRevenue)}
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    +{formatCurrency(financialStats.todayRevenue)} leo
                  </Typography>
                </Box>
                <TrendingUp color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Miamala ya Jumla
                  </Typography>
                  <Typography variant="h5" color="primary.main">
                    {financialStats.totalTransactions.toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Tangu mwanzo
                  </Typography>
                </Box>
                <SwapHoriz color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Inasubiri Idhini
                  </Typography>
                  <Typography variant="h5" color="warning.main">
                    {financialStats.pendingWithdrawals + financialStats.pendingDeposits}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {financialStats.pendingWithdrawals} utolewa, {financialStats.pendingDeposits} uwekaji
                  </Typography>
                </Box>
                <Warning color="warning" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Onyo za Ulaghai
                  </Typography>
                  <Typography variant="h5" color="error.main">
                    {financialStats.fraudAlerts}
                  </Typography>
                  <Typography variant="body2" color="error.main">
                    Zinahitaji uchunguzi
                  </Typography>
                </Box>
                <Warning color="error" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Revenue Chart */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Mwelekeo wa Mapato
          </Typography>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={revenueData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip 
                formatter={(value, name) => [
                  name === 'revenue' ? formatCurrency(value) : value.toLocaleString(),
                  name === 'revenue' ? 'Mapato' : 'Miamala'
                ]}
              />
              <Line type="monotone" dataKey="revenue" stroke="#4CAF50" strokeWidth={3} />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Transaction Type Tabs */}
      <Card sx={{ mb: 3 }}>
        <Tabs
          value={currentTab}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
        >
          {tabs.map((tab, index) => (
            <Tab
              key={index}
              label={tab.label}
              icon={tab.icon}
              iconPosition="start"
            />
          ))}
        </Tabs>
      </Card>

      {/* Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Tafuta miamala..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Button
                variant="outlined"
                startIcon={<FilterList />}
              >
                Chuja kwa Tarehe
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Transactions Table */}
      <Card>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Rejeleo</TableCell>
                <TableCell>Aina</TableCell>
                <TableCell>Kutoka/Kwenda</TableCell>
                <TableCell>Kiasi</TableCell>
                <TableCell>Hali</TableCell>
                <TableCell>Tarehe</TableCell>
                <TableCell>Vitendo</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredTransactions
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((transaction) => (
                  <TableRow key={transaction.id}>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {transaction.reference}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {transaction.paymentMethod}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getTypeLabel(transaction.type)}
                        color={getTypeColor(transaction.type)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        <strong>Kutoka:</strong> {transaction.fromUser}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Kwenda:</strong> {transaction.toUser}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {formatCurrency(transaction.amount)}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Ada: {formatCurrency(transaction.fees)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getStatusLabel(transaction.status)}
                        color={getStatusColor(transaction.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(transaction.createdAt).toLocaleDateString('sw-TZ')}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {new Date(transaction.createdAt).toLocaleTimeString('sw-TZ')}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <IconButton
                        onClick={(e) => handleActionClick(e, transaction)}
                        disabled={!hasPermission('VIEW_TRANSACTIONS')}
                      >
                        <MoreVert />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={filteredTransactions.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={(e, newPage) => setPage(newPage)}
          onRowsPerPageChange={(e) => {
            setRowsPerPage(parseInt(e.target.value, 10));
            setPage(0);
          }}
        />
      </Card>

      {/* Action Menu */}
      <Menu
        anchorEl={actionMenuAnchor}
        open={Boolean(actionMenuAnchor)}
        onClose={handleActionClose}
      >
        <MenuItem onClick={() => handleTransactionAction('details')}>
          <AccountBalance sx={{ mr: 1 }} /> Maelezo Kamili
        </MenuItem>
        {selectedTransaction?.status === 'PENDING' && (
          <>
            <MenuItem onClick={() => handleTransactionAction('approve')}>
              <CheckCircle sx={{ mr: 1 }} /> Idhinisha
            </MenuItem>
            <MenuItem onClick={() => handleTransactionAction('reject')}>
              <Cancel sx={{ mr: 1 }} /> Kataa
            </MenuItem>
          </>
        )}
        <MenuItem onClick={() => handleTransactionAction('flag')}>
          <Warning sx={{ mr: 1 }} /> Bandika kama Ulaghai
        </MenuItem>
      </Menu>

      {/* Transaction Details Dialog */}
      <Dialog 
        open={detailsDialogOpen} 
        onClose={() => setDetailsDialogOpen(false)} 
        maxWidth="md" 
        fullWidth
      >
        <DialogTitle>Maelezo ya Muamala</DialogTitle>
        <DialogContent>
          {selectedTransaction && (
            <Box>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Rejeleo:</Typography>
                  <Typography variant="body1">{selectedTransaction.reference}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Aina ya Muamala:</Typography>
                  <Chip 
                    label={getTypeLabel(selectedTransaction.type)} 
                    color={getTypeColor(selectedTransaction.type)}
                    size="small"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Kiasi:</Typography>
                  <Typography variant="h6">{formatCurrency(selectedTransaction.amount)}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Ada:</Typography>
                  <Typography variant="body1">{formatCurrency(selectedTransaction.fees)}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Kutoka:</Typography>
                  <Typography variant="body1">{selectedTransaction.fromUser}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Kwenda:</Typography>
                  <Typography variant="body1">{selectedTransaction.toUser}</Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2">Maelezo:</Typography>
                  <Typography variant="body1">{selectedTransaction.description}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Njia ya Malipo:</Typography>
                  <Typography variant="body1">{selectedTransaction.paymentMethod}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Hali:</Typography>
                  <Chip 
                    label={getStatusLabel(selectedTransaction.status)} 
                    color={getStatusColor(selectedTransaction.status)}
                    size="small"
                  />
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsDialogOpen(false)}>
            Funga
          </Button>
          {selectedTransaction?.status === 'PENDING' && hasPermission('APPROVE_TRANSACTIONS') && (
            <>
              <Button 
                variant="contained" 
                color="success"
                onClick={() => handleTransactionAction('approve')}
              >
                Idhinisha
              </Button>
              <Button 
                variant="contained" 
                color="error"
                onClick={() => handleTransactionAction('reject')}
              >
                Kataa
              </Button>
            </>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
}
