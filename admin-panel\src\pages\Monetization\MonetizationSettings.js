import React, { useState } from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Grid,
  TextField,
  Switch,
  FormControlLabel,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
} from '@mui/material';
import {
  Save,
  Edit,
  Delete,
  Add,
  MonetizationOn,
  TrendingUp,
  CardGiftcard,
  Assignment,
  Loyalty,
} from '@mui/icons-material';

export default function MonetizationSettings() {
  const [currentTab, setCurrentTab] = useState(0);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedRate, setSelectedRate] = useState(null);

  const [earningRates] = useState([
    {
      id: 1,
      type: 'LIKE',
      displayName: 'Like',
      rate: 0.5,
      currency: 'TZS',
      isActive: true,
      description: 'Mapato kwa kila like',
      minimumThreshold: 1,
      maximumPerDay: 1000,
    },
    {
      id: 2,
      type: 'DOWNLOAD',
      displayName: 'Download',
      rate: 0.25,
      currency: 'TZS',
      isActive: true,
      description: 'Mapato kwa kila MB iliyopakuliwa',
      minimumThreshold: 1,
      maximumPerDay: 5000,
    },
    {
      id: 3,
      type: 'GIFT',
      displayName: 'Gift',
      rate: 80.0,
      currency: '%',
      isActive: true,
      description: 'Asilimia ya zawadi anayopokea mtumiaji',
      minimumThreshold: 0,
      maximumPerDay: 50000,
    },
    {
      id: 4,
      type: 'INVITE_FRIEND',
      displayName: 'Invite Friend',
      rate: 100.0,
      currency: 'TZS',
      isActive: true,
      description: 'Bonasi ya kualika rafiki',
      minimumThreshold: 1,
      maximumPerDay: 1000,
    },
  ]);

  const [gifts] = useState([
    {
      id: 1,
      name: 'coin',
      displayName: 'Sarafu',
      price: 50,
      category: 'BASIC',
      recipientPercentage: 80,
      isActive: true,
    },
    {
      id: 2,
      name: 'rose',
      displayName: 'Waridi',
      price: 200,
      category: 'FLOWERS',
      recipientPercentage: 80,
      isActive: true,
    },
    {
      id: 3,
      name: 'diamond',
      displayName: 'Almasi',
      price: 1000,
      category: 'JEWELRY',
      recipientPercentage: 85,
      isActive: true,
    },
  ]);

  const [tasks] = useState([
    {
      id: 1,
      title: 'Penda Machapisho 50',
      taskType: 'LIKE_POSTS',
      targetCount: 50,
      rewardAmount: 500,
      isActive: true,
      isDaily: true,
    },
    {
      id: 2,
      title: 'Alika Marafiki 3',
      taskType: 'INVITE_FRIENDS',
      targetCount: 3,
      rewardAmount: 1000,
      isActive: true,
      isDaily: false,
    },
    {
      id: 3,
      title: 'Ingia Kila Siku',
      taskType: 'DAILY_LOGIN',
      targetCount: 7,
      rewardAmount: 200,
      isActive: true,
      isDaily: true,
    },
  ]);

  const [cashbackRules] = useState([
    {
      id: 1,
      name: 'ProPay Cashback',
      activityType: 'PROPAY_TRANSACTION',
      cashbackPercentage: 2.0,
      minimumAmount: 1000,
      maximumCashback: 500,
      isActive: true,
    },
    {
      id: 2,
      name: 'Ticket Cashback',
      activityType: 'TICKET_PURCHASE',
      cashbackPercentage: 5.0,
      minimumAmount: 5000,
      maximumCashback: 1000,
      isActive: true,
    },
  ]);

  const tabs = [
    { label: 'Viwango vya Mapato', icon: <MonetizationOn /> },
    { label: 'Zawadi', icon: <CardGiftcard /> },
    { label: 'Kazi za Bonasi', icon: <Assignment /> },
    { label: 'Cashback', icon: <Loyalty /> },
  ];

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  const handleEditRate = (rate) => {
    setSelectedRate(rate);
    setEditDialogOpen(true);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const renderEarningRates = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">Viwango vya Mapato</Typography>
        <Button variant="contained" startIcon={<Add />}>
          Ongeza Kiwango
        </Button>
      </Box>

      <Alert severity="info" sx={{ mb: 3 }}>
        Hapa unaweza kubadilisha viwango vya mapato kwa shughuli mbalimbali za watumiaji.
      </Alert>

      <TableContainer component={Card}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Aina ya Mapato</TableCell>
              <TableCell>Kiwango</TableCell>
              <TableCell>Kiwango cha Chini</TableCell>
              <TableCell>Kiwango cha Juu (Siku)</TableCell>
              <TableCell>Hali</TableCell>
              <TableCell>Vitendo</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {earningRates.map((rate) => (
              <TableRow key={rate.id}>
                <TableCell>
                  <Typography variant="subtitle2">{rate.displayName}</Typography>
                  <Typography variant="caption" color="text.secondary">
                    {rate.description}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" fontWeight="bold">
                    {rate.rate} {rate.currency === 'TZS' ? 'TZS' : rate.currency}
                  </Typography>
                </TableCell>
                <TableCell>
                  {rate.minimumThreshold}
                </TableCell>
                <TableCell>
                  {rate.currency === 'TZS' ? formatCurrency(rate.maximumPerDay) : rate.maximumPerDay}
                </TableCell>
                <TableCell>
                  <Chip
                    label={rate.isActive ? 'Inatumika' : 'Imezimwa'}
                    color={rate.isActive ? 'success' : 'error'}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <IconButton onClick={() => handleEditRate(rate)} size="small">
                    <Edit />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );

  const renderGifts = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">Mfumo wa Zawadi</Typography>
        <Button variant="contained" startIcon={<Add />}>
          Ongeza Zawadi
        </Button>
      </Box>

      <Alert severity="info" sx={{ mb: 3 }}>
        Zawadi ni njia ya watumiaji kutumiana pesa kwa njia ya kuvutia. Platform inachukua asilimia kidogo.
      </Alert>

      <Grid container spacing={3}>
        {gifts.map((gift) => (
          <Grid item xs={12} md={6} lg={4} key={gift.id}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="h6">{gift.displayName}</Typography>
                  <Chip
                    label={gift.isActive ? 'Inatumika' : 'Imezimwa'}
                    color={gift.isActive ? 'success' : 'error'}
                    size="small"
                  />
                </Box>
                
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Aina: {gift.category}
                </Typography>
                
                <Typography variant="h5" color="primary" gutterBottom>
                  {formatCurrency(gift.price)}
                </Typography>
                
                <Typography variant="body2" sx={{ mb: 2 }}>
                  Mpokeaji: {gift.recipientPercentage}%
                </Typography>
                
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button size="small" startIcon={<Edit />}>
                    Hariri
                  </Button>
                  <Button size="small" color="error" startIcon={<Delete />}>
                    Futa
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );

  const renderTasks = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">Kazi za Bonasi</Typography>
        <Button variant="contained" startIcon={<Add />}>
          Ongeza Kazi
        </Button>
      </Box>

      <Alert severity="info" sx={{ mb: 3 }}>
        Kazi za bonasi ni njia ya kuwahimiza watumiaji kutumia app zaidi na kupata mapato.
      </Alert>

      <TableContainer component={Card}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Jina la Kazi</TableCell>
              <TableCell>Aina</TableCell>
              <TableCell>Lengo</TableCell>
              <TableCell>Tuzo</TableCell>
              <TableCell>Mzunguko</TableCell>
              <TableCell>Hali</TableCell>
              <TableCell>Vitendo</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {tasks.map((task) => (
              <TableRow key={task.id}>
                <TableCell>
                  <Typography variant="subtitle2">{task.title}</Typography>
                </TableCell>
                <TableCell>{task.taskType}</TableCell>
                <TableCell>{task.targetCount}</TableCell>
                <TableCell>
                  <Typography variant="body2" fontWeight="bold">
                    {formatCurrency(task.rewardAmount)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip
                    label={task.isDaily ? 'Kila Siku' : 'Mara Moja'}
                    color={task.isDaily ? 'primary' : 'secondary'}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Chip
                    label={task.isActive ? 'Inatumika' : 'Imezimwa'}
                    color={task.isActive ? 'success' : 'error'}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <IconButton size="small">
                    <Edit />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );

  const renderCashback = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">Mfumo wa Cashback</Typography>
        <Button variant="contained" startIcon={<Add />}>
          Ongeza Sheria
        </Button>
      </Box>

      <Alert severity="info" sx={{ mb: 3 }}>
        Cashback ni njia ya kurudishia watumiaji sehemu ya pesa wanazotumia katika shughuli mbalimbali.
      </Alert>

      <TableContainer component={Card}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Jina la Sheria</TableCell>
              <TableCell>Aina ya Shughuli</TableCell>
              <TableCell>Asilimia</TableCell>
              <TableCell>Kiwango cha Chini</TableCell>
              <TableCell>Kiwango cha Juu</TableCell>
              <TableCell>Hali</TableCell>
              <TableCell>Vitendo</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {cashbackRules.map((rule) => (
              <TableRow key={rule.id}>
                <TableCell>
                  <Typography variant="subtitle2">{rule.name}</Typography>
                </TableCell>
                <TableCell>{rule.activityType}</TableCell>
                <TableCell>
                  <Typography variant="body2" fontWeight="bold">
                    {rule.cashbackPercentage}%
                  </Typography>
                </TableCell>
                <TableCell>
                  {formatCurrency(rule.minimumAmount)}
                </TableCell>
                <TableCell>
                  {formatCurrency(rule.maximumCashback)}
                </TableCell>
                <TableCell>
                  <Chip
                    label={rule.isActive ? 'Inatumika' : 'Imezimwa'}
                    color={rule.isActive ? 'success' : 'error'}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <IconButton size="small">
                    <Edit />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );

  const renderTabContent = () => {
    switch (currentTab) {
      case 0: return renderEarningRates();
      case 1: return renderGifts();
      case 2: return renderTasks();
      case 3: return renderCashback();
      default: return renderEarningRates();
    }
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Mipangilio ya Mapato
        </Typography>
        <Button variant="contained" startIcon={<Save />}>
          Hifadhi Mabadiliko
        </Button>
      </Box>

      {/* Tabs */}
      <Card sx={{ mb: 3 }}>
        <Tabs
          value={currentTab}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
        >
          {tabs.map((tab, index) => (
            <Tab
              key={index}
              label={tab.label}
              icon={tab.icon}
              iconPosition="start"
            />
          ))}
        </Tabs>
      </Card>

      {/* Content */}
      {renderTabContent()}

      {/* Edit Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Hariri Kiwango cha Mapato</DialogTitle>
        <DialogContent>
          {selectedRate && (
            <Grid container spacing={3} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Kiwango"
                  type="number"
                  defaultValue={selectedRate.rate}
                  inputProps={{ step: 0.01, min: 0 }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Kiwango cha Chini"
                  type="number"
                  defaultValue={selectedRate.minimumThreshold}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Kiwango cha Juu (Siku)"
                  type="number"
                  defaultValue={selectedRate.maximumPerDay}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControlLabel
                  control={<Switch defaultChecked={selectedRate.isActive} />}
                  label="Inatumika"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Maelezo"
                  multiline
                  rows={3}
                  defaultValue={selectedRate.description}
                />
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>
            Ghairi
          </Button>
          <Button variant="contained">
            Hifadhi
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
