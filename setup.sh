#!/bin/bash

# ProChat Setup Script
# This script sets up the entire ProChat application

echo "🚀 Setting up ProChat - Social & Financial Platform"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if required tools are installed
check_requirements() {
    print_step "Checking system requirements..."
    
    # Check Java
    if command -v java &> /dev/null; then
        JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
        print_status "Java found: $JAVA_VERSION"
    else
        print_error "Java not found. Please install Java 17 or higher."
        exit 1
    fi
    
    # Check Node.js
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_status "Node.js found: $NODE_VERSION"
    else
        print_error "Node.js not found. Please install Node.js 16 or higher."
        exit 1
    fi
    
    # Check MySQL
    if command -v mysql &> /dev/null; then
        print_status "MySQL found"
    else
        print_error "MySQL not found. Please install MySQL 8.0 or higher."
        exit 1
    fi
    
    # Check npm
    if command -v npm &> /dev/null; then
        NPM_VERSION=$(npm --version)
        print_status "npm found: $NPM_VERSION"
    else
        print_error "npm not found. Please install npm."
        exit 1
    fi
    
    print_status "All requirements satisfied!"
}

# Setup database
setup_database() {
    print_step "Setting up database..."
    
    echo "Please enter MySQL root password:"
    read -s MYSQL_PASSWORD
    
    # Create database
    mysql -u root -p$MYSQL_PASSWORD -e "CREATE DATABASE IF NOT EXISTS prochat_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        print_status "Database created successfully"
    else
        print_error "Failed to create database. Please check your MySQL credentials."
        exit 1
    fi
    
    # Run schema
    mysql -u root -p$MYSQL_PASSWORD prochat_db < database/init_database.sql 2>/dev/null
    
    if [ $? -eq 0 ]; then
        print_status "Database schema created successfully"
    else
        print_error "Failed to create database schema."
        exit 1
    fi
}

# Setup backend
setup_backend() {
    print_step "Setting up backend..."
    
    cd backend
    
    # Check if Maven wrapper exists
    if [ -f "./mvnw" ]; then
        chmod +x ./mvnw
        print_status "Maven wrapper found"
    else
        print_warning "Maven wrapper not found. Using system Maven."
    fi
    
    # Install dependencies and compile
    if [ -f "./mvnw" ]; then
        ./mvnw clean compile
    else
        mvn clean compile
    fi
    
    if [ $? -eq 0 ]; then
        print_status "Backend setup completed"
    else
        print_error "Backend setup failed"
        exit 1
    fi
    
    cd ..
}

# Setup mobile app
setup_mobile() {
    print_step "Setting up mobile app..."
    
    cd mobile
    
    # Install dependencies
    npm install
    
    if [ $? -eq 0 ]; then
        print_status "Mobile app dependencies installed"
    else
        print_error "Failed to install mobile app dependencies"
        exit 1
    fi
    
    # Check for React Native CLI
    if command -v react-native &> /dev/null; then
        print_status "React Native CLI found"
    else
        print_warning "React Native CLI not found. Installing globally..."
        npm install -g react-native-cli
    fi
    
    cd ..
}

# Setup web admin
setup_web_admin() {
    print_step "Setting up web admin..."
    
    cd web-admin
    
    # Install dependencies
    npm install
    
    if [ $? -eq 0 ]; then
        print_status "Web admin dependencies installed"
    else
        print_error "Failed to install web admin dependencies"
        exit 1
    fi
    
    cd ..
}

# Create environment files
create_env_files() {
    print_step "Creating environment files..."
    
    # Backend environment
    cat > backend/src/main/resources/application-local.yml << EOF
spring:
  datasource:
    url: ***************************************************************************************************
    username: root
    password: Ram\$0101
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true

jwt:
  secret: ProChatLocalSecretKeyForDevelopment2024
  expiration: 86400000

aws:
  s3:
    bucket-name: prochat-media-storage-local
    region: us-east-1
    access-key: \${AWS_ACCESS_KEY:}
    secret-key: \${AWS_SECRET_KEY:}

logging:
  level:
    com.prochat: DEBUG
EOF
    
    # Mobile app environment
    cat > mobile/.env << EOF
API_BASE_URL=http://localhost:8080/api
ENVIRONMENT=development
EOF
    
    # Web admin environment
    cat > web-admin/.env << EOF
REACT_APP_API_BASE_URL=http://localhost:8080/api
REACT_APP_ENVIRONMENT=development
EOF
    
    print_status "Environment files created"
}

# Start services
start_services() {
    print_step "Starting services..."
    
    # Start backend
    print_status "Starting backend server..."
    cd backend
    if [ -f "./mvnw" ]; then
        ./mvnw spring-boot:run &
    else
        mvn spring-boot:run &
    fi
    BACKEND_PID=$!
    cd ..
    
    # Wait for backend to start
    print_status "Waiting for backend to start..."
    sleep 10
    
    # Check if backend is running
    if curl -s http://localhost:8080/api/auth/signin > /dev/null; then
        print_status "Backend is running on http://localhost:8080"
    else
        print_warning "Backend might not be fully started yet"
    fi
    
    # Start web admin
    print_status "Starting web admin..."
    cd web-admin
    npm start &
    WEB_ADMIN_PID=$!
    cd ..
    
    print_status "Web admin starting on http://localhost:3000"
    
    # Save PIDs for cleanup
    echo $BACKEND_PID > .backend.pid
    echo $WEB_ADMIN_PID > .webadmin.pid
}

# Main setup function
main() {
    print_step "Starting ProChat setup..."
    
    check_requirements
    setup_database
    setup_backend
    setup_mobile
    setup_web_admin
    create_env_files
    
    echo ""
    print_status "Setup completed successfully! 🎉"
    echo ""
    echo "Next steps:"
    echo "1. Start the backend: cd backend && ./mvnw spring-boot:run"
    echo "2. Start the web admin: cd web-admin && npm start"
    echo "3. Start the mobile app: cd mobile && npx react-native run-android"
    echo ""
    echo "URLs:"
    echo "- Backend API: http://localhost:8080/api"
    echo "- Web Admin: http://localhost:3000"
    echo ""
    echo "Default credentials:"
    echo "- Database: root / Ram\$0101"
    echo ""
    
    read -p "Do you want to start the services now? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        start_services
        echo ""
        print_status "Services started! Check the URLs above."
        echo ""
        echo "To stop services later, run: ./stop.sh"
    fi
}

# Run main function
main
