{"ast": null, "code": "var baseGet = require('./_baseGet'),\n  baseSlice = require('./_baseSlice');\n\n/**\n * Gets the parent value at `path` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array} path The path to get the parent value of.\n * @returns {*} Returns the parent value.\n */\nfunction parent(object, path) {\n  return path.length < 2 ? object : baseGet(object, baseSlice(path, 0, -1));\n}\nmodule.exports = parent;", "map": {"version": 3, "names": ["baseGet", "require", "baseSlice", "parent", "object", "path", "length", "module", "exports"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/lodash/_parent.js"], "sourcesContent": ["var baseGet = require('./_baseGet'),\n    baseSlice = require('./_baseSlice');\n\n/**\n * Gets the parent value at `path` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array} path The path to get the parent value of.\n * @returns {*} Returns the parent value.\n */\nfunction parent(object, path) {\n  return path.length < 2 ? object : baseGet(object, baseSlice(path, 0, -1));\n}\n\nmodule.exports = parent;\n"], "mappings": "AAAA,IAAIA,OAAO,GAAGC,OAAO,CAAC,YAAY,CAAC;EAC/BC,SAAS,GAAGD,OAAO,CAAC,cAAc,CAAC;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAACC,MAAM,EAAEC,IAAI,EAAE;EAC5B,OAAOA,IAAI,CAACC,MAAM,GAAG,CAAC,GAAGF,MAAM,GAAGJ,OAAO,CAACI,MAAM,EAAEF,SAAS,CAACG,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC3E;AAEAE,MAAM,CAACC,OAAO,GAAGL,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}