package com.prochat.service;

import com.prochat.model.*;
import com.prochat.repository.TransactionRepository;
import com.prochat.repository.UserRepository;
import com.prochat.repository.WalletRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

@Service
public class WalletService {
    
    @Autowired
    private WalletRepository walletRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private TransactionRepository transactionRepository;
    
    @Autowired
    private PasswordEncoder encoder;
    
    public Wallet createWallet(User user) {
        // Check if wallet already exists
        Optional<Wallet> existingWallet = walletRepository.findByUser(user);
        if (existingWallet.isPresent()) {
            return existingWallet.get();
        }
        
        // Generate unique wallet number
        String walletNumber;
        do {
            walletNumber = generateWalletNumber();
        } while (walletRepository.existsByWalletNumber(walletNumber));
        
        Wallet wallet = new Wallet(user, walletNumber);
        return walletRepository.save(wallet);
    }
    
    @Transactional
    public Transaction sendMoney(Long senderId, String receiverWalletNumber, 
                               BigDecimal amount, String description, String pin) {
        
        // Validate sender wallet
        Optional<Wallet> senderWallet = walletRepository.findByUserId(senderId);
        if (!senderWallet.isPresent()) {
            throw new RuntimeException("Sender wallet not found");
        }
        
        // Validate receiver wallet
        Optional<Wallet> receiverWallet = walletRepository.findByWalletNumber(receiverWalletNumber);
        if (!receiverWallet.isPresent()) {
            throw new RuntimeException("Receiver wallet not found");
        }
        
        Wallet sender = senderWallet.get();
        Wallet receiver = receiverWallet.get();
        
        // Validate PIN
        if (!validatePin(sender, pin)) {
            throw new RuntimeException("Invalid PIN");
        }
        
        // Check balance
        if (sender.getBalance().compareTo(amount) < 0) {
            throw new RuntimeException("Insufficient balance");
        }
        
        // Calculate fee (2% of transaction amount)
        BigDecimal fee = amount.multiply(new BigDecimal("0.02"));
        BigDecimal totalDeduction = amount.add(fee);
        
        // Check if sender has enough balance including fee
        if (sender.getBalance().compareTo(totalDeduction) < 0) {
            throw new RuntimeException("Insufficient balance including transaction fee");
        }
        
        // Create transaction
        String transactionId = generateTransactionId();
        Transaction transaction = new Transaction(
                transactionId,
                sender,
                receiver,
                amount,
                TransactionType.SEND_MONEY,
                description
        );
        transaction.setFee(fee);
        transaction.setStatus(TransactionStatus.PROCESSING);
        
        // Save transaction
        transaction = transactionRepository.save(transaction);
        
        try {
            // Update balances
            sender.setBalance(sender.getBalance().subtract(totalDeduction));
            sender.setTotalSent(sender.getTotalSent().add(amount));
            
            receiver.setBalance(receiver.getBalance().add(amount));
            receiver.setTotalReceived(receiver.getTotalReceived().add(amount));
            
            // Save wallets
            walletRepository.save(sender);
            walletRepository.save(receiver);
            
            // Update transaction status
            transaction.setStatus(TransactionStatus.COMPLETED);
            transaction.setCompletedAt(LocalDateTime.now());
            
        } catch (Exception e) {
            // Mark transaction as failed
            transaction.setStatus(TransactionStatus.FAILED);
            transaction.setFailureReason(e.getMessage());
            throw new RuntimeException("Transaction failed: " + e.getMessage());
        } finally {
            transactionRepository.save(transaction);
        }
        
        return transaction;
    }
    
    @Transactional
    public Transaction deposit(Long userId, BigDecimal amount, String paymentMethod, String externalReference) {
        Optional<Wallet> walletOptional = walletRepository.findByUserId(userId);
        if (!walletOptional.isPresent()) {
            throw new RuntimeException("Wallet not found");
        }
        
        Wallet wallet = walletOptional.get();
        
        // Create transaction
        String transactionId = generateTransactionId();
        Transaction transaction = new Transaction(
                transactionId,
                null, // No sender for deposits
                wallet,
                amount,
                TransactionType.DEPOSIT,
                "Deposit via " + paymentMethod
        );
        transaction.setExternalReference(externalReference);
        transaction.setStatus(TransactionStatus.PROCESSING);
        
        // Save transaction
        transaction = transactionRepository.save(transaction);
        
        try {
            // Update wallet balance
            wallet.setBalance(wallet.getBalance().add(amount));
            wallet.setTotalReceived(wallet.getTotalReceived().add(amount));
            walletRepository.save(wallet);
            
            // Update transaction status
            transaction.setStatus(TransactionStatus.COMPLETED);
            transaction.setCompletedAt(LocalDateTime.now());
            
        } catch (Exception e) {
            transaction.setStatus(TransactionStatus.FAILED);
            transaction.setFailureReason(e.getMessage());
            throw new RuntimeException("Deposit failed: " + e.getMessage());
        } finally {
            transactionRepository.save(transaction);
        }
        
        return transaction;
    }
    
    @Transactional
    public Transaction withdraw(Long userId, BigDecimal amount, String withdrawMethod, 
                              String accountDetails, String pin) {
        
        Optional<Wallet> walletOptional = walletRepository.findByUserId(userId);
        if (!walletOptional.isPresent()) {
            throw new RuntimeException("Wallet not found");
        }
        
        Wallet wallet = walletOptional.get();
        
        // Validate PIN
        if (!validatePin(wallet, pin)) {
            throw new RuntimeException("Invalid PIN");
        }
        
        // Check balance
        if (wallet.getBalance().compareTo(amount) < 0) {
            throw new RuntimeException("Insufficient balance");
        }
        
        // Create transaction
        String transactionId = generateTransactionId();
        Transaction transaction = new Transaction(
                transactionId,
                wallet,
                null, // No receiver for withdrawals
                amount,
                TransactionType.WITHDRAWAL,
                "Withdrawal via " + withdrawMethod
        );
        transaction.setExternalReference(accountDetails);
        transaction.setStatus(TransactionStatus.PROCESSING);
        
        // Save transaction
        transaction = transactionRepository.save(transaction);
        
        try {
            // Update wallet balance
            wallet.setBalance(wallet.getBalance().subtract(amount));
            wallet.setTotalSent(wallet.getTotalSent().add(amount));
            walletRepository.save(wallet);
            
            // Update transaction status
            transaction.setStatus(TransactionStatus.COMPLETED);
            transaction.setCompletedAt(LocalDateTime.now());
            
        } catch (Exception e) {
            transaction.setStatus(TransactionStatus.FAILED);
            transaction.setFailureReason(e.getMessage());
            throw new RuntimeException("Withdrawal failed: " + e.getMessage());
        } finally {
            transactionRepository.save(transaction);
        }
        
        return transaction;
    }
    
    public void setPin(Long userId, String pin) {
        Optional<Wallet> walletOptional = walletRepository.findByUserId(userId);
        if (!walletOptional.isPresent()) {
            throw new RuntimeException("Wallet not found");
        }
        
        Wallet wallet = walletOptional.get();
        
        // Validate PIN format (4 digits)
        if (!pin.matches("\\d{4}")) {
            throw new RuntimeException("PIN must be 4 digits");
        }
        
        // Hash and save PIN
        wallet.setPinHash(encoder.encode(pin));
        wallet.setIsPinSet(true);
        walletRepository.save(wallet);
    }
    
    private boolean validatePin(Wallet wallet, String pin) {
        if (!wallet.getIsPinSet() || wallet.getPinHash() == null) {
            throw new RuntimeException("PIN not set. Please set your PIN first.");
        }
        
        return encoder.matches(pin, wallet.getPinHash());
    }
    
    private String generateWalletNumber() {
        // Generate wallet number in format: PC + 6 random digits
        long number = (long) (Math.random() * 1000000);
        return String.format("PC%06d", number);
    }
    
    private String generateTransactionId() {
        return "TXN" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
}
