package com.prochat.model.monetization;

import com.prochat.model.User;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "donation_campaigns")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DonationCampaign {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "creator_id", nullable = false)
    private User creator;
    
    @Column(name = "title", nullable = false)
    private String title;
    
    @Column(name = "description", nullable = false, length = 2000)
    private String description;
    
    @Column(name = "target_amount", nullable = false, precision = 12, scale = 2)
    private BigDecimal targetAmount;
    
    @Column(name = "current_amount", nullable = false, precision = 12, scale = 2)
    private BigDecimal currentAmount = BigDecimal.ZERO;
    
    @Column(name = "currency", nullable = false)
    private String currency = "TZS";
    
    @Enumerated(EnumType.STRING)
    @Column(name = "category", nullable = false)
    private DonationCategory category;
    
    @Column(name = "image_url")
    private String imageUrl;
    
    @Column(name = "video_url")
    private String videoUrl;
    
    @Column(name = "beneficiary_name")
    private String beneficiaryName;
    
    @Column(name = "beneficiary_contact")
    private String beneficiaryContact;
    
    @Column(name = "location")
    private String location;
    
    @Column(name = "start_date", nullable = false)
    private LocalDateTime startDate;
    
    @Column(name = "end_date", nullable = false)
    private LocalDateTime endDate;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private CampaignStatus status = CampaignStatus.PENDING;
    
    @Column(name = "platform_fee_percentage", nullable = false, precision = 5, scale = 2)
    private BigDecimal platformFeePercentage = new BigDecimal("5.00"); // 5% platform fee
    
    @Column(name = "is_featured", nullable = false)
    private Boolean isFeatured = false;
    
    @Column(name = "is_urgent", nullable = false)
    private Boolean isUrgent = false;
    
    @Column(name = "verification_documents")
    private String verificationDocuments;
    
    @Column(name = "admin_notes")
    private String adminNotes;
    
    @Column(name = "approved_by")
    private Long approvedBy;
    
    @Column(name = "approved_at")
    private LocalDateTime approvedAt;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @OneToMany(mappedBy = "campaign", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<DonationTransaction> donations;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        if (startDate == null) {
            startDate = LocalDateTime.now();
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    public enum DonationCategory {
        MEDICAL("Matibabu", "Msaada wa matibabu"),
        EDUCATION("Elimu", "Msaada wa masomo"),
        EMERGENCY("Dharura", "Msaada wa dharura"),
        COMMUNITY("Jamii", "Miradi ya kijamii"),
        BUSINESS("Biashara", "Msaada wa biashara"),
        CHARITY("Hisani", "Kazi za hisani"),
        DISASTER("Majanga", "Msaada wa majanga"),
        FAMILY("Familia", "Msaada wa kifamilia"),
        SPORTS("Michezo", "Msaada wa michezo"),
        ARTS("Sanaa", "Msaada wa sanaa");
        
        private final String displayName;
        private final String description;
        
        DonationCategory(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public enum CampaignStatus {
        PENDING("Inasubiri Idhini"),
        APPROVED("Imeidhinishwa"),
        REJECTED("Imekataliwa"),
        ACTIVE("Inaendelea"),
        COMPLETED("Imekamilika"),
        CANCELLED("Imeghairiwa"),
        SUSPENDED("Imesimamishwa");
        
        private final String displayName;
        
        CampaignStatus(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
}

@Entity
@Table(name = "donation_transactions")
@Data
@NoArgsConstructor
@AllArgsConstructor
class DonationTransaction {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "campaign_id", nullable = false)
    private DonationCampaign campaign;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "donor_id", nullable = false)
    private User donor;
    
    @Column(name = "amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal amount;
    
    @Column(name = "platform_fee", nullable = false, precision = 10, scale = 2)
    private BigDecimal platformFee;
    
    @Column(name = "net_amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal netAmount;
    
    @Column(name = "currency", nullable = false)
    private String currency = "TZS";
    
    @Column(name = "message")
    private String message;
    
    @Column(name = "is_anonymous", nullable = false)
    private Boolean isAnonymous = false;
    
    @Column(name = "payment_method")
    private String paymentMethod;
    
    @Column(name = "transaction_reference")
    private String transactionReference;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private TransactionStatus status = TransactionStatus.COMPLETED;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
    
    public enum TransactionStatus {
        PENDING("Inasubiri"),
        COMPLETED("Imekamilika"),
        FAILED("Imeshindwa"),
        REFUNDED("Imerudishwa");
        
        private final String displayName;
        
        TransactionStatus(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
}
