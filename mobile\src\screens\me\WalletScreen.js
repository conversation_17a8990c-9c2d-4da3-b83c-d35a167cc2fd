import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  RefreshControl,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors, typography, spacing } from '../../theme/theme';
import { walletAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

export default function WalletScreen({ navigation }) {
  const { user } = useAuth();
  const [wallet, setWallet] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadWalletData();
  }, []);

  const loadWalletData = async () => {
    try {
      const response = await walletAPI.getBalance();
      if (response.success) {
        setWallet(response.data);
      }
    } catch (error) {
      console.error('Error loading wallet:', error);
      Alert.alert('Hitilafu', 'Imeshindwa kupakia taarifa za wallet');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadWalletData();
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const handleSendMoney = () => {
    navigation.navigate('SendMoney');
  };

  const handleWithdraw = () => {
    navigation.navigate('Withdraw');
  };

  const handleDeposit = () => {
    navigation.navigate('Deposit');
  };

  const handleTransactionHistory = () => {
    navigation.navigate('TransactionHistory');
  };

  const handleRequestMoney = () => {
    navigation.navigate('RequestMoney');
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Inapakia...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>ProPay Wallet</Text>
          <TouchableOpacity style={styles.settingsButton}>
            <Icon name="settings" size={24} color={colors.text} />
          </TouchableOpacity>
        </View>

        {/* Balance Card */}
        <View style={styles.balanceCard}>
          <View style={styles.balanceHeader}>
            <Text style={styles.balanceLabel}>Salio Lako</Text>
            <View style={styles.walletInfo}>
              <Text style={styles.walletNumber}>
                {wallet?.walletNumber || 'N/A'}
              </Text>
              <View style={[
                styles.statusBadge,
                { backgroundColor: wallet?.isActive ? colors.success : colors.error }
              ]}>
                <Text style={styles.statusText}>
                  {wallet?.isActive ? 'Active' : 'Inactive'}
                </Text>
              </View>
            </View>
          </View>
          
          <Text style={styles.balanceAmount}>
            {formatCurrency(wallet?.balance || 0)}
          </Text>
          
          <Text style={styles.balanceSubtext}>
            Salio halisi la wallet yako
          </Text>
        </View>

        {/* Quick Actions */}
        <View style={styles.actionsContainer}>
          <Text style={styles.sectionTitle}>Vitendo vya Haraka</Text>
          
          <View style={styles.actionsGrid}>
            <TouchableOpacity style={styles.actionButton} onPress={handleSendMoney}>
              <View style={[styles.actionIcon, { backgroundColor: colors.primary }]}>
                <Icon name="send" size={24} color={colors.white} />
              </View>
              <Text style={styles.actionText}>Tuma Pesa</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton} onPress={handleWithdraw}>
              <View style={[styles.actionIcon, { backgroundColor: colors.warning }]}>
                <Icon name="account-balance" size={24} color={colors.white} />
              </View>
              <Text style={styles.actionText}>Toa Pesa</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton} onPress={handleDeposit}>
              <View style={[styles.actionIcon, { backgroundColor: colors.success }]}>
                <Icon name="add-circle" size={24} color={colors.white} />
              </View>
              <Text style={styles.actionText}>Ongeza Pesa</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton} onPress={handleRequestMoney}>
              <View style={[styles.actionIcon, { backgroundColor: colors.info }]}>
                <Icon name="request-quote" size={24} color={colors.white} />
              </View>
              <Text style={styles.actionText}>Omba Pesa</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Recent Transactions */}
        <View style={styles.transactionsContainer}>
          <View style={styles.transactionsHeader}>
            <Text style={styles.sectionTitle}>Miamala ya Hivi Karibuni</Text>
            <TouchableOpacity onPress={handleTransactionHistory}>
              <Text style={styles.viewAllText}>Ona Yote</Text>
            </TouchableOpacity>
          </View>
          
          {/* Transaction items would go here */}
          <View style={styles.emptyTransactions}>
            <Icon name="receipt" size={48} color={colors.gray} />
            <Text style={styles.emptyText}>Hakuna miamala ya hivi karibuni</Text>
            <TouchableOpacity onPress={handleTransactionHistory}>
              <Text style={styles.viewHistoryText}>Ona Historia Kamili</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Additional Services */}
        <View style={styles.servicesContainer}>
          <Text style={styles.sectionTitle}>Huduma Zingine</Text>
          
          <TouchableOpacity style={styles.serviceItem}>
            <Icon name="qr-code" size={24} color={colors.primary} />
            <Text style={styles.serviceText}>QR Code ya Malipo</Text>
            <Icon name="chevron-right" size={24} color={colors.gray} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.serviceItem}>
            <Icon name="phone" size={24} color={colors.primary} />
            <Text style={styles.serviceText}>Jaza Mitandao</Text>
            <Icon name="chevron-right" size={24} color={colors.gray} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.serviceItem}>
            <Icon name="receipt-long" size={24} color={colors.primary} />
            <Text style={styles.serviceText}>Lipa Bili</Text>
            <Icon name="chevron-right" size={24} color={colors.gray} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.serviceItem}>
            <Icon name="help" size={24} color={colors.primary} />
            <Text style={styles.serviceText}>Msaada</Text>
            <Icon name="chevron-right" size={24} color={colors.gray} />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  backButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
  },
  settingsButton: {
    padding: spacing.sm,
  },
  balanceCard: {
    backgroundColor: colors.primary,
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
    borderRadius: 16,
    padding: spacing.lg,
  },
  balanceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  balanceLabel: {
    fontSize: typography.fontSize.md,
    color: colors.white,
    opacity: 0.8,
  },
  walletInfo: {
    alignItems: 'flex-end',
  },
  walletNumber: {
    fontSize: typography.fontSize.sm,
    color: colors.white,
    opacity: 0.8,
    marginBottom: spacing.xs,
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: 2,
    borderRadius: 12,
  },
  statusText: {
    fontSize: typography.fontSize.xs,
    color: colors.white,
    fontWeight: typography.fontWeight.medium,
  },
  balanceAmount: {
    fontSize: 36,
    fontWeight: typography.fontWeight.bold,
    color: colors.white,
    marginBottom: spacing.xs,
  },
  balanceSubtext: {
    fontSize: typography.fontSize.sm,
    color: colors.white,
    opacity: 0.8,
  },
  actionsContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionButton: {
    width: '48%',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  actionIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  actionText: {
    fontSize: typography.fontSize.sm,
    color: colors.text,
    textAlign: 'center',
  },
  transactionsContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.lg,
  },
  transactionsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  viewAllText: {
    fontSize: typography.fontSize.sm,
    color: colors.primary,
    fontWeight: typography.fontWeight.medium,
  },
  emptyTransactions: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  emptyText: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  viewHistoryText: {
    fontSize: typography.fontSize.sm,
    color: colors.primary,
    fontWeight: typography.fontWeight.medium,
  },
  servicesContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  serviceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  serviceText: {
    flex: 1,
    fontSize: typography.fontSize.md,
    color: colors.text,
    marginLeft: spacing.md,
  },
});
