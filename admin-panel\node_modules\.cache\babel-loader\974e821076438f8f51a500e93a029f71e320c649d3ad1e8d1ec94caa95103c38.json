{"ast": null, "code": "export function blur(values, r) {\n  if (!((r = +r) >= 0)) throw new RangeError(\"invalid r\");\n  let length = values.length;\n  if (!((length = Math.floor(length)) >= 0)) throw new RangeError(\"invalid length\");\n  if (!length || !r) return values;\n  const blur = blurf(r);\n  const temp = values.slice();\n  blur(values, temp, 0, length, 1);\n  blur(temp, values, 0, length, 1);\n  blur(values, temp, 0, length, 1);\n  return values;\n}\nexport const blur2 = Blur2(blurf);\nexport const blurImage = Blur2(blurfImage);\nfunction Blur2(blur) {\n  return function (data, rx, ry = rx) {\n    if (!((rx = +rx) >= 0)) throw new RangeError(\"invalid rx\");\n    if (!((ry = +ry) >= 0)) throw new RangeError(\"invalid ry\");\n    let {\n      data: values,\n      width,\n      height\n    } = data;\n    if (!((width = Math.floor(width)) >= 0)) throw new RangeError(\"invalid width\");\n    if (!((height = Math.floor(height !== undefined ? height : values.length / width)) >= 0)) throw new RangeError(\"invalid height\");\n    if (!width || !height || !rx && !ry) return data;\n    const blurx = rx && blur(rx);\n    const blury = ry && blur(ry);\n    const temp = values.slice();\n    if (blurx && blury) {\n      blurh(blurx, temp, values, width, height);\n      blurh(blurx, values, temp, width, height);\n      blurh(blurx, temp, values, width, height);\n      blurv(blury, values, temp, width, height);\n      blurv(blury, temp, values, width, height);\n      blurv(blury, values, temp, width, height);\n    } else if (blurx) {\n      blurh(blurx, values, temp, width, height);\n      blurh(blurx, temp, values, width, height);\n      blurh(blurx, values, temp, width, height);\n    } else if (blury) {\n      blurv(blury, values, temp, width, height);\n      blurv(blury, temp, values, width, height);\n      blurv(blury, values, temp, width, height);\n    }\n    return data;\n  };\n}\nfunction blurh(blur, T, S, w, h) {\n  for (let y = 0, n = w * h; y < n;) {\n    blur(T, S, y, y += w, 1);\n  }\n}\nfunction blurv(blur, T, S, w, h) {\n  for (let x = 0, n = w * h; x < w; ++x) {\n    blur(T, S, x, x + n, w);\n  }\n}\nfunction blurfImage(radius) {\n  const blur = blurf(radius);\n  return (T, S, start, stop, step) => {\n    start <<= 2, stop <<= 2, step <<= 2;\n    blur(T, S, start + 0, stop + 0, step);\n    blur(T, S, start + 1, stop + 1, step);\n    blur(T, S, start + 2, stop + 2, step);\n    blur(T, S, start + 3, stop + 3, step);\n  };\n}\n\n// Given a target array T, a source array S, sets each value T[i] to the average\n// of {S[i - r], …, S[i], …, S[i + r]}, where r = ⌊radius⌋, start <= i < stop,\n// for each i, i + step, i + 2 * step, etc., and where S[j] is clamped between\n// S[start] (inclusive) and S[stop] (exclusive). If the given radius is not an\n// integer, S[i - r - 1] and S[i + r + 1] are added to the sum, each weighted\n// according to r - ⌊radius⌋.\nfunction blurf(radius) {\n  const radius0 = Math.floor(radius);\n  if (radius0 === radius) return bluri(radius);\n  const t = radius - radius0;\n  const w = 2 * radius + 1;\n  return (T, S, start, stop, step) => {\n    // stop must be aligned!\n    if (!((stop -= step) >= start)) return; // inclusive stop\n    let sum = radius0 * S[start];\n    const s0 = step * radius0;\n    const s1 = s0 + step;\n    for (let i = start, j = start + s0; i < j; i += step) {\n      sum += S[Math.min(stop, i)];\n    }\n    for (let i = start, j = stop; i <= j; i += step) {\n      sum += S[Math.min(stop, i + s0)];\n      T[i] = (sum + t * (S[Math.max(start, i - s1)] + S[Math.min(stop, i + s1)])) / w;\n      sum -= S[Math.max(start, i - s0)];\n    }\n  };\n}\n\n// Like blurf, but optimized for integer radius.\nfunction bluri(radius) {\n  const w = 2 * radius + 1;\n  return (T, S, start, stop, step) => {\n    // stop must be aligned!\n    if (!((stop -= step) >= start)) return; // inclusive stop\n    let sum = radius * S[start];\n    const s = step * radius;\n    for (let i = start, j = start + s; i < j; i += step) {\n      sum += S[Math.min(stop, i)];\n    }\n    for (let i = start, j = stop; i <= j; i += step) {\n      sum += S[Math.min(stop, i + s)];\n      T[i] = sum / w;\n      sum -= S[Math.max(start, i - s)];\n    }\n  };\n}", "map": {"version": 3, "names": ["blur", "values", "r", "RangeError", "length", "Math", "floor", "blurf", "temp", "slice", "blur2", "Blur2", "blurImage", "blurfImage", "data", "rx", "ry", "width", "height", "undefined", "blurx", "blury", "blurh", "blurv", "T", "S", "w", "h", "y", "n", "x", "radius", "start", "stop", "step", "radius0", "bluri", "t", "sum", "s0", "s1", "i", "j", "min", "max", "s"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/d3-array/src/blur.js"], "sourcesContent": ["export function blur(values, r) {\n  if (!((r = +r) >= 0)) throw new RangeError(\"invalid r\");\n  let length = values.length;\n  if (!((length = Math.floor(length)) >= 0)) throw new RangeError(\"invalid length\");\n  if (!length || !r) return values;\n  const blur = blurf(r);\n  const temp = values.slice();\n  blur(values, temp, 0, length, 1);\n  blur(temp, values, 0, length, 1);\n  blur(values, temp, 0, length, 1);\n  return values;\n}\n\nexport const blur2 = Blur2(blurf);\n\nexport const blurImage = Blur2(blurfImage);\n\nfunction Blur2(blur) {\n  return function(data, rx, ry = rx) {\n    if (!((rx = +rx) >= 0)) throw new RangeError(\"invalid rx\");\n    if (!((ry = +ry) >= 0)) throw new RangeError(\"invalid ry\");\n    let {data: values, width, height} = data;\n    if (!((width = Math.floor(width)) >= 0)) throw new RangeError(\"invalid width\");\n    if (!((height = Math.floor(height !== undefined ? height : values.length / width)) >= 0)) throw new RangeError(\"invalid height\");\n    if (!width || !height || (!rx && !ry)) return data;\n    const blurx = rx && blur(rx);\n    const blury = ry && blur(ry);\n    const temp = values.slice();\n    if (blurx && blury) {\n      blurh(blurx, temp, values, width, height);\n      blurh(blurx, values, temp, width, height);\n      blurh(blurx, temp, values, width, height);\n      blurv(blury, values, temp, width, height);\n      blurv(blury, temp, values, width, height);\n      blurv(blury, values, temp, width, height);\n    } else if (blurx) {\n      blurh(blurx, values, temp, width, height);\n      blurh(blurx, temp, values, width, height);\n      blurh(blurx, values, temp, width, height);\n    } else if (blury) {\n      blurv(blury, values, temp, width, height);\n      blurv(blury, temp, values, width, height);\n      blurv(blury, values, temp, width, height);\n    }\n    return data;\n  };\n}\n\nfunction blurh(blur, T, S, w, h) {\n  for (let y = 0, n = w * h; y < n;) {\n    blur(T, S, y, y += w, 1);\n  }\n}\n\nfunction blurv(blur, T, S, w, h) {\n  for (let x = 0, n = w * h; x < w; ++x) {\n    blur(T, S, x, x + n, w);\n  }\n}\n\nfunction blurfImage(radius) {\n  const blur = blurf(radius);\n  return (T, S, start, stop, step) => {\n    start <<= 2, stop <<= 2, step <<= 2;\n    blur(T, S, start + 0, stop + 0, step);\n    blur(T, S, start + 1, stop + 1, step);\n    blur(T, S, start + 2, stop + 2, step);\n    blur(T, S, start + 3, stop + 3, step);\n  };\n}\n\n// Given a target array T, a source array S, sets each value T[i] to the average\n// of {S[i - r], …, S[i], …, S[i + r]}, where r = ⌊radius⌋, start <= i < stop,\n// for each i, i + step, i + 2 * step, etc., and where S[j] is clamped between\n// S[start] (inclusive) and S[stop] (exclusive). If the given radius is not an\n// integer, S[i - r - 1] and S[i + r + 1] are added to the sum, each weighted\n// according to r - ⌊radius⌋.\nfunction blurf(radius) {\n  const radius0 = Math.floor(radius);\n  if (radius0 === radius) return bluri(radius);\n  const t = radius - radius0;\n  const w = 2 * radius + 1;\n  return (T, S, start, stop, step) => { // stop must be aligned!\n    if (!((stop -= step) >= start)) return; // inclusive stop\n    let sum = radius0 * S[start];\n    const s0 = step * radius0;\n    const s1 = s0 + step;\n    for (let i = start, j = start + s0; i < j; i += step) {\n      sum += S[Math.min(stop, i)];\n    }\n    for (let i = start, j = stop; i <= j; i += step) {\n      sum += S[Math.min(stop, i + s0)];\n      T[i] = (sum + t * (S[Math.max(start, i - s1)] + S[Math.min(stop, i + s1)])) / w;\n      sum -= S[Math.max(start, i - s0)];\n    }\n  };\n}\n\n// Like blurf, but optimized for integer radius.\nfunction bluri(radius) {\n  const w = 2 * radius + 1;\n  return (T, S, start, stop, step) => { // stop must be aligned!\n    if (!((stop -= step) >= start)) return; // inclusive stop\n    let sum = radius * S[start];\n    const s = step * radius;\n    for (let i = start, j = start + s; i < j; i += step) {\n      sum += S[Math.min(stop, i)];\n    }\n    for (let i = start, j = stop; i <= j; i += step) {\n      sum += S[Math.min(stop, i + s)];\n      T[i] = sum / w;\n      sum -= S[Math.max(start, i - s)];\n    }\n  };\n}\n"], "mappings": "AAAA,OAAO,SAASA,IAAIA,CAACC,MAAM,EAAEC,CAAC,EAAE;EAC9B,IAAI,EAAE,CAACA,CAAC,GAAG,CAACA,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,IAAIC,UAAU,CAAC,WAAW,CAAC;EACvD,IAAIC,MAAM,GAAGH,MAAM,CAACG,MAAM;EAC1B,IAAI,EAAE,CAACA,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACF,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,IAAID,UAAU,CAAC,gBAAgB,CAAC;EACjF,IAAI,CAACC,MAAM,IAAI,CAACF,CAAC,EAAE,OAAOD,MAAM;EAChC,MAAMD,IAAI,GAAGO,KAAK,CAACL,CAAC,CAAC;EACrB,MAAMM,IAAI,GAAGP,MAAM,CAACQ,KAAK,CAAC,CAAC;EAC3BT,IAAI,CAACC,MAAM,EAAEO,IAAI,EAAE,CAAC,EAAEJ,MAAM,EAAE,CAAC,CAAC;EAChCJ,IAAI,CAACQ,IAAI,EAAEP,MAAM,EAAE,CAAC,EAAEG,MAAM,EAAE,CAAC,CAAC;EAChCJ,IAAI,CAACC,MAAM,EAAEO,IAAI,EAAE,CAAC,EAAEJ,MAAM,EAAE,CAAC,CAAC;EAChC,OAAOH,MAAM;AACf;AAEA,OAAO,MAAMS,KAAK,GAAGC,KAAK,CAACJ,KAAK,CAAC;AAEjC,OAAO,MAAMK,SAAS,GAAGD,KAAK,CAACE,UAAU,CAAC;AAE1C,SAASF,KAAKA,CAACX,IAAI,EAAE;EACnB,OAAO,UAASc,IAAI,EAAEC,EAAE,EAAEC,EAAE,GAAGD,EAAE,EAAE;IACjC,IAAI,EAAE,CAACA,EAAE,GAAG,CAACA,EAAE,KAAK,CAAC,CAAC,EAAE,MAAM,IAAIZ,UAAU,CAAC,YAAY,CAAC;IAC1D,IAAI,EAAE,CAACa,EAAE,GAAG,CAACA,EAAE,KAAK,CAAC,CAAC,EAAE,MAAM,IAAIb,UAAU,CAAC,YAAY,CAAC;IAC1D,IAAI;MAACW,IAAI,EAAEb,MAAM;MAAEgB,KAAK;MAAEC;IAAM,CAAC,GAAGJ,IAAI;IACxC,IAAI,EAAE,CAACG,KAAK,GAAGZ,IAAI,CAACC,KAAK,CAACW,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,IAAId,UAAU,CAAC,eAAe,CAAC;IAC9E,IAAI,EAAE,CAACe,MAAM,GAAGb,IAAI,CAACC,KAAK,CAACY,MAAM,KAAKC,SAAS,GAAGD,MAAM,GAAGjB,MAAM,CAACG,MAAM,GAAGa,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,IAAId,UAAU,CAAC,gBAAgB,CAAC;IAChI,IAAI,CAACc,KAAK,IAAI,CAACC,MAAM,IAAK,CAACH,EAAE,IAAI,CAACC,EAAG,EAAE,OAAOF,IAAI;IAClD,MAAMM,KAAK,GAAGL,EAAE,IAAIf,IAAI,CAACe,EAAE,CAAC;IAC5B,MAAMM,KAAK,GAAGL,EAAE,IAAIhB,IAAI,CAACgB,EAAE,CAAC;IAC5B,MAAMR,IAAI,GAAGP,MAAM,CAACQ,KAAK,CAAC,CAAC;IAC3B,IAAIW,KAAK,IAAIC,KAAK,EAAE;MAClBC,KAAK,CAACF,KAAK,EAAEZ,IAAI,EAAEP,MAAM,EAAEgB,KAAK,EAAEC,MAAM,CAAC;MACzCI,KAAK,CAACF,KAAK,EAAEnB,MAAM,EAAEO,IAAI,EAAES,KAAK,EAAEC,MAAM,CAAC;MACzCI,KAAK,CAACF,KAAK,EAAEZ,IAAI,EAAEP,MAAM,EAAEgB,KAAK,EAAEC,MAAM,CAAC;MACzCK,KAAK,CAACF,KAAK,EAAEpB,MAAM,EAAEO,IAAI,EAAES,KAAK,EAAEC,MAAM,CAAC;MACzCK,KAAK,CAACF,KAAK,EAAEb,IAAI,EAAEP,MAAM,EAAEgB,KAAK,EAAEC,MAAM,CAAC;MACzCK,KAAK,CAACF,KAAK,EAAEpB,MAAM,EAAEO,IAAI,EAAES,KAAK,EAAEC,MAAM,CAAC;IAC3C,CAAC,MAAM,IAAIE,KAAK,EAAE;MAChBE,KAAK,CAACF,KAAK,EAAEnB,MAAM,EAAEO,IAAI,EAAES,KAAK,EAAEC,MAAM,CAAC;MACzCI,KAAK,CAACF,KAAK,EAAEZ,IAAI,EAAEP,MAAM,EAAEgB,KAAK,EAAEC,MAAM,CAAC;MACzCI,KAAK,CAACF,KAAK,EAAEnB,MAAM,EAAEO,IAAI,EAAES,KAAK,EAAEC,MAAM,CAAC;IAC3C,CAAC,MAAM,IAAIG,KAAK,EAAE;MAChBE,KAAK,CAACF,KAAK,EAAEpB,MAAM,EAAEO,IAAI,EAAES,KAAK,EAAEC,MAAM,CAAC;MACzCK,KAAK,CAACF,KAAK,EAAEb,IAAI,EAAEP,MAAM,EAAEgB,KAAK,EAAEC,MAAM,CAAC;MACzCK,KAAK,CAACF,KAAK,EAAEpB,MAAM,EAAEO,IAAI,EAAES,KAAK,EAAEC,MAAM,CAAC;IAC3C;IACA,OAAOJ,IAAI;EACb,CAAC;AACH;AAEA,SAASQ,KAAKA,CAACtB,IAAI,EAAEwB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGH,CAAC,GAAGC,CAAC,EAAEC,CAAC,GAAGC,CAAC,GAAG;IACjC7B,IAAI,CAACwB,CAAC,EAAEC,CAAC,EAAEG,CAAC,EAAEA,CAAC,IAAIF,CAAC,EAAE,CAAC,CAAC;EAC1B;AACF;AAEA,SAASH,KAAKA,CAACvB,IAAI,EAAEwB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC/B,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAED,CAAC,GAAGH,CAAC,GAAGC,CAAC,EAAEG,CAAC,GAAGJ,CAAC,EAAE,EAAEI,CAAC,EAAE;IACrC9B,IAAI,CAACwB,CAAC,EAAEC,CAAC,EAAEK,CAAC,EAAEA,CAAC,GAAGD,CAAC,EAAEH,CAAC,CAAC;EACzB;AACF;AAEA,SAASb,UAAUA,CAACkB,MAAM,EAAE;EAC1B,MAAM/B,IAAI,GAAGO,KAAK,CAACwB,MAAM,CAAC;EAC1B,OAAO,CAACP,CAAC,EAAEC,CAAC,EAAEO,KAAK,EAAEC,IAAI,EAAEC,IAAI,KAAK;IAClCF,KAAK,KAAK,CAAC,EAAEC,IAAI,KAAK,CAAC,EAAEC,IAAI,KAAK,CAAC;IACnClC,IAAI,CAACwB,CAAC,EAAEC,CAAC,EAAEO,KAAK,GAAG,CAAC,EAAEC,IAAI,GAAG,CAAC,EAAEC,IAAI,CAAC;IACrClC,IAAI,CAACwB,CAAC,EAAEC,CAAC,EAAEO,KAAK,GAAG,CAAC,EAAEC,IAAI,GAAG,CAAC,EAAEC,IAAI,CAAC;IACrClC,IAAI,CAACwB,CAAC,EAAEC,CAAC,EAAEO,KAAK,GAAG,CAAC,EAAEC,IAAI,GAAG,CAAC,EAAEC,IAAI,CAAC;IACrClC,IAAI,CAACwB,CAAC,EAAEC,CAAC,EAAEO,KAAK,GAAG,CAAC,EAAEC,IAAI,GAAG,CAAC,EAAEC,IAAI,CAAC;EACvC,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS3B,KAAKA,CAACwB,MAAM,EAAE;EACrB,MAAMI,OAAO,GAAG9B,IAAI,CAACC,KAAK,CAACyB,MAAM,CAAC;EAClC,IAAII,OAAO,KAAKJ,MAAM,EAAE,OAAOK,KAAK,CAACL,MAAM,CAAC;EAC5C,MAAMM,CAAC,GAAGN,MAAM,GAAGI,OAAO;EAC1B,MAAMT,CAAC,GAAG,CAAC,GAAGK,MAAM,GAAG,CAAC;EACxB,OAAO,CAACP,CAAC,EAAEC,CAAC,EAAEO,KAAK,EAAEC,IAAI,EAAEC,IAAI,KAAK;IAAE;IACpC,IAAI,EAAE,CAACD,IAAI,IAAIC,IAAI,KAAKF,KAAK,CAAC,EAAE,OAAO,CAAC;IACxC,IAAIM,GAAG,GAAGH,OAAO,GAAGV,CAAC,CAACO,KAAK,CAAC;IAC5B,MAAMO,EAAE,GAAGL,IAAI,GAAGC,OAAO;IACzB,MAAMK,EAAE,GAAGD,EAAE,GAAGL,IAAI;IACpB,KAAK,IAAIO,CAAC,GAAGT,KAAK,EAAEU,CAAC,GAAGV,KAAK,GAAGO,EAAE,EAAEE,CAAC,GAAGC,CAAC,EAAED,CAAC,IAAIP,IAAI,EAAE;MACpDI,GAAG,IAAIb,CAAC,CAACpB,IAAI,CAACsC,GAAG,CAACV,IAAI,EAAEQ,CAAC,CAAC,CAAC;IAC7B;IACA,KAAK,IAAIA,CAAC,GAAGT,KAAK,EAAEU,CAAC,GAAGT,IAAI,EAAEQ,CAAC,IAAIC,CAAC,EAAED,CAAC,IAAIP,IAAI,EAAE;MAC/CI,GAAG,IAAIb,CAAC,CAACpB,IAAI,CAACsC,GAAG,CAACV,IAAI,EAAEQ,CAAC,GAAGF,EAAE,CAAC,CAAC;MAChCf,CAAC,CAACiB,CAAC,CAAC,GAAG,CAACH,GAAG,GAAGD,CAAC,IAAIZ,CAAC,CAACpB,IAAI,CAACuC,GAAG,CAACZ,KAAK,EAAES,CAAC,GAAGD,EAAE,CAAC,CAAC,GAAGf,CAAC,CAACpB,IAAI,CAACsC,GAAG,CAACV,IAAI,EAAEQ,CAAC,GAAGD,EAAE,CAAC,CAAC,CAAC,IAAId,CAAC;MAC/EY,GAAG,IAAIb,CAAC,CAACpB,IAAI,CAACuC,GAAG,CAACZ,KAAK,EAAES,CAAC,GAAGF,EAAE,CAAC,CAAC;IACnC;EACF,CAAC;AACH;;AAEA;AACA,SAASH,KAAKA,CAACL,MAAM,EAAE;EACrB,MAAML,CAAC,GAAG,CAAC,GAAGK,MAAM,GAAG,CAAC;EACxB,OAAO,CAACP,CAAC,EAAEC,CAAC,EAAEO,KAAK,EAAEC,IAAI,EAAEC,IAAI,KAAK;IAAE;IACpC,IAAI,EAAE,CAACD,IAAI,IAAIC,IAAI,KAAKF,KAAK,CAAC,EAAE,OAAO,CAAC;IACxC,IAAIM,GAAG,GAAGP,MAAM,GAAGN,CAAC,CAACO,KAAK,CAAC;IAC3B,MAAMa,CAAC,GAAGX,IAAI,GAAGH,MAAM;IACvB,KAAK,IAAIU,CAAC,GAAGT,KAAK,EAAEU,CAAC,GAAGV,KAAK,GAAGa,CAAC,EAAEJ,CAAC,GAAGC,CAAC,EAAED,CAAC,IAAIP,IAAI,EAAE;MACnDI,GAAG,IAAIb,CAAC,CAACpB,IAAI,CAACsC,GAAG,CAACV,IAAI,EAAEQ,CAAC,CAAC,CAAC;IAC7B;IACA,KAAK,IAAIA,CAAC,GAAGT,KAAK,EAAEU,CAAC,GAAGT,IAAI,EAAEQ,CAAC,IAAIC,CAAC,EAAED,CAAC,IAAIP,IAAI,EAAE;MAC/CI,GAAG,IAAIb,CAAC,CAACpB,IAAI,CAACsC,GAAG,CAACV,IAAI,EAAEQ,CAAC,GAAGI,CAAC,CAAC,CAAC;MAC/BrB,CAAC,CAACiB,CAAC,CAAC,GAAGH,GAAG,GAAGZ,CAAC;MACdY,GAAG,IAAIb,CAAC,CAACpB,IAAI,CAACuC,GAAG,CAACZ,KAAK,EAAES,CAAC,GAAGI,CAAC,CAAC,CAAC;IAClC;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}