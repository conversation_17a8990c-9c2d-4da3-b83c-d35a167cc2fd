{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nconst UNINITIALIZED = {};\n\n/**\n * A React.useRef() that is initialized lazily with a function. Note that it accepts an optional\n * initialization argument, so the initialization function doesn't need to be an inline closure.\n *\n * @usage\n *   const ref = useLazyRef(sortColumns, columns)\n */\nexport default function useLazyRef(init, initArg) {\n  const ref = React.useRef(UNINITIALIZED);\n  if (ref.current === UNINITIALIZED) {\n    ref.current = init(initArg);\n  }\n  return ref;\n}", "map": {"version": 3, "names": ["React", "UNINITIALIZED", "useLazyRef", "init", "initArg", "ref", "useRef", "current"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/@mui/utils/esm/useLazyRef/useLazyRef.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nconst UNINITIALIZED = {};\n\n/**\n * A React.useRef() that is initialized lazily with a function. Note that it accepts an optional\n * initialization argument, so the initialization function doesn't need to be an inline closure.\n *\n * @usage\n *   const ref = useLazyRef(sortColumns, columns)\n */\nexport default function useLazyRef(init, initArg) {\n  const ref = React.useRef(UNINITIALIZED);\n  if (ref.current === UNINITIALIZED) {\n    ref.current = init(initArg);\n  }\n  return ref;\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,aAAa,GAAG,CAAC,CAAC;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,UAAUA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAChD,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAACL,aAAa,CAAC;EACvC,IAAII,GAAG,CAACE,OAAO,KAAKN,aAAa,EAAE;IACjCI,GAAG,CAACE,OAAO,GAAGJ,IAAI,CAACC,OAAO,CAAC;EAC7B;EACA,OAAOC,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}