import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Chip,
  Avatar,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  InputAdornment,
  Tabs,
  Tab,
  Alert,
} from '@mui/material';
import {
  Search,
  FilterList,
  MoreVert,
  CheckCircle,
  Cancel,
  Flag,
  Visibility,
  Delete,
  Warning,
  ThumbUp,
  ThumbDown,
  Comment,
  VideoLibrary,
  Article,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

export default function ContentModeration() {
  const [currentTab, setCurrentTab] = useState(0);
  const [content, setContent] = useState([]);
  const [filteredContent, setFilteredContent] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [selectedContent, setSelectedContent] = useState(null);
  const [actionMenuAnchor, setActionMenuAnchor] = useState(null);
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false);
  const { hasPermission } = useAuth();

  const tabs = [
    { label: 'Machapisho', value: 'posts', icon: <ThumbUp /> },
    { label: 'Maoni', value: 'comments', icon: <Comment /> },
    { label: 'Video Fupi', value: 'videos', icon: <VideoLibrary /> },
    { label: 'Habari', value: 'news', icon: <Article /> },
  ];

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockContent = [
      {
        id: 1,
        type: 'posts',
        title: 'Habari za leo',
        content: 'Hii ni chapisho la kawaida la mtumiaji...',
        author: 'John Doe',
        authorAvatar: null,
        status: 'PENDING',
        reportCount: 2,
        aiScore: 85,
        createdAt: '2024-01-20T10:30:00',
        reportReasons: ['Spam', 'Inappropriate'],
        mediaUrls: [],
      },
      {
        id: 2,
        type: 'videos',
        title: 'Video ya muziki',
        content: 'Video fupi ya muziki wa Bongo Flava',
        author: 'Mary Johnson',
        authorAvatar: null,
        status: 'FLAGGED',
        reportCount: 5,
        aiScore: 45,
        createdAt: '2024-01-20T09:15:00',
        reportReasons: ['Copyright', 'Inappropriate'],
        mediaUrls: ['video1.mp4'],
      },
      {
        id: 3,
        type: 'news',
        title: 'Habari za uchumi',
        content: 'Makala kuhusu hali ya uchumi wa Tanzania...',
        author: 'Peter Smith',
        authorAvatar: null,
        status: 'APPROVED',
        reportCount: 0,
        aiScore: 95,
        createdAt: '2024-01-20T08:00:00',
        reportReasons: [],
        mediaUrls: [],
      },
    ];
    setContent(mockContent);
    setFilteredContent(mockContent.filter(item => item.type === 'posts'));
  }, []);

  useEffect(() => {
    const currentTabValue = tabs[currentTab].value;
    const filtered = content
      .filter(item => item.type === currentTabValue)
      .filter(item =>
        item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.author.toLowerCase().includes(searchQuery.toLowerCase())
      );
    setFilteredContent(filtered);
  }, [currentTab, searchQuery, content]);

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
    setPage(0);
  };

  const handleActionClick = (event, contentItem) => {
    setSelectedContent(contentItem);
    setActionMenuAnchor(event.currentTarget);
  };

  const handleActionClose = () => {
    setActionMenuAnchor(null);
    setSelectedContent(null);
  };

  const handleContentAction = (action) => {
    if (!selectedContent) return;
    
    switch (action) {
      case 'approve':
        console.log('Approve content:', selectedContent.id);
        break;
      case 'reject':
        console.log('Reject content:', selectedContent.id);
        break;
      case 'flag':
        console.log('Flag content:', selectedContent.id);
        break;
      case 'delete':
        console.log('Delete content:', selectedContent.id);
        break;
      case 'preview':
        setPreviewDialogOpen(true);
        break;
      default:
        break;
    }
    handleActionClose();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'APPROVED': return 'success';
      case 'REJECTED': return 'error';
      case 'FLAGGED': return 'warning';
      case 'PENDING': return 'info';
      default: return 'default';
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'APPROVED': return 'Imeidhinishwa';
      case 'REJECTED': return 'Imekataliwa';
      case 'FLAGGED': return 'Imebandikwa';
      case 'PENDING': return 'Inasubiri';
      default: return status;
    }
  };

  const getAIScoreColor = (score) => {
    if (score >= 80) return 'success';
    if (score >= 60) return 'warning';
    return 'error';
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Udhibiti wa Maudhui
        </Typography>
        <Button variant="contained" startIcon={<Flag />}>
          Ripoti za Maudhui
        </Button>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Inasubiri Ukaguzi
              </Typography>
              <Typography variant="h4" color="info.main">
                {content.filter(c => c.status === 'PENDING').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Imebandikwa
              </Typography>
              <Typography variant="h4" color="warning.main">
                {content.filter(c => c.status === 'FLAGGED').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Imeidhinishwa Leo
              </Typography>
              <Typography variant="h4" color="success.main">
                {content.filter(c => c.status === 'APPROVED').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Imekataliwa Leo
              </Typography>
              <Typography variant="h4" color="error.main">
                {content.filter(c => c.status === 'REJECTED').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Content Type Tabs */}
      <Card sx={{ mb: 3 }}>
        <Tabs
          value={currentTab}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
        >
          {tabs.map((tab, index) => (
            <Tab
              key={index}
              label={tab.label}
              icon={tab.icon}
              iconPosition="start"
            />
          ))}
        </Tabs>
      </Card>

      {/* Search and Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Tafuta maudhui..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Button
                variant="outlined"
                startIcon={<FilterList />}
              >
                Chuja kwa Hali
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Content Table */}
      <Card>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Maudhui</TableCell>
                <TableCell>Mwandishi</TableCell>
                <TableCell>Hali</TableCell>
                <TableCell>AI Score</TableCell>
                <TableCell>Ripoti</TableCell>
                <TableCell>Tarehe</TableCell>
                <TableCell>Vitendo</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredContent
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <Box>
                        <Typography variant="subtitle2" noWrap sx={{ maxWidth: 200 }}>
                          {item.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" noWrap sx={{ maxWidth: 200 }}>
                          {item.content}
                        </Typography>
                        {item.mediaUrls.length > 0 && (
                          <Chip label={`${item.mediaUrls.length} media`} size="small" sx={{ mt: 1 }} />
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar src={item.authorAvatar} sx={{ mr: 1, width: 32, height: 32 }}>
                          {item.author[0]}
                        </Avatar>
                        <Typography variant="body2">
                          {item.author}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getStatusLabel(item.status)}
                        color={getStatusColor(item.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={`${item.aiScore}%`}
                        color={getAIScoreColor(item.aiScore)}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      {item.reportCount > 0 ? (
                        <Chip
                          label={item.reportCount}
                          color="error"
                          size="small"
                          icon={<Flag />}
                        />
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          Hakuna
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(item.createdAt).toLocaleDateString('sw-TZ')}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <IconButton
                        onClick={(e) => handleActionClick(e, item)}
                        disabled={!hasPermission('MODERATE_POSTS')}
                      >
                        <MoreVert />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={filteredContent.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={(e, newPage) => setPage(newPage)}
          onRowsPerPageChange={(e) => {
            setRowsPerPage(parseInt(e.target.value, 10));
            setPage(0);
          }}
        />
      </Card>

      {/* Action Menu */}
      <Menu
        anchorEl={actionMenuAnchor}
        open={Boolean(actionMenuAnchor)}
        onClose={handleActionClose}
      >
        <MenuItem onClick={() => handleContentAction('preview')}>
          <Visibility sx={{ mr: 1 }} /> Angalia
        </MenuItem>
        <MenuItem onClick={() => handleContentAction('approve')}>
          <CheckCircle sx={{ mr: 1 }} /> Idhinisha
        </MenuItem>
        <MenuItem onClick={() => handleContentAction('reject')}>
          <Cancel sx={{ mr: 1 }} /> Kataa
        </MenuItem>
        <MenuItem onClick={() => handleContentAction('flag')}>
          <Flag sx={{ mr: 1 }} /> Bandika
        </MenuItem>
        <MenuItem onClick={() => handleContentAction('delete')}>
          <Delete sx={{ mr: 1 }} /> Futa
        </MenuItem>
      </Menu>

      {/* Content Preview Dialog */}
      <Dialog 
        open={previewDialogOpen} 
        onClose={() => setPreviewDialogOpen(false)} 
        maxWidth="md" 
        fullWidth
      >
        <DialogTitle>Angalia Maudhui</DialogTitle>
        <DialogContent>
          {selectedContent && (
            <Box>
              <Typography variant="h6" gutterBottom>
                {selectedContent.title}
              </Typography>
              <Typography variant="body1" paragraph>
                {selectedContent.content}
              </Typography>
              
              {selectedContent.reportCount > 0 && (
                <Alert severity="warning" sx={{ mb: 2 }}>
                  <Typography variant="subtitle2">
                    Sababu za Ripoti:
                  </Typography>
                  {selectedContent.reportReasons.map((reason, index) => (
                    <Chip key={index} label={reason} size="small" sx={{ mr: 1, mt: 1 }} />
                  ))}
                </Alert>
              )}
              
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  AI Analysis Score: {selectedContent.aiScore}%
                </Typography>
                <Chip
                  label={`${selectedContent.aiScore >= 80 ? 'Safe' : selectedContent.aiScore >= 60 ? 'Review' : 'Risky'}`}
                  color={getAIScoreColor(selectedContent.aiScore)}
                  size="small"
                />
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewDialogOpen(false)}>
            Funga
          </Button>
          <Button 
            variant="contained" 
            color="success"
            onClick={() => handleContentAction('approve')}
          >
            Idhinisha
          </Button>
          <Button 
            variant="contained" 
            color="error"
            onClick={() => handleContentAction('reject')}
          >
            Kataa
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
