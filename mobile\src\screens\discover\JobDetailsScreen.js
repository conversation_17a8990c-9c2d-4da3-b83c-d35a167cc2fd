import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Share,
  Linking,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors, typography, spacing } from '../../theme/theme';
import { jobsAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

export default function JobDetailsScreen({ navigation, route }) {
  const { jobId } = route.params;
  const { user } = useAuth();
  const [job, setJob] = useState(null);
  const [loading, setLoading] = useState(true);
  const [hasApplied, setHasApplied] = useState(false);

  useEffect(() => {
    loadJobDetails();
  }, [jobId]);

  const loadJobDetails = async () => {
    try {
      const response = await jobsAPI.getJobById(jobId);
      if (response.success) {
        setJob(response.data);
        setHasApplied(response.data.hasUserApplied || false);
      }
    } catch (error) {
      console.error('Error loading job:', error);
      Alert.alert('Hitilafu', 'Imeshindwa kupakia taarifa za kazi');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('sw-TZ', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getJobTypeColor = (type) => {
    switch (type) {
      case 'FULL_TIME':
        return colors.success;
      case 'PART_TIME':
        return colors.warning;
      case 'CONTRACT':
        return colors.info;
      case 'INTERNSHIP':
        return colors.secondary;
      default:
        return colors.gray;
    }
  };

  const getJobTypeName = (type) => {
    switch (type) {
      case 'FULL_TIME':
        return 'Kazi ya Kudumu';
      case 'PART_TIME':
        return 'Kazi ya Muda';
      case 'CONTRACT':
        return 'Mkataba';
      case 'INTERNSHIP':
        return 'Mafunzo';
      default:
        return type;
    }
  };

  const handleApplyJob = () => {
    if (!user) {
      Alert.alert('Ingia Kwanza', 'Unahitaji kuingia ili kuomba kazi');
      return;
    }

    if (hasApplied) {
      Alert.alert('Tayari Umeomba', 'Umeshakwisha omba kazi hii');
      return;
    }

    navigation.navigate('ApplyJob', { 
      jobId: job.id,
      jobTitle: job.title,
      companyName: job.companyName 
    });
  };

  const handleShare = async () => {
    try {
      await Share.share({
        message: `Angalia fursa hii ya kazi: ${job.title} kwa ${job.companyName}\n\n${job.description}\n\nMshahara: ${job.salaryMin ? formatCurrency(job.salaryMin) + ' - ' + formatCurrency(job.salaryMax) : 'Kujadiliwa'}\n\nPakua ProChat app kuona zaidi!`,
        url: job.shareUrl || '',
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  const handleContactEmployer = () => {
    if (job.contactEmail) {
      Linking.openURL(`mailto:${job.contactEmail}`);
    } else if (job.contactPhone) {
      Linking.openURL(`tel:${job.contactPhone}`);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Inapakia...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!job) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text>Kazi haipatikani</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Maelezo ya Kazi</Text>
        <TouchableOpacity style={styles.shareButton} onPress={handleShare}>
          <Icon name="share" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView}>
        {/* Job Header */}
        <View style={styles.jobHeader}>
          <View style={styles.companyLogo}>
            <Icon name="business" size={32} color={colors.primary} />
          </View>
          
          <View style={styles.jobTitleSection}>
            <Text style={styles.jobTitle}>{job.title}</Text>
            <Text style={styles.companyName}>{job.companyName}</Text>
            <Text style={styles.location}>
              <Icon name="location-on" size={16} color={colors.textSecondary} />
              {' '}{job.location}
            </Text>
          </View>

          <View style={styles.jobBadges}>
            <View style={[
              styles.jobTypeBadge,
              { backgroundColor: getJobTypeColor(job.jobType) + '20' }
            ]}>
              <Text style={[
                styles.jobTypeText,
                { color: getJobTypeColor(job.jobType) }
              ]}>
                {getJobTypeName(job.jobType)}
              </Text>
            </View>
            
            {job.isUrgent && (
              <View style={styles.urgentBadge}>
                <Text style={styles.urgentText}>Haraka</Text>
              </View>
            )}
          </View>
        </View>

        {/* Job Info */}
        <View style={styles.jobInfo}>
          {/* Salary */}
          <View style={styles.infoRow}>
            <Icon name="attach-money" size={20} color={colors.primary} />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Mshahara</Text>
              <Text style={styles.infoValue}>
                {job.salaryMin && job.salaryMax 
                  ? `${formatCurrency(job.salaryMin)} - ${formatCurrency(job.salaryMax)}`
                  : job.salaryMin 
                    ? `Kuanzia ${formatCurrency(job.salaryMin)}`
                    : 'Kujadiliwa'
                }
              </Text>
            </View>
          </View>

          {/* Experience */}
          <View style={styles.infoRow}>
            <Icon name="work" size={20} color={colors.primary} />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Uzoefu</Text>
              <Text style={styles.infoValue}>
                {job.experienceRequired || 'Hakuna uzoefu unahitajika'}
              </Text>
            </View>
          </View>

          {/* Education */}
          <View style={styles.infoRow}>
            <Icon name="school" size={20} color={colors.primary} />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Elimu</Text>
              <Text style={styles.infoValue}>
                {job.educationRequired || 'Hakuna mahitaji maalum'}
              </Text>
            </View>
          </View>

          {/* Deadline */}
          <View style={styles.infoRow}>
            <Icon name="schedule" size={20} color={colors.primary} />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Mwisho wa Maombi</Text>
              <Text style={styles.infoValue}>
                {formatDate(job.applicationDeadline)}
              </Text>
            </View>
          </View>

          {/* Applications */}
          <View style={styles.infoRow}>
            <Icon name="people" size={20} color={colors.primary} />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>Maombi</Text>
              <Text style={styles.infoValue}>
                {job.applicationsCount} wameomba
              </Text>
            </View>
          </View>
        </View>

        {/* Job Description */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Maelezo ya Kazi</Text>
          <Text style={styles.description}>{job.description}</Text>
        </View>

        {/* Requirements */}
        {job.requirements && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Mahitaji</Text>
            <Text style={styles.requirements}>{job.requirements}</Text>
          </View>
        )}

        {/* Responsibilities */}
        {job.responsibilities && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Majukumu</Text>
            <Text style={styles.responsibilities}>{job.responsibilities}</Text>
          </View>
        )}

        {/* Benefits */}
        {job.benefits && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Faida</Text>
            <Text style={styles.benefits}>{job.benefits}</Text>
          </View>
        )}

        {/* Company Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Kuhusu Kampuni</Text>
          <Text style={styles.companyInfo}>
            {job.companyDescription || 'Hakuna maelezo ya kampuni'}
          </Text>
          
          {(job.contactEmail || job.contactPhone) && (
            <TouchableOpacity 
              style={styles.contactButton}
              onPress={handleContactEmployer}
            >
              <Icon name="contact-mail" size={16} color={colors.primary} />
              <Text style={styles.contactButtonText}>Wasiliana na Mwajiri</Text>
            </TouchableOpacity>
          )}
        </View>
      </ScrollView>

      {/* Bottom Action Button */}
      <View style={styles.bottomActions}>
        <TouchableOpacity 
          style={[
            styles.applyButton,
            hasApplied && styles.appliedButton
          ]} 
          onPress={handleApplyJob}
          disabled={hasApplied}
        >
          <Icon 
            name={hasApplied ? "check-circle" : "send"} 
            size={20} 
            color={colors.white} 
          />
          <Text style={styles.applyButtonText}>
            {hasApplied ? 'Umeomba Tayari' : 'Omba Kazi'}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
  },
  shareButton: {
    padding: spacing.sm,
  },
  scrollView: {
    flex: 1,
  },
  jobHeader: {
    padding: spacing.lg,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  companyLogo: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  jobTitleSection: {
    marginBottom: spacing.md,
  },
  jobTitle: {
    fontSize: typography.fontSize.xxl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  companyName: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.medium,
    color: colors.primary,
    marginBottom: spacing.xs,
  },
  location: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
  },
  jobBadges: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  jobTypeBadge: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
  },
  jobTypeText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  urgentBadge: {
    backgroundColor: colors.error,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
  },
  urgentText: {
    color: colors.white,
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  jobInfo: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.lg,
  },
  infoContent: {
    flex: 1,
    marginLeft: spacing.md,
  },
  infoLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  infoValue: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
  },
  section: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginBottom: spacing.sm,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  description: {
    fontSize: typography.fontSize.md,
    color: colors.text,
    lineHeight: 24,
  },
  requirements: {
    fontSize: typography.fontSize.md,
    color: colors.text,
    lineHeight: 24,
  },
  responsibilities: {
    fontSize: typography.fontSize.md,
    color: colors.text,
    lineHeight: 24,
  },
  benefits: {
    fontSize: typography.fontSize.md,
    color: colors.text,
    lineHeight: 24,
  },
  companyInfo: {
    fontSize: typography.fontSize.md,
    color: colors.text,
    lineHeight: 24,
    marginBottom: spacing.md,
  },
  contactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  contactButtonText: {
    fontSize: typography.fontSize.md,
    color: colors.primary,
    marginLeft: spacing.sm,
    fontWeight: typography.fontWeight.medium,
  },
  bottomActions: {
    padding: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    backgroundColor: colors.white,
  },
  applyButton: {
    backgroundColor: colors.primary,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.lg,
    borderRadius: 12,
  },
  appliedButton: {
    backgroundColor: colors.success,
  },
  applyButtonText: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.white,
    marginLeft: spacing.sm,
  },
});
