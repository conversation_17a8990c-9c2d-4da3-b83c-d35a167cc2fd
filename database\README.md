# ProChat Database Setup

Hii ni mwongozo wa kuanzisha database ya ProChat.

## Mahitaji

- MySQL 8.0 au zaidi
- Java 17 au zaidi (kwa Spring Boot backend)
- Node.js 16 au zaidi (kwa React Native na React web admin)

## Kuanzisha Database

### 1. Tengeneza Database

```sql
-- Fungua MySQL command line au MySQL Workbench
mysql -u root -p

-- Tengeneza database
CREATE DATABASE prochat_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Tumia database
USE prochat_db;
```

### 2. Te<PERSON><PERSON><PERSON> Schema

```bash
# Kutoka kwenye directory ya mradi
cd database
mysql -u root -p prochat_db < init_database.sql
```

Au unaweza ku-copy na ku-paste maudhui ya `init_database.sql` kwenye MySQL client yako.

### 3. Thibitisha Database

```sql
-- <PERSON><PERSON> tables zilizotengezwa
SHOW TABLES;

-- <PERSON><PERSON> muundo wa table ya users
DESCRIBE users;

-- Angalia data ya roles
SELECT * FROM roles;
```

## Muundo wa Database

### Tables Kuu

1. **users** - Watumiaji wa mfumo
2. **roles** - Majukumu ya watumiaji
3. **user_roles** - Uhusiano wa watumiaji na majukumu
4. **wallets** - Pochi za ProPay
5. **transactions** - Miamala ya kifedha
6. **chats** - Mazungumzo
7. **chat_participants** - Washiriki wa mazungumzo
8. **messages** - Ujumbe
9. **posts** - Machapisho ya mitandao ya kijamii

### Uhusiano wa Tables

```
users (1) ←→ (1) wallets
users (1) ←→ (n) posts
users (n) ←→ (n) chats (kupitia chat_participants)
chats (1) ←→ (n) messages
wallets (1) ←→ (n) transactions (kama sender au receiver)
```

## Configuration ya Backend

Hakikisha `application.yml` ina configuration sahihi:

```yaml
spring:
  datasource:
    url: ***************************************************************************************************
    username: root
    password: Ram$0101
    driver-class-name: com.mysql.cj.jdbc.Driver
```

## Test Data (Optional)

Unaweza kuongeza test data kwa majaribio:

```sql
-- Test user
INSERT INTO users (username, email, phone_number, password, first_name, last_name, created_at, updated_at) 
VALUES ('testuser', '<EMAIL>', '+255123456789', '$2a$10$encrypted_password', 'Test', 'User', NOW(), NOW());

-- Test wallet
INSERT INTO wallets (user_id, wallet_number, balance, created_at, updated_at) 
VALUES (1, 'PC123456', 100000.00, NOW(), NOW());
```

## Backup na Restore

### Backup

```bash
mysqldump -u root -p prochat_db > prochat_backup_$(date +%Y%m%d).sql
```

### Restore

```bash
mysql -u root -p prochat_db < prochat_backup_20240120.sql
```

## Troubleshooting

### Kosa la Connection

1. Hakikisha MySQL service inaendesha
2. Thibitisha username na password
3. Angalia firewall settings

### Kosa la Character Set

```sql
ALTER DATABASE prochat_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### Kosa la Timezone

Ongeza kwenye MySQL configuration:

```ini
[mysqld]
default-time-zone='+03:00'
```

## Performance Optimization

### Indexes

Database schema tayari ina indexes muhimu, lakini unaweza kuongeza zaidi:

```sql
-- Index kwa search
CREATE INDEX idx_users_search ON users(username, first_name, last_name);
CREATE INDEX idx_posts_content ON posts(content(100));

-- Index kwa performance
CREATE INDEX idx_transactions_date ON transactions(created_at);
CREATE INDEX idx_messages_chat_date ON messages(chat_id, created_at);
```

### Query Optimization

- Tumia LIMIT kwa queries kubwa
- Avoid SELECT * 
- Tumia prepared statements
- Monitor slow queries

## Security

### User Permissions

```sql
-- Tengeneza user maalum kwa application
CREATE USER 'prochat_app'@'localhost' IDENTIFIED BY 'secure_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON prochat_db.* TO 'prochat_app'@'localhost';
FLUSH PRIVILEGES;
```

### Data Encryption

- Passwords zina-hash kwa bcrypt
- PINs za wallet zina-encrypt
- Sensitive data inahitaji encryption at rest

## Monitoring

### Useful Queries

```sql
-- Idadi ya watumiaji
SELECT COUNT(*) as total_users FROM users;

-- Miamala ya leo
SELECT COUNT(*) as today_transactions 
FROM transactions 
WHERE DATE(created_at) = CURDATE();

-- Watumiaji waliopo online
SELECT COUNT(*) as online_users 
FROM users 
WHERE is_online = true;

-- Jumla ya pesa kwenye mfumo
SELECT SUM(balance) as total_money 
FROM wallets;
```

## Maintenance

### Daily Tasks

1. Backup database
2. Check error logs
3. Monitor disk space
4. Review slow queries

### Weekly Tasks

1. Optimize tables
2. Update statistics
3. Review security logs
4. Check data integrity

### Monthly Tasks

1. Archive old data
2. Review indexes
3. Update documentation
4. Performance review
