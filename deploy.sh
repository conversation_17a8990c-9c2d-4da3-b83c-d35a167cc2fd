#!/bin/bash

# ProChat Deployment Script
# This script automates the deployment of ProChat platform

set -e

echo "🚀 Starting ProChat Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Docker and Docker Compose are installed"
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p logs
    mkdir -p uploads
    mkdir -p nginx/ssl
    mkdir -p backups
    
    print_success "Directories created"
}

# Set up environment variables
setup_environment() {
    print_status "Setting up environment variables..."
    
    if [ ! -f .env ]; then
        print_warning ".env file not found. Creating from template..."
        cat > .env << EOF
# Database Configuration
MYSQL_ROOT_PASSWORD=Ram\$0101
MYSQL_DATABASE=prochat_db
MYSQL_USER=prochat_user
MYSQL_PASSWORD=Ram\$0101

# AWS Configuration
AWS_ACCESS_KEY=your_aws_access_key
AWS_SECRET_KEY=your_aws_secret_key

# Twilio Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_FROM_NUMBER=your_twilio_phone_number

# Firebase Configuration
FIREBASE_SERVER_KEY=your_firebase_server_key

# Email Configuration
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=Ram\$0101

# Environment
NODE_ENV=production
SPRING_PROFILES_ACTIVE=production
EOF
        print_warning "Please update .env file with your actual credentials"
    else
        print_success "Environment file exists"
    fi
}

# Generate SSL certificates (self-signed for development)
generate_ssl_certificates() {
    print_status "Generating SSL certificates..."
    
    if [ ! -f nginx/ssl/prochat.crt ]; then
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout nginx/ssl/prochat.key \
            -out nginx/ssl/prochat.crt \
            -subj "/C=TZ/ST=Dar es Salaam/L=Dar es Salaam/O=ProChat/CN=prochat.co.tz"
        
        cp nginx/ssl/prochat.crt nginx/ssl/admin.crt
        cp nginx/ssl/prochat.key nginx/ssl/admin.key
        cp nginx/ssl/prochat.crt nginx/ssl/api.crt
        cp nginx/ssl/prochat.key nginx/ssl/api.key
        
        print_success "SSL certificates generated"
    else
        print_success "SSL certificates already exist"
    fi
}

# Build Docker images
build_images() {
    print_status "Building Docker images..."
    
    # Build backend
    print_status "Building backend image..."
    docker build -t prochat-backend ./backend
    
    # Build admin panel
    print_status "Building admin panel image..."
    docker build -t prochat-admin ./admin-panel
    
    # Build public website
    print_status "Building public website image..."
    docker build -t prochat-website ./public-website
    
    print_success "All images built successfully"
}

# Start services
start_services() {
    print_status "Starting ProChat services..."
    
    # Start database first
    docker-compose up -d mysql redis
    
    print_status "Waiting for database to be ready..."
    sleep 30
    
    # Start backend
    docker-compose up -d backend
    
    print_status "Waiting for backend to be ready..."
    sleep 20
    
    # Start frontend services
    docker-compose up -d admin-panel public-website nginx
    
    print_success "All services started"
}

# Check service health
check_health() {
    print_status "Checking service health..."
    
    # Check MySQL
    if docker-compose exec mysql mysqladmin ping -h localhost -u root -pRam\$0101 &> /dev/null; then
        print_success "MySQL is healthy"
    else
        print_error "MySQL is not responding"
    fi
    
    # Check Backend
    if curl -f http://localhost:8080/api/health &> /dev/null; then
        print_success "Backend is healthy"
    else
        print_warning "Backend health check failed"
    fi
    
    # Check Admin Panel
    if curl -f http://localhost:3001 &> /dev/null; then
        print_success "Admin Panel is accessible"
    else
        print_warning "Admin Panel is not accessible"
    fi
    
    # Check Public Website
    if curl -f http://localhost:3002 &> /dev/null; then
        print_success "Public Website is accessible"
    else
        print_warning "Public Website is not accessible"
    fi
}

# Show service URLs
show_urls() {
    print_success "🎉 ProChat deployment completed!"
    echo ""
    echo "📱 Service URLs:"
    echo "   🌐 Public Website: http://localhost:3002"
    echo "   🖥️  Admin Panel:    http://localhost:3001"
    echo "   ⚙️  Backend API:    http://localhost:8080"
    echo "   🗄️  Database:       localhost:3306"
    echo ""
    echo "📋 Default Admin Credentials:"
    echo "   Username: superadmin"
    echo "   Password: ProChat2024!"
    echo ""
    echo "🔧 Management Commands:"
    echo "   View logs:     docker-compose logs -f [service]"
    echo "   Stop services: docker-compose down"
    echo "   Restart:       docker-compose restart [service]"
    echo ""
    print_warning "Remember to update .env file with your actual credentials!"
}

# Backup function
backup_data() {
    print_status "Creating backup..."
    
    BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Backup database
    docker-compose exec mysql mysqldump -u root -pRam\$0101 prochat_db > "$BACKUP_DIR/database.sql"
    
    # Backup uploads
    cp -r uploads "$BACKUP_DIR/" 2>/dev/null || true
    
    print_success "Backup created in $BACKUP_DIR"
}

# Main deployment function
deploy() {
    print_status "🚀 Starting ProChat deployment process..."
    
    check_docker
    create_directories
    setup_environment
    generate_ssl_certificates
    build_images
    start_services
    
    print_status "Waiting for services to stabilize..."
    sleep 10
    
    check_health
    show_urls
}

# Handle command line arguments
case "${1:-deploy}" in
    "deploy")
        deploy
        ;;
    "backup")
        backup_data
        ;;
    "stop")
        print_status "Stopping ProChat services..."
        docker-compose down
        print_success "Services stopped"
        ;;
    "restart")
        print_status "Restarting ProChat services..."
        docker-compose restart
        print_success "Services restarted"
        ;;
    "logs")
        docker-compose logs -f "${2:-}"
        ;;
    "health")
        check_health
        ;;
    *)
        echo "Usage: $0 {deploy|backup|stop|restart|logs|health}"
        echo ""
        echo "Commands:"
        echo "  deploy  - Full deployment (default)"
        echo "  backup  - Create backup"
        echo "  stop    - Stop all services"
        echo "  restart - Restart all services"
        echo "  logs    - View logs (optionally specify service)"
        echo "  health  - Check service health"
        exit 1
        ;;
esac
