import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  Alert,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import * as ImagePicker from 'expo-image-picker';
import { colors, typography, spacing } from '../../theme/theme';
import { userAPI, uploadAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

export default function ProfileScreen({ navigation }) {
  const { user, updateUser } = useAuth();
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState({
    postsCount: 0,
    followersCount: 0,
    followingCount: 0,
  });

  useEffect(() => {
    loadProfile();
    loadStats();
  }, []);

  const loadProfile = async () => {
    try {
      const response = await userAPI.getProfile();
      if (response.success) {
        setProfile(response.data);
      }
    } catch (error) {
      console.error('Error loading profile:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const loadStats = async () => {
    try {
      const response = await userAPI.getUserStats();
      if (response.success) {
        setStats(response.data);
      }
    } catch (error) {
      console.error('Error loading stats:', error);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadProfile();
    loadStats();
  };

  const handleEditProfile = () => {
    navigation.navigate('EditProfile');
  };

  const handleChangeProfilePicture = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert('Ruhusa', 'Tunahitaji ruhusa ya kufikia picha zako');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled) {
        const uploadResponse = await uploadAPI.uploadImage(result.assets[0]);
        
        if (uploadResponse.success) {
          const updateResponse = await updateUser({
            profilePicture: uploadResponse.data.url
          });
          
          if (updateResponse.success) {
            setProfile(prev => ({
              ...prev,
              profilePicture: uploadResponse.data.url
            }));
            Alert.alert('Mafanikio', 'Picha ya wasifu imebadilishwa');
          }
        }
      }
    } catch (error) {
      console.error('Error changing profile picture:', error);
      Alert.alert('Hitilafu', 'Imeshindwa kubadilisha picha');
    }
  };

  const handleViewPosts = () => {
    navigation.navigate('UserPosts', { userId: user.id });
  };

  const handleViewFollowers = () => {
    navigation.navigate('Followers', { userId: user.id });
  };

  const handleViewFollowing = () => {
    navigation.navigate('Following', { userId: user.id });
  };

  const getVerificationBadge = () => {
    if (profile?.verificationLevel === 'VERIFIED') {
      return (
        <View style={styles.verificationBadge}>
          <Icon name="verified" size={20} color={colors.primary} />
        </View>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Inapakia wasifu...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Wasifu Wangu</Text>
        <TouchableOpacity style={styles.settingsButton} onPress={handleEditProfile}>
          <Icon name="edit" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Profile Header */}
        <View style={styles.profileHeader}>
          <TouchableOpacity
            style={styles.profileImageContainer}
            onPress={handleChangeProfilePicture}
          >
            <Image
              source={{ 
                uri: profile?.profilePicture || user?.profilePicture || 'https://via.placeholder.com/120' 
              }}
              style={styles.profileImage}
            />
            <View style={styles.cameraOverlay}>
              <Icon name="camera-alt" size={20} color={colors.white} />
            </View>
          </TouchableOpacity>

          <View style={styles.profileInfo}>
            <View style={styles.nameContainer}>
              <Text style={styles.profileName}>
                {profile?.firstName || user?.firstName} {profile?.lastName || user?.lastName}
              </Text>
              {getVerificationBadge()}
            </View>
            
            <Text style={styles.username}>@{profile?.username || user?.username}</Text>
            
            {profile?.bio && (
              <Text style={styles.bio}>{profile.bio}</Text>
            )}

            {profile?.location && (
              <View style={styles.locationContainer}>
                <Icon name="location-on" size={16} color={colors.textSecondary} />
                <Text style={styles.location}>{profile.location}</Text>
              </View>
            )}

            {profile?.website && (
              <View style={styles.websiteContainer}>
                <Icon name="link" size={16} color={colors.textSecondary} />
                <Text style={styles.website}>{profile.website}</Text>
              </View>
            )}

            <View style={styles.joinDateContainer}>
              <Icon name="calendar-today" size={16} color={colors.textSecondary} />
              <Text style={styles.joinDate}>
                Alijiunga {new Date(profile?.createdAt || user?.createdAt).toLocaleDateString('sw-TZ')}
              </Text>
            </View>
          </View>
        </View>

        {/* Stats */}
        <View style={styles.statsContainer}>
          <TouchableOpacity style={styles.statItem} onPress={handleViewPosts}>
            <Text style={styles.statNumber}>{stats.postsCount}</Text>
            <Text style={styles.statLabel}>Machapisho</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.statItem} onPress={handleViewFollowers}>
            <Text style={styles.statNumber}>{stats.followersCount}</Text>
            <Text style={styles.statLabel}>Wafuataji</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.statItem} onPress={handleViewFollowing}>
            <Text style={styles.statNumber}>{stats.followingCount}</Text>
            <Text style={styles.statLabel}>Ninafuata</Text>
          </TouchableOpacity>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.editButton} onPress={handleEditProfile}>
            <Icon name="edit" size={20} color={colors.primary} />
            <Text style={styles.editButtonText}>Hariri Wasifu</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.shareButton}>
            <Icon name="share" size={20} color={colors.textSecondary} />
            <Text style={styles.shareButtonText}>Shiriki Wasifu</Text>
          </TouchableOpacity>
        </View>

        {/* Profile Sections */}
        <View style={styles.sectionsContainer}>
          <TouchableOpacity style={styles.sectionItem} onPress={handleViewPosts}>
            <Icon name="grid-on" size={24} color={colors.primary} />
            <Text style={styles.sectionText}>Machapisho Yangu</Text>
            <Icon name="chevron-right" size={24} color={colors.gray} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.sectionItem}>
            <Icon name="bookmark" size={24} color={colors.primary} />
            <Text style={styles.sectionText}>Vilivyohifadhiwa</Text>
            <Icon name="chevron-right" size={24} color={colors.gray} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.sectionItem}>
            <Icon name="favorite" size={24} color={colors.primary} />
            <Text style={styles.sectionText}>Vipendwa</Text>
            <Icon name="chevron-right" size={24} color={colors.gray} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.sectionItem}>
            <Icon name="history" size={24} color={colors.primary} />
            <Text style={styles.sectionText}>Shughuli za Hivi Karibuni</Text>
            <Icon name="chevron-right" size={24} color={colors.gray} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.sectionItem}>
            <Icon name="analytics" size={24} color={colors.primary} />
            <Text style={styles.sectionText}>Takwimu za Wasifu</Text>
            <Icon name="chevron-right" size={24} color={colors.gray} />
          </TouchableOpacity>
        </View>

        {/* Account Info */}
        <View style={styles.accountInfo}>
          <Text style={styles.accountInfoTitle}>Taarifa za Akaunti</Text>
          
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Barua Pepe:</Text>
            <Text style={styles.infoValue}>{profile?.email || user?.email}</Text>
            {profile?.emailVerified && (
              <Icon name="verified" size={16} color={colors.success} />
            )}
          </View>

          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Simu:</Text>
            <Text style={styles.infoValue}>{profile?.phoneNumber || user?.phoneNumber}</Text>
            {profile?.phoneVerified && (
              <Icon name="verified" size={16} color={colors.success} />
            )}
          </View>

          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Kiwango cha Uthibitisho:</Text>
            <Text style={[
              styles.infoValue,
              { color: profile?.verificationLevel === 'VERIFIED' ? colors.success : colors.warning }
            ]}>
              {profile?.verificationLevel === 'VERIFIED' ? 'Imethibitishwa' : 'Haijathibitishwa'}
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
  },
  settingsButton: {
    padding: spacing.sm,
  },
  content: {
    flex: 1,
  },
  profileHeader: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    alignItems: 'center',
  },
  profileImageContainer: {
    position: 'relative',
    marginBottom: spacing.md,
  },
  profileImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 3,
    borderColor: colors.primary,
  },
  cameraOverlay: {
    position: 'absolute',
    bottom: 5,
    right: 5,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileInfo: {
    alignItems: 'center',
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  profileName: {
    fontSize: typography.fontSize.xxl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
  },
  verificationBadge: {
    marginLeft: spacing.sm,
  },
  username: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
    marginBottom: spacing.sm,
  },
  bio: {
    fontSize: typography.fontSize.md,
    color: colors.text,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: spacing.sm,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  location: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginLeft: spacing.xs,
  },
  websiteContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  website: {
    fontSize: typography.fontSize.sm,
    color: colors.primary,
    marginLeft: spacing.xs,
  },
  joinDateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  joinDate: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginLeft: spacing.xs,
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    paddingVertical: spacing.lg,
    marginTop: spacing.sm,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
  },
  statLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    gap: spacing.md,
  },
  editButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.primary + '20',
    paddingVertical: spacing.md,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  editButtonText: {
    color: colors.primary,
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
    marginLeft: spacing.sm,
  },
  shareButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.surface,
    paddingVertical: spacing.md,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
  },
  shareButtonText: {
    color: colors.textSecondary,
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
    marginLeft: spacing.sm,
  },
  sectionsContainer: {
    backgroundColor: colors.white,
    marginTop: spacing.sm,
    paddingHorizontal: spacing.lg,
  },
  sectionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  sectionText: {
    flex: 1,
    fontSize: typography.fontSize.md,
    color: colors.text,
    marginLeft: spacing.md,
  },
  accountInfo: {
    backgroundColor: colors.white,
    marginTop: spacing.sm,
    padding: spacing.lg,
  },
  accountInfoTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  infoLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    width: 100,
  },
  infoValue: {
    flex: 1,
    fontSize: typography.fontSize.sm,
    color: colors.text,
    marginLeft: spacing.sm,
  },
});
