import AsyncStorage from '@react-native-async-storage/async-storage';

// Format currency (Tanzanian Shilling)
export const formatCurrency = (amount, currency = 'TSH') => {
  if (typeof amount !== 'number') {
    amount = parseFloat(amount) || 0;
  }
  
  return new Intl.NumberFormat('sw-TZ', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(amount);
};

// Format large numbers (e.g., 1.2K, 1.5M)
export const formatNumber = (num) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

// Format time ago
export const timeAgo = (date) => {
  const now = new Date();
  const diffInSeconds = Math.floor((now - new Date(date)) / 1000);
  
  if (diffInSeconds < 60) {
    return 'Sasa hivi';
  }
  
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes} dakika zilizopita`;
  }
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours} saa zilizopita`;
  }
  
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) {
    return `${diffInDays} siku zilizopita`;
  }
  
  const diffInWeeks = Math.floor(diffInDays / 7);
  if (diffInWeeks < 4) {
    return `${diffInWeeks} wiki zilizopita`;
  }
  
  const diffInMonths = Math.floor(diffInDays / 30);
  return `${diffInMonths} mwezi zilizopita`;
};

// Format phone number for Tanzania
export const formatPhoneNumber = (phone) => {
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');
  
  // Handle different formats
  if (cleaned.startsWith('255')) {
    // +255 format
    return `+${cleaned}`;
  } else if (cleaned.startsWith('0')) {
    // 0 format, convert to +255
    return `+255${cleaned.substring(1)}`;
  } else if (cleaned.length === 9) {
    // 9 digits, add +255
    return `+255${cleaned}`;
  }
  
  return phone; // Return original if can't format
};

// Validate email
export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Validate Tanzanian phone number
export const validatePhoneNumber = (phone) => {
  const phoneRegex = /^(\+255|0)[67]\d{8}$/;
  return phoneRegex.test(phone);
};

// Generate unique ID
export const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// Truncate text
export const truncateText = (text, maxLength = 100) => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

// Storage helpers
export const storage = {
  set: async (key, value) => {
    try {
      const jsonValue = JSON.stringify(value);
      await AsyncStorage.setItem(key, jsonValue);
    } catch (error) {
      console.error('Storage set error:', error);
    }
  },

  get: async (key) => {
    try {
      const jsonValue = await AsyncStorage.getItem(key);
      return jsonValue != null ? JSON.parse(jsonValue) : null;
    } catch (error) {
      console.error('Storage get error:', error);
      return null;
    }
  },

  remove: async (key) => {
    try {
      await AsyncStorage.removeItem(key);
    } catch (error) {
      console.error('Storage remove error:', error);
    }
  },

  clear: async () => {
    try {
      await AsyncStorage.clear();
    } catch (error) {
      console.error('Storage clear error:', error);
    }
  },
};

// Color helpers
export const colors = {
  primary: '#007AFF',
  secondary: '#5856D6',
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#FF3B30',
  info: '#2196F3',
  light: '#F8F9FA',
  dark: '#333333',
  gray: '#666666',
  lightGray: '#999999',
  border: '#E0E0E0',
  background: '#FFFFFF',
  text: '#333333',
  textSecondary: '#666666',
  textLight: '#999999',
};

// Device info helpers
export const deviceInfo = {
  isIOS: Platform.OS === 'ios',
  isAndroid: Platform.OS === 'android',
  screenWidth: Dimensions.get('window').width,
  screenHeight: Dimensions.get('window').height,
};

// Debounce function
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Deep clone object
export const deepClone = (obj) => {
  return JSON.parse(JSON.stringify(obj));
};

// Check if object is empty
export const isEmpty = (obj) => {
  return Object.keys(obj).length === 0;
};

// Capitalize first letter
export const capitalize = (str) => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

// Generate random color
export const getRandomColor = () => {
  const colors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
  ];
  return colors[Math.floor(Math.random() * colors.length)];
};

// Format file size
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Get initials from name
export const getInitials = (name) => {
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .join('')
    .substring(0, 2);
};

// Check if string contains only numbers
export const isNumeric = (str) => {
  return /^\d+$/.test(str);
};

// Generate wallet number
export const generateWalletNumber = () => {
  const prefix = 'PC';
  const randomNumber = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
  return `${prefix}${randomNumber}`;
};

// Format transaction reference
export const formatTransactionRef = (type, id) => {
  const typeCode = {
    'SEND_MONEY': 'SM',
    'RECEIVE_MONEY': 'RM',
    'DEPOSIT': 'DP',
    'WITHDRAWAL': 'WD',
    'BILL_PAYMENT': 'BP',
    'GOVERNMENT_PAYMENT': 'GP',
  };
  
  const code = typeCode[type] || 'TX';
  const timestamp = Date.now().toString().slice(-6);
  return `${code}${timestamp}${id}`;
};
