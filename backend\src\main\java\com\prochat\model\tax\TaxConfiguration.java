package com.prochat.model.tax;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "tax_configurations")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TaxConfiguration {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "tax_type", nullable = false, unique = true)
    private TaxType taxType;
    
    @Column(name = "tax_name", nullable = false)
    private String taxName;
    
    @Column(name = "tax_rate", nullable = false, precision = 5, scale = 4)
    private BigDecimal taxRate; // 0.18 for 18%
    
    @Column(name = "tax_percentage", nullable = false, precision = 5, scale = 2)
    private BigDecimal taxPercentage; // 18.00 for display
    
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;
    
    @Column(name = "applies_to_transactions", nullable = false)
    private Boolean appliesToTransactions = true;
    
    @Column(name = "applies_to_withdrawals", nullable = false)
    private Boolean appliesToWithdrawals = true;
    
    @Column(name = "applies_to_donations", nullable = false)
    private Boolean appliesToDonations = true;
    
    @Column(name = "applies_to_merchant_payments", nullable = false)
    private Boolean appliesToMerchantPayments = true;
    
    @Column(name = "applies_to_gifts", nullable = false)
    private Boolean appliesToGifts = false;
    
    @Column(name = "applies_to_tickets", nullable = false)
    private Boolean appliesToTickets = true;
    
    @Column(name = "minimum_amount", precision = 10, scale = 2)
    private BigDecimal minimumAmount; // Minimum amount to apply tax
    
    @Column(name = "maximum_amount", precision = 10, scale = 2)
    private BigDecimal maximumAmount; // Maximum amount for tax calculation
    
    @Column(name = "tax_authority", nullable = false)
    private String taxAuthority = "Tanzania Revenue Authority (TRA)";
    
    @Column(name = "tax_id", nullable = false)
    private String taxId; // ProChat's Tax ID/TIN
    
    @Column(name = "description")
    private String description;
    
    @Column(name = "effective_from", nullable = false)
    private LocalDateTime effectiveFrom;
    
    @Column(name = "effective_to")
    private LocalDateTime effectiveTo;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @Column(name = "created_by")
    private Long createdBy;
    
    @Column(name = "updated_by")
    private Long updatedBy;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        if (effectiveFrom == null) {
            effectiveFrom = LocalDateTime.now();
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    public enum TaxType {
        VAT("Value Added Tax", "Kodi ya Ongezeko la Thamani"),
        WITHHOLDING_TAX("Withholding Tax", "Kodi ya Kuzuia"),
        SERVICE_TAX("Service Tax", "Kodi ya Huduma"),
        EXCISE_TAX("Excise Tax", "Kodi ya Ushuru"),
        STAMP_DUTY("Stamp Duty", "Ada ya Muhuri");
        
        private final String displayName;
        private final String swahiliName;
        
        TaxType(String displayName, String swahiliName) {
            this.displayName = displayName;
            this.swahiliName = swahiliName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public String getSwahiliName() {
            return swahiliName;
        }
    }
    
    // Helper method to calculate tax amount
    public BigDecimal calculateTaxAmount(BigDecimal amount) {
        if (!isActive || amount == null) {
            return BigDecimal.ZERO;
        }
        
        if (minimumAmount != null && amount.compareTo(minimumAmount) < 0) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal taxableAmount = amount;
        if (maximumAmount != null && amount.compareTo(maximumAmount) > 0) {
            taxableAmount = maximumAmount;
        }
        
        return taxableAmount.multiply(taxRate).setScale(2, BigDecimal.ROUND_HALF_UP);
    }
    
    // Helper method to calculate net amount after tax
    public BigDecimal calculateNetAmount(BigDecimal grossAmount) {
        BigDecimal taxAmount = calculateTaxAmount(grossAmount);
        return grossAmount.subtract(taxAmount);
    }
}
