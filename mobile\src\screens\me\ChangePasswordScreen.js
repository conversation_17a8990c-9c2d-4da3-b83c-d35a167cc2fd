import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors, typography, spacing } from '../../theme/theme';
import { authAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

export default function ChangePasswordScreen({ navigation }) {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });
  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const togglePasswordVisibility = (field) => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const validateForm = () => {
    if (!formData.currentPassword.trim()) {
      Alert.alert('Hitilafu', 'Andika nywila yako ya sasa');
      return false;
    }
    
    if (!formData.newPassword.trim()) {
      Alert.alert('Hitilafu', 'Andika nywila mpya');
      return false;
    }
    
    if (formData.newPassword.length < 8) {
      Alert.alert('Hitilafu', 'Nywila mpya lazima iwe na angalau herufi 8');
      return false;
    }
    
    if (formData.newPassword !== formData.confirmPassword) {
      Alert.alert('Hitilafu', 'Nywila mpya na uthibitisho havifanani');
      return false;
    }
    
    if (formData.currentPassword === formData.newPassword) {
      Alert.alert('Hitilafu', 'Nywila mpya lazima iwe tofauti na ya zamani');
      return false;
    }
    
    return true;
  };

  const handleChangePassword = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);
      const response = await authAPI.changePassword({
        currentPassword: formData.currentPassword,
        newPassword: formData.newPassword,
      });
      
      if (response.success) {
        Alert.alert(
          'Mafanikio!',
          'Nywila yako imebadilishwa kwa mafanikio',
          [
            {
              text: 'Sawa',
              onPress: () => navigation.goBack()
            }
          ]
        );
      } else {
        Alert.alert('Hitilafu', response.message || 'Imeshindwa kubadilisha nywila');
      }
    } catch (error) {
      console.error('Change password error:', error);
      Alert.alert('Hitilafu', 'Imeshindwa kubadilisha nywila');
    } finally {
      setLoading(false);
    }
  };

  const getPasswordStrength = (password) => {
    if (!password) return { strength: 0, text: '', color: colors.gray };
    
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    
    if (strength <= 2) return { strength, text: 'Dhaifu', color: colors.error };
    if (strength <= 3) return { strength, text: 'Wastani', color: colors.warning };
    if (strength <= 4) return { strength, text: 'Imara', color: colors.success };
    return { strength, text: 'Imara Sana', color: colors.success };
  };

  const passwordStrength = getPasswordStrength(formData.newPassword);

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Badilisha Nywila</Text>
        <View style={styles.placeholder} />
      </View>

      <View style={styles.content}>
        {/* Security Info */}
        <View style={styles.securityInfo}>
          <Icon name="security" size={48} color={colors.primary} />
          <Text style={styles.securityTitle}>Badilisha Nywila Yako</Text>
          <Text style={styles.securityDescription}>
            Hakikisha nywila yako mpya ni imara na salama. Tumia mchanganyiko wa herufi kubwa na ndogo, nambari na alama maalum.
          </Text>
        </View>

        {/* Form */}
        <View style={styles.form}>
          {/* Current Password */}
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Nywila ya Sasa *</Text>
            <View style={styles.passwordContainer}>
              <TextInput
                style={styles.passwordInput}
                placeholder="Andika nywila yako ya sasa"
                placeholderTextColor={colors.textSecondary}
                value={formData.currentPassword}
                onChangeText={(text) => handleInputChange('currentPassword', text)}
                secureTextEntry={!showPasswords.current}
                autoCapitalize="none"
              />
              <TouchableOpacity
                style={styles.eyeButton}
                onPress={() => togglePasswordVisibility('current')}
              >
                <Icon 
                  name={showPasswords.current ? "visibility-off" : "visibility"} 
                  size={20} 
                  color={colors.textSecondary} 
                />
              </TouchableOpacity>
            </View>
          </View>

          {/* New Password */}
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Nywila Mpya *</Text>
            <View style={styles.passwordContainer}>
              <TextInput
                style={styles.passwordInput}
                placeholder="Andika nywila mpya"
                placeholderTextColor={colors.textSecondary}
                value={formData.newPassword}
                onChangeText={(text) => handleInputChange('newPassword', text)}
                secureTextEntry={!showPasswords.new}
                autoCapitalize="none"
              />
              <TouchableOpacity
                style={styles.eyeButton}
                onPress={() => togglePasswordVisibility('new')}
              >
                <Icon 
                  name={showPasswords.new ? "visibility-off" : "visibility"} 
                  size={20} 
                  color={colors.textSecondary} 
                />
              </TouchableOpacity>
            </View>
            
            {/* Password Strength */}
            {formData.newPassword.length > 0 && (
              <View style={styles.strengthContainer}>
                <View style={styles.strengthBar}>
                  <View 
                    style={[
                      styles.strengthFill,
                      { 
                        width: `${(passwordStrength.strength / 5) * 100}%`,
                        backgroundColor: passwordStrength.color
                      }
                    ]} 
                  />
                </View>
                <Text style={[styles.strengthText, { color: passwordStrength.color }]}>
                  {passwordStrength.text}
                </Text>
              </View>
            )}
          </View>

          {/* Confirm Password */}
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Thibitisha Nywila Mpya *</Text>
            <View style={styles.passwordContainer}>
              <TextInput
                style={styles.passwordInput}
                placeholder="Andika nywila mpya tena"
                placeholderTextColor={colors.textSecondary}
                value={formData.confirmPassword}
                onChangeText={(text) => handleInputChange('confirmPassword', text)}
                secureTextEntry={!showPasswords.confirm}
                autoCapitalize="none"
              />
              <TouchableOpacity
                style={styles.eyeButton}
                onPress={() => togglePasswordVisibility('confirm')}
              >
                <Icon 
                  name={showPasswords.confirm ? "visibility-off" : "visibility"} 
                  size={20} 
                  color={colors.textSecondary} 
                />
              </TouchableOpacity>
            </View>
            
            {/* Password Match Indicator */}
            {formData.confirmPassword.length > 0 && (
              <View style={styles.matchIndicator}>
                <Icon 
                  name={formData.newPassword === formData.confirmPassword ? "check-circle" : "cancel"} 
                  size={16} 
                  color={formData.newPassword === formData.confirmPassword ? colors.success : colors.error} 
                />
                <Text style={[
                  styles.matchText,
                  { color: formData.newPassword === formData.confirmPassword ? colors.success : colors.error }
                ]}>
                  {formData.newPassword === formData.confirmPassword ? 'Nywila zinafanana' : 'Nywila hazifanani'}
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* Password Requirements */}
        <View style={styles.requirementsSection}>
          <Text style={styles.requirementsTitle}>Mahitaji ya Nywila:</Text>
          
          <View style={styles.requirement}>
            <Icon 
              name={formData.newPassword.length >= 8 ? "check-circle" : "radio-button-unchecked"} 
              size={16} 
              color={formData.newPassword.length >= 8 ? colors.success : colors.gray} 
            />
            <Text style={styles.requirementText}>Angalau herufi 8</Text>
          </View>
          
          <View style={styles.requirement}>
            <Icon 
              name={/[a-z]/.test(formData.newPassword) ? "check-circle" : "radio-button-unchecked"} 
              size={16} 
              color={/[a-z]/.test(formData.newPassword) ? colors.success : colors.gray} 
            />
            <Text style={styles.requirementText}>Herufi ndogo (a-z)</Text>
          </View>
          
          <View style={styles.requirement}>
            <Icon 
              name={/[A-Z]/.test(formData.newPassword) ? "check-circle" : "radio-button-unchecked"} 
              size={16} 
              color={/[A-Z]/.test(formData.newPassword) ? colors.success : colors.gray} 
            />
            <Text style={styles.requirementText}>Herufi kubwa (A-Z)</Text>
          </View>
          
          <View style={styles.requirement}>
            <Icon 
              name={/[0-9]/.test(formData.newPassword) ? "check-circle" : "radio-button-unchecked"} 
              size={16} 
              color={/[0-9]/.test(formData.newPassword) ? colors.success : colors.gray} 
            />
            <Text style={styles.requirementText}>Nambari (0-9)</Text>
          </View>
          
          <View style={styles.requirement}>
            <Icon 
              name={/[^A-Za-z0-9]/.test(formData.newPassword) ? "check-circle" : "radio-button-unchecked"} 
              size={16} 
              color={/[^A-Za-z0-9]/.test(formData.newPassword) ? colors.success : colors.gray} 
            />
            <Text style={styles.requirementText}>Alama maalum (!@#$%)</Text>
          </View>
        </View>

        {/* Change Password Button */}
        <TouchableOpacity
          style={[
            styles.changeButton,
            loading && styles.changeButtonDisabled
          ]}
          onPress={handleChangePassword}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color={colors.white} />
          ) : (
            <>
              <Icon name="lock" size={20} color={colors.white} />
              <Text style={styles.changeButtonText}>Badilisha Nywila</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  securityInfo: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  securityTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  securityDescription: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  form: {
    marginBottom: spacing.xl,
  },
  inputGroup: {
    marginBottom: spacing.lg,
  },
  inputLabel: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    backgroundColor: colors.surface,
  },
  passwordInput: {
    flex: 1,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    fontSize: typography.fontSize.md,
    color: colors.text,
  },
  eyeButton: {
    padding: spacing.md,
  },
  strengthContainer: {
    marginTop: spacing.sm,
  },
  strengthBar: {
    height: 4,
    backgroundColor: colors.border,
    borderRadius: 2,
    overflow: 'hidden',
    marginBottom: spacing.xs,
  },
  strengthFill: {
    height: '100%',
    borderRadius: 2,
  },
  strengthText: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  matchIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.sm,
  },
  matchText: {
    fontSize: typography.fontSize.sm,
    marginLeft: spacing.xs,
  },
  requirementsSection: {
    backgroundColor: colors.surface,
    padding: spacing.md,
    borderRadius: 8,
    marginBottom: spacing.xl,
  },
  requirementsTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  requirement: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  requirementText: {
    fontSize: typography.fontSize.sm,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  changeButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingVertical: spacing.lg,
    borderRadius: 8,
  },
  changeButtonDisabled: {
    backgroundColor: colors.gray,
  },
  changeButtonText: {
    color: colors.white,
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
    marginLeft: spacing.sm,
  },
});
