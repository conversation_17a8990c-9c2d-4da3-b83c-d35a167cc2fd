package com.prochat.controller;

import com.prochat.model.News;
import com.prochat.model.NewsBookmark;
import com.prochat.service.NewsService;
import com.prochat.security.CurrentUser;
import com.prochat.security.UserPrincipal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

@RestController
@RequestMapping("/api/news")
@PreAuthorize("hasRole('USER')")
public class NewsController {

    @Autowired
    private NewsService newsService;

    @GetMapping
    public ResponseEntity<?> getNews(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String source,
            @RequestParam(required = false) String keywords) {
        try {
            List<News> news = newsService.getNews(page, size, category, source, keywords);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", news);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata habari: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/{newsId}")
    public ResponseEntity<?> getNewsById(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable Long newsId) {
        try {
            News news = newsService.getNewsById(newsId, currentUser.getId());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", news);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata habari: " + e.getMessage()
            ));
        }
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('JOURNALIST')")
    public ResponseEntity<?> createNews(
            @CurrentUser UserPrincipal currentUser,
            @Valid @RequestBody CreateNewsRequest request) {
        try {
            News news = newsService.createNews(currentUser.getId(), request);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Habari imeundwa kwa mafanikio");
            response.put("data", news);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kuunda habari: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/{newsId}/bookmark")
    public ResponseEntity<?> toggleBookmark(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable Long newsId) {
        try {
            boolean isBookmarked = newsService.toggleBookmark(currentUser.getId(), newsId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", isBookmarked ? "Habari imehifadhiwa" : "Habari imeondolewa kwenye zilizohifadhiwa");
            response.put("data", Map.of("isBookmarked", isBookmarked));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kuhifadhi habari: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/bookmarks")
    public ResponseEntity<?> getBookmarkedNews(
            @CurrentUser UserPrincipal currentUser,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            List<News> news = newsService.getBookmarkedNews(currentUser.getId(), page, size);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", news);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata habari zilizohifadhiwa: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/categories")
    public ResponseEntity<?> getNewsCategories() {
        try {
            List<String> categories = newsService.getNewsCategories();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", categories);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata makundi: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/trending")
    public ResponseEntity<?> getTrendingNews(
            @RequestParam(defaultValue = "10") int limit) {
        try {
            List<News> news = newsService.getTrendingNews(limit);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", news);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata habari maarufu: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/breaking")
    public ResponseEntity<?> getBreakingNews(
            @RequestParam(defaultValue = "5") int limit) {
        try {
            List<News> news = newsService.getBreakingNews(limit);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", news);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata habari za haraka: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/recommended")
    public ResponseEntity<?> getRecommendedNews(
            @CurrentUser UserPrincipal currentUser,
            @RequestParam(defaultValue = "10") int limit) {
        try {
            List<News> news = newsService.getRecommendedNews(currentUser.getId(), limit);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", news);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata habari zilizopendekezwa: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/{newsId}/view")
    public ResponseEntity<?> recordView(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable Long newsId) {
        try {
            newsService.recordView(newsId, currentUser.getId());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Mwonekano umerekodiwa");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kurekodi mwonekano: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/{newsId}/share")
    public ResponseEntity<?> shareNews(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable Long newsId) {
        try {
            String shareUrl = newsService.generateShareUrl(newsId);
            newsService.recordShare(newsId, currentUser.getId());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", Map.of(
                "shareUrl", shareUrl,
                "message", "Shiriki habari hii na marafiki zako!"
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kushiriki: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/search")
    public ResponseEntity<?> searchNews(
            @RequestParam String query,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            List<News> news = newsService.searchNews(query, page, size);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", news);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kutafuta: " + e.getMessage()
            ));
        }
    }

    // Admin endpoints
    @PutMapping("/{newsId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateNews(
            @PathVariable Long newsId,
            @Valid @RequestBody UpdateNewsRequest request) {
        try {
            News news = newsService.updateNews(newsId, request);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Habari imesasishwa");
            response.put("data", news);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kusasisha: " + e.getMessage()
            ));
        }
    }

    @DeleteMapping("/{newsId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteNews(@PathVariable Long newsId) {
        try {
            newsService.deleteNews(newsId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Habari imefutwa");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kufuta: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/{newsId}/feature")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> toggleFeature(@PathVariable Long newsId) {
        try {
            boolean isFeatured = newsService.toggleFeature(newsId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", isFeatured ? "Habari imewekwa kama kuu" : "Habari imeondolewa kama kuu");
            response.put("data", Map.of("isFeatured", isFeatured));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kubadilisha hali: " + e.getMessage()
            ));
        }
    }

    // Request DTOs
    public static class CreateNewsRequest {
        private String title;
        private String content;
        private String summary;
        private String category;
        private String source;
        private String imageUrl;
        private String[] tags;
        private Boolean isBreaking;
        private Boolean isFeatured;
        
        // Getters and setters
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        public String getSummary() { return summary; }
        public void setSummary(String summary) { this.summary = summary; }
        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }
        public String getSource() { return source; }
        public void setSource(String source) { this.source = source; }
        public String getImageUrl() { return imageUrl; }
        public void setImageUrl(String imageUrl) { this.imageUrl = imageUrl; }
        public String[] getTags() { return tags; }
        public void setTags(String[] tags) { this.tags = tags; }
        public Boolean getIsBreaking() { return isBreaking; }
        public void setIsBreaking(Boolean isBreaking) { this.isBreaking = isBreaking; }
        public Boolean getIsFeatured() { return isFeatured; }
        public void setIsFeatured(Boolean isFeatured) { this.isFeatured = isFeatured; }
    }

    public static class UpdateNewsRequest {
        private String title;
        private String content;
        private String summary;
        private String category;
        private String imageUrl;
        private String[] tags;
        private Boolean isBreaking;
        private Boolean isFeatured;
        
        // Getters and setters
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        public String getSummary() { return summary; }
        public void setSummary(String summary) { this.summary = summary; }
        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }
        public String getImageUrl() { return imageUrl; }
        public void setImageUrl(String imageUrl) { this.imageUrl = imageUrl; }
        public String[] getTags() { return tags; }
        public void setTags(String[] tags) { this.tags = tags; }
        public Boolean getIsBreaking() { return isBreaking; }
        public void setIsBreaking(Boolean isBreaking) { this.isBreaking = isBreaking; }
        public Boolean getIsFeatured() { return isFeatured; }
        public void setIsFeatured(Boolean isFeatured) { this.isFeatured = isFeatured; }
    }
}
