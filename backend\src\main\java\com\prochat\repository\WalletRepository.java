package com.prochat.repository;

import com.prochat.model.User;
import com.prochat.model.Wallet;
import com.prochat.model.WalletStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface WalletRepository extends JpaRepository<Wallet, Long> {
    
    Optional<Wallet> findByUser(User user);
    
    Optional<Wallet> findByWalletNumber(String walletNumber);
    
    Boolean existsByWalletNumber(String walletNumber);
    
    List<Wallet> findByStatus(WalletStatus status);
    
    @Query("SELECT w FROM Wallet w WHERE w.user.id = :userId")
    Optional<Wallet> findByUserId(@Param("userId") Long userId);
    
    @Query("SELECT w FROM Wallet w WHERE w.user.phoneNumber = :phoneNumber")
    Optional<Wallet> findByUserPhoneNumber(@Param("phoneNumber") String phoneNumber);
}
