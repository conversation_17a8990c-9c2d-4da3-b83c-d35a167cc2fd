{"ast": null, "code": "\"use strict\";\n'use client';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _styledEngine = require(\"@mui/styled-engine\");\nfunction _getRequireWildcardCache(e) {\n  if (\"function\" != typeof WeakMap) return null;\n  var r = new WeakMap(),\n    t = new WeakMap();\n  return (_getRequireWildcardCache = function (e) {\n    return e ? t : r;\n  })(e);\n}\nfunction _interopRequireWildcard(e, r) {\n  if (!r && e && e.__esModule) return e;\n  if (null === e || \"object\" != typeof e && \"function\" != typeof e) return {\n    default: e\n  };\n  var t = _getRequireWildcardCache(r);\n  if (t && t.has(e)) return t.get(e);\n  var n = {\n      __proto__: null\n    },\n    a = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var u in e) if (\"default\" !== u && Object.prototype.hasOwnProperty.call(e, u)) {\n    var i = a ? Object.getOwnPropertyDescriptor(e, u) : null;\n    i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u];\n  }\n  return n.default = e, t && t.set(e, n), n;\n}\nfunction isObjectEmpty(obj) {\n  return Object.keys(obj).length === 0;\n}\nfunction useTheme(defaultTheme = null) {\n  const contextTheme = React.useContext(_styledEngine.ThemeContext);\n  return !contextTheme || isObjectEmpty(contextTheme) ? defaultTheme : contextTheme;\n}\nvar _default = exports.default = useTheme;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "React", "_interopRequireWildcard", "require", "_styledE<PERSON>ine", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "has", "get", "n", "__proto__", "a", "getOwnPropertyDescriptor", "u", "prototype", "hasOwnProperty", "call", "i", "set", "isObjectEmpty", "obj", "keys", "length", "useTheme", "defaultTheme", "contextTheme", "useContext", "ThemeContext", "_default"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/@mui/system/useThemeWithoutDefault.js"], "sourcesContent": ["\"use strict\";\n'use client';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _styledEngine = require(\"@mui/styled-engine\");\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nfunction isObjectEmpty(obj) {\n  return Object.keys(obj).length === 0;\n}\nfunction useTheme(defaultTheme = null) {\n  const contextTheme = React.useContext(_styledEngine.ThemeContext);\n  return !contextTheme || isObjectEmpty(contextTheme) ? defaultTheme : contextTheme;\n}\nvar _default = exports.default = useTheme;"], "mappings": "AAAA,YAAY;AACZ,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIC,KAAK,GAAGC,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIC,aAAa,GAAGD,OAAO,CAAC,oBAAoB,CAAC;AACjD,SAASE,wBAAwBA,CAACC,CAAC,EAAE;EAAE,IAAI,UAAU,IAAI,OAAOC,OAAO,EAAE,OAAO,IAAI;EAAE,IAAIC,CAAC,GAAG,IAAID,OAAO,CAAC,CAAC;IAAEE,CAAC,GAAG,IAAIF,OAAO,CAAC,CAAC;EAAE,OAAO,CAACF,wBAAwB,GAAG,SAAAA,CAAUC,CAAC,EAAE;IAAE,OAAOA,CAAC,GAAGG,CAAC,GAAGD,CAAC;EAAE,CAAC,EAAEF,CAAC,CAAC;AAAE;AAC3M,SAASJ,uBAAuBA,CAACI,CAAC,EAAEE,CAAC,EAAE;EAAE,IAAI,CAACA,CAAC,IAAIF,CAAC,IAAIA,CAAC,CAACI,UAAU,EAAE,OAAOJ,CAAC;EAAE,IAAI,IAAI,KAAKA,CAAC,IAAI,QAAQ,IAAI,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOA,CAAC,EAAE,OAAO;IAAEN,OAAO,EAAEM;EAAE,CAAC;EAAE,IAAIG,CAAC,GAAGJ,wBAAwB,CAACG,CAAC,CAAC;EAAE,IAAIC,CAAC,IAAIA,CAAC,CAACE,GAAG,CAACL,CAAC,CAAC,EAAE,OAAOG,CAAC,CAACG,GAAG,CAACN,CAAC,CAAC;EAAE,IAAIO,CAAC,GAAG;MAAEC,SAAS,EAAE;IAAK,CAAC;IAAEC,CAAC,GAAGnB,MAAM,CAACC,cAAc,IAAID,MAAM,CAACoB,wBAAwB;EAAE,KAAK,IAAIC,CAAC,IAAIX,CAAC,EAAE,IAAI,SAAS,KAAKW,CAAC,IAAIrB,MAAM,CAACsB,SAAS,CAACC,cAAc,CAACC,IAAI,CAACd,CAAC,EAAEW,CAAC,CAAC,EAAE;IAAE,IAAII,CAAC,GAAGN,CAAC,GAAGnB,MAAM,CAACoB,wBAAwB,CAACV,CAAC,EAAEW,CAAC,CAAC,GAAG,IAAI;IAAEI,CAAC,KAAKA,CAAC,CAACT,GAAG,IAAIS,CAAC,CAACC,GAAG,CAAC,GAAG1B,MAAM,CAACC,cAAc,CAACgB,CAAC,EAAEI,CAAC,EAAEI,CAAC,CAAC,GAAGR,CAAC,CAACI,CAAC,CAAC,GAAGX,CAAC,CAACW,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC,CAACb,OAAO,GAAGM,CAAC,EAAEG,CAAC,IAAIA,CAAC,CAACa,GAAG,CAAChB,CAAC,EAAEO,CAAC,CAAC,EAAEA,CAAC;AAAE;AAChlB,SAASU,aAAaA,CAACC,GAAG,EAAE;EAC1B,OAAO5B,MAAM,CAAC6B,IAAI,CAACD,GAAG,CAAC,CAACE,MAAM,KAAK,CAAC;AACtC;AACA,SAASC,QAAQA,CAACC,YAAY,GAAG,IAAI,EAAE;EACrC,MAAMC,YAAY,GAAG5B,KAAK,CAAC6B,UAAU,CAAC1B,aAAa,CAAC2B,YAAY,CAAC;EACjE,OAAO,CAACF,YAAY,IAAIN,aAAa,CAACM,YAAY,CAAC,GAAGD,YAAY,GAAGC,YAAY;AACnF;AACA,IAAIG,QAAQ,GAAGlC,OAAO,CAACE,OAAO,GAAG2B,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}