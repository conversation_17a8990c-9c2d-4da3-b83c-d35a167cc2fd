import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  TextInput,
  Alert,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors, typography, spacing } from '../../theme/theme';
import { postsAPI, commentsAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

export default function PostDetailsScreen({ navigation, route }) {
  const { postId } = route.params;
  const { user } = useAuth();
  const [post, setPost] = useState(null);
  const [comments, setComments] = useState([]);
  const [newComment, setNewComment] = useState('');
  const [loading, setLoading] = useState(true);
  const [isLiked, setIsLiked] = useState(false);
  const [likesCount, setLikesCount] = useState(0);

  useEffect(() => {
    loadPostDetails();
    loadComments();
  }, [postId]);

  const loadPostDetails = async () => {
    try {
      const response = await postsAPI.getPostById(postId);
      if (response.success) {
        setPost(response.data);
        setIsLiked(response.data.isLiked);
        setLikesCount(response.data.likesCount);
      }
    } catch (error) {
      console.error('Error loading post:', error);
      Alert.alert('Hitilafu', 'Imeshindwa kupakia chapisho');
    } finally {
      setLoading(false);
    }
  };

  const loadComments = async () => {
    try {
      const response = await commentsAPI.getPostComments(postId);
      if (response.success) {
        setComments(response.data);
      }
    } catch (error) {
      console.error('Error loading comments:', error);
    }
  };

  const handleLike = async () => {
    try {
      const response = await postsAPI.toggleLike(postId);
      if (response.success) {
        setIsLiked(!isLiked);
        setLikesCount(prev => isLiked ? prev - 1 : prev + 1);
      }
    } catch (error) {
      console.error('Error liking post:', error);
    }
  };

  const handleComment = async () => {
    if (!newComment.trim()) return;

    try {
      const response = await commentsAPI.createComment({
        postId: postId,
        content: newComment.trim()
      });

      if (response.success) {
        setComments([response.data, ...comments]);
        setNewComment('');
      }
    } catch (error) {
      console.error('Error creating comment:', error);
      Alert.alert('Hitilafu', 'Imeshindwa kuongeza maoni');
    }
  };

  const handleShare = () => {
    // Implement share functionality
    Alert.alert('Shiriki', 'Utashiriki chapisho hili');
  };

  const handleGift = () => {
    navigation.navigate('Gift', {
      recipientId: post.author.id,
      recipientName: `${post.author.firstName} ${post.author.lastName}`,
      postId: post.id
    });
  };

  const formatTime = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'Dakika chache zilizopita';
    } else if (diffInHours < 24) {
      return `Masaa ${Math.floor(diffInHours)} yaliyopita`;
    } else {
      return date.toLocaleDateString('sw-TZ');
    }
  };

  const renderComment = ({ item }) => (
    <View style={styles.commentItem}>
      <Image
        source={{ uri: item.author.profilePicture || 'https://via.placeholder.com/40' }}
        style={styles.commentAvatar}
      />
      <View style={styles.commentContent}>
        <View style={styles.commentBubble}>
          <Text style={styles.commentAuthor}>
            {item.author.firstName} {item.author.lastName}
          </Text>
          <Text style={styles.commentText}>{item.content}</Text>
        </View>
        <Text style={styles.commentTime}>{formatTime(item.createdAt)}</Text>
      </View>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Inapakia...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!post) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text>Chapisho halipatikani</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Chapisho</Text>
        <TouchableOpacity style={styles.moreButton}>
          <Icon name="more-vert" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {/* Post Content */}
        <View style={styles.postContainer}>
          {/* Author Info */}
          <View style={styles.authorInfo}>
            <Image
              source={{ uri: post.author.profilePicture || 'https://via.placeholder.com/50' }}
              style={styles.authorAvatar}
            />
            <View style={styles.authorDetails}>
              <Text style={styles.authorName}>
                {post.author.firstName} {post.author.lastName}
              </Text>
              <Text style={styles.postTime}>{formatTime(post.createdAt)}</Text>
            </View>
          </View>

          {/* Post Text */}
          {post.content && (
            <Text style={styles.postContent}>{post.content}</Text>
          )}

          {/* Post Media */}
          {post.media && post.media.length > 0 && (
            <View style={styles.mediaContainer}>
              {post.media.map((media, index) => (
                <View key={index} style={styles.mediaItem}>
                  {media.type === 'image' ? (
                    <Image source={{ uri: media.url }} style={styles.postImage} />
                  ) : (
                    <View style={styles.videoContainer}>
                      <Image source={{ uri: media.thumbnail }} style={styles.postImage} />
                      <View style={styles.videoOverlay}>
                        <Icon name="play-circle-filled" size={48} color={colors.white} />
                      </View>
                    </View>
                  )}
                </View>
              ))}
            </View>
          )}

          {/* Post Stats */}
          <View style={styles.postStats}>
            <Text style={styles.statsText}>
              {likesCount} mapendekezo • {comments.length} maoni
            </Text>
          </View>

          {/* Post Actions */}
          <View style={styles.postActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={handleLike}
            >
              <Icon 
                name={isLiked ? "favorite" : "favorite-border"} 
                size={20} 
                color={isLiked ? colors.error : colors.textSecondary} 
              />
              <Text style={[
                styles.actionText,
                isLiked && { color: colors.error }
              ]}>
                Penda
              </Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton}>
              <Icon name="comment" size={20} color={colors.textSecondary} />
              <Text style={styles.actionText}>Maoni</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton} onPress={handleShare}>
              <Icon name="share" size={20} color={colors.textSecondary} />
              <Text style={styles.actionText}>Shiriki</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton} onPress={handleGift}>
              <Icon name="card-giftcard" size={20} color={colors.warning} />
              <Text style={[styles.actionText, { color: colors.warning }]}>
                Zawadi
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Comments Section */}
        <View style={styles.commentsSection}>
          <Text style={styles.commentsTitle}>
            Maoni ({comments.length})
          </Text>
          
          <FlatList
            data={comments}
            renderItem={renderComment}
            keyExtractor={(item) => item.id.toString()}
            scrollEnabled={false}
          />
        </View>
      </ScrollView>

      {/* Comment Input */}
      <View style={styles.commentInputContainer}>
        <Image
          source={{ uri: user?.profilePicture || 'https://via.placeholder.com/40' }}
          style={styles.userAvatar}
        />
        <View style={styles.commentInputWrapper}>
          <TextInput
            style={styles.commentInput}
            placeholder="Andika maoni..."
            placeholderTextColor={colors.textSecondary}
            value={newComment}
            onChangeText={setNewComment}
            multiline
          />
          <TouchableOpacity
            style={[
              styles.sendButton,
              !newComment.trim() && styles.sendButtonDisabled
            ]}
            onPress={handleComment}
            disabled={!newComment.trim()}
          >
            <Icon name="send" size={20} color={colors.white} />
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
  },
  moreButton: {
    padding: spacing.sm,
  },
  content: {
    flex: 1,
  },
  postContainer: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginBottom: spacing.sm,
  },
  authorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  authorAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: spacing.md,
  },
  authorDetails: {
    flex: 1,
  },
  authorName: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
  },
  postTime: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  postContent: {
    fontSize: typography.fontSize.md,
    color: colors.text,
    lineHeight: 22,
    marginBottom: spacing.md,
  },
  mediaContainer: {
    marginBottom: spacing.md,
  },
  mediaItem: {
    marginBottom: spacing.sm,
  },
  postImage: {
    width: '100%',
    height: 300,
    borderRadius: 8,
  },
  videoContainer: {
    position: 'relative',
  },
  videoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 8,
  },
  postStats: {
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    marginBottom: spacing.sm,
  },
  statsText: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
  postActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  actionText: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginLeft: spacing.xs,
  },
  commentsSection: {
    backgroundColor: colors.white,
    padding: spacing.lg,
  },
  commentsTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  commentItem: {
    flexDirection: 'row',
    marginBottom: spacing.md,
  },
  commentAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: spacing.sm,
  },
  commentContent: {
    flex: 1,
  },
  commentBubble: {
    backgroundColor: colors.surface,
    borderRadius: 16,
    padding: spacing.md,
    marginBottom: spacing.xs,
  },
  commentAuthor: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  commentText: {
    fontSize: typography.fontSize.sm,
    color: colors.text,
    lineHeight: 18,
  },
  commentTime: {
    fontSize: typography.fontSize.xs,
    color: colors.textSecondary,
    marginLeft: spacing.sm,
  },
  commentInputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    backgroundColor: colors.white,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: spacing.sm,
  },
  commentInputWrapper: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  commentInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 20,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: typography.fontSize.md,
    color: colors.text,
    maxHeight: 100,
    marginRight: spacing.sm,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: colors.gray,
  },
});
