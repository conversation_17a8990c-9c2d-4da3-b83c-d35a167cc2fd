package com.prochat.model.monetization;

import com.prochat.model.User;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "user_earnings")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserEarning {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "earning_type", nullable = false)
    private EarningRate.EarningType earningType;
    
    @Column(name = "amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal amount;
    
    @Column(name = "currency", nullable = false)
    private String currency = "TZS";
    
    @Column(name = "source_id")
    private Long sourceId; // ID ya post, comment, event, etc.
    
    @Column(name = "source_type")
    private String sourceType; // POST, COMMENT, EVENT, etc.
    
    @Column(name = "description")
    private String description;
    
    @Column(name = "quantity")
    private Integer quantity; // Kama ni likes, downloads, etc.
    
    @Column(name = "rate_used", precision = 10, scale = 4)
    private BigDecimal rateUsed; // Rate iliyotumika wakati huo
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private EarningStatus status = EarningStatus.PENDING;
    
    @Column(name = "processed_at")
    private LocalDateTime processedAt;
    
    @Column(name = "paid_at")
    private LocalDateTime paidAt;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    public enum EarningStatus {
        PENDING("Inasubiri"),
        PROCESSED("Imechakatwa"),
        PAID("Imelipwa"),
        CANCELLED("Imeghairiwa"),
        FAILED("Imeshindwa");
        
        private final String displayName;
        
        EarningStatus(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
}
