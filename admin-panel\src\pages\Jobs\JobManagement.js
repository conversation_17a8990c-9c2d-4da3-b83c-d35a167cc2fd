import React, { useState } from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Grid,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  InputAdornment,
  IconButton,
  Menu,
  MenuItem,
} from '@mui/material';
import {
  Search,
  Work,
  CheckCircle,
  Cancel,
  MoreVert,
  Visibility,
  Business,
} from '@mui/icons-material';

export default function JobManagement() {
  const [jobs] = useState([
    {
      id: 1,
      title: 'Software Developer',
      company: 'TechCorp Tanzania',
      location: 'Dar es Salaam',
      jobType: 'TECHNOLOGY',
      employmentType: 'FULL_TIME',
      status: 'PENDING',
      applications: 25,
      salaryMin: 800000,
      salaryMax: 1500000,
      postedDate: '2024-01-15',
    },
    {
      id: 2,
      title: 'Marketing Manager',
      company: 'Vodacom Tanzania',
      location: 'Dodoma',
      jobType: 'MARKETING',
      employmentType: 'FULL_TIME',
      status: 'APPROVED',
      applications: 45,
      salaryMin: 1200000,
      salaryMax: 2000000,
      postedDate: '2024-01-10',
    },
  ]);

  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchQuery, setSearchQuery] = useState('');
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedJob, setSelectedJob] = useState(null);

  const handleActionClick = (event, job) => {
    setSelectedJob(job);
    setAnchorEl(event.currentTarget);
  };

  const handleActionClose = () => {
    setAnchorEl(null);
    setSelectedJob(null);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'APPROVED': return 'success';
      case 'REJECTED': return 'error';
      case 'PENDING': return 'warning';
      default: return 'default';
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'APPROVED': return 'Imeidhinishwa';
      case 'REJECTED': return 'Imekataliwa';
      case 'PENDING': return 'Inasubiri';
      default: return status;
    }
  };

  const getJobTypeLabel = (type) => {
    const types = {
      TECHNOLOGY: 'Teknolojia',
      MARKETING: 'Uuzaji',
      FINANCE: 'Fedha',
      HEALTHCARE: 'Afya',
      EDUCATION: 'Elimu',
    };
    return types[type] || type;
  };

  const getEmploymentTypeLabel = (type) => {
    const types = {
      FULL_TIME: 'Wakati wote',
      PART_TIME: 'Wakati wa nusu',
      CONTRACT: 'Mkataba',
      INTERNSHIP: 'Mafunzo',
    };
    return types[type] || type;
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Uongozi wa Kazi na Zabuni
        </Typography>
        <Button variant="contained" startIcon={<Work />}>
          Ongeza Kazi
        </Button>
      </Box>

      {/* Stats */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Kazi za Jumla
              </Typography>
              <Typography variant="h4" color="primary">
                {jobs.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Zimeidhinishwa
              </Typography>
              <Typography variant="h4" color="success.main">
                {jobs.filter(j => j.status === 'APPROVED').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Zinasubiri
              </Typography>
              <Typography variant="h4" color="warning.main">
                {jobs.filter(j => j.status === 'PENDING').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Maombi ya Jumla
              </Typography>
              <Typography variant="h4" color="info.main">
                {jobs.reduce((sum, j) => sum + j.applications, 0)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <TextField
            fullWidth
            placeholder="Tafuta kazi..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
          />
        </CardContent>
      </Card>

      {/* Jobs Table */}
      <Card>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Jina la Kazi</TableCell>
                <TableCell>Kampuni</TableCell>
                <TableCell>Mahali</TableCell>
                <TableCell>Aina</TableCell>
                <TableCell>Mshahara</TableCell>
                <TableCell>Hali</TableCell>
                <TableCell>Maombi</TableCell>
                <TableCell>Vitendo</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {jobs
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((job) => (
                  <TableRow key={job.id}>
                    <TableCell>
                      <Typography variant="subtitle2">
                        {job.title}
                      </Typography>
                      <Chip
                        label={getEmploymentTypeLabel(job.employmentType)}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Business sx={{ mr: 1, fontSize: 16 }} />
                        {job.company}
                      </Box>
                    </TableCell>
                    <TableCell>{job.location}</TableCell>
                    <TableCell>
                      <Chip
                        label={getJobTypeLabel(job.jobType)}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {formatCurrency(job.salaryMin)} - {formatCurrency(job.salaryMax)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getStatusLabel(job.status)}
                        color={getStatusColor(job.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {job.applications}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <IconButton onClick={(e) => handleActionClick(e, job)}>
                        <MoreVert />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={jobs.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={(e, newPage) => setPage(newPage)}
          onRowsPerPageChange={(e) => {
            setRowsPerPage(parseInt(e.target.value, 10));
            setPage(0);
          }}
        />
      </Card>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleActionClose}
      >
        <MenuItem onClick={handleActionClose}>
          <Visibility sx={{ mr: 1 }} /> Angalia
        </MenuItem>
        <MenuItem onClick={handleActionClose}>
          <CheckCircle sx={{ mr: 1 }} /> Idhinisha
        </MenuItem>
        <MenuItem onClick={handleActionClose}>
          <Cancel sx={{ mr: 1 }} /> Kataa
        </MenuItem>
      </Menu>
    </Box>
  );
}
