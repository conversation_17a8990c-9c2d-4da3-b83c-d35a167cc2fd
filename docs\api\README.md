# 🔌 ProChat API Documentation

Welcome to the ProChat Platform API documentation. This comprehensive guide covers all available endpoints, authentication methods, and usage examples.

## 📋 Table of Contents

- [Overview](#overview)
- [Authentication](#authentication)
- [Base URL](#base-url)
- [Response Format](#response-format)
- [Error Handling](#error-handling)
- [Rate Limiting](#rate-limiting)
- [API Endpoints](#api-endpoints)

## 🌟 Overview

The ProChat API is a RESTful API built with Spring Boot that provides access to all platform features including user management, social media functionality, financial services, and more.

### Key Features
- **RESTful Design** - Standard HTTP methods and status codes
- **JWT Authentication** - Secure token-based authentication
- **Role-based Authorization** - Granular permission system
- **Real-time Support** - WebSocket integration
- **Comprehensive Coverage** - All platform features accessible

## 🔐 Authentication

### JWT Token Authentication

All API requests (except public endpoints) require authentication using JWT tokens.

#### Login Request
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Login Response
```json
{
  "success": true,
  "message": "Umeingia kwa mafanikio",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "type": "Bearer",
    "expiresIn": 86400,
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "USER"
    }
  }
}
```

#### Using the Token
Include the JWT token in the Authorization header:

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 🌐 Base URL

```
Development: http://localhost:8080/api
Production: https://api.prochat.co.tz/api
```

## 📄 Response Format

All API responses follow a consistent format:

### Success Response
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data here
  }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error description",
  "error": {
    "code": "ERROR_CODE",
    "details": "Detailed error information"
  }
}
```

## ❌ Error Handling

### HTTP Status Codes

| Code | Description |
|------|-------------|
| 200 | OK - Request successful |
| 201 | Created - Resource created |
| 400 | Bad Request - Invalid request |
| 401 | Unauthorized - Authentication required |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server error |

### Common Error Codes

| Error Code | Description |
|------------|-------------|
| `INVALID_CREDENTIALS` | Invalid email or password |
| `TOKEN_EXPIRED` | JWT token has expired |
| `INSUFFICIENT_PERMISSIONS` | User lacks required permissions |
| `RESOURCE_NOT_FOUND` | Requested resource not found |
| `VALIDATION_ERROR` | Request validation failed |
| `RATE_LIMIT_EXCEEDED` | Too many requests |

## 🚦 Rate Limiting

API requests are rate-limited to ensure fair usage:

- **Authenticated Users**: 1000 requests per hour
- **Anonymous Users**: 100 requests per hour
- **Admin Users**: 5000 requests per hour

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## 🔗 API Endpoints

### 👤 Authentication & Users
- [Authentication API](authentication.md) - Login, register, password reset
- [Users API](users.md) - User management and profiles

### 📱 Social Features
- [Posts API](posts.md) - Create, read, update, delete posts
- [Comments API](comments.md) - Post comments and replies
- [Likes API](likes.md) - Like/unlike posts and comments
- [Follows API](follows.md) - Follow/unfollow users

### 💬 Communication
- [Chats API](chats.md) - Direct messaging
- [Groups API](groups.md) - Group chats
- [Notifications API](notifications.md) - Push notifications

### 💰 Financial Services
- [Wallet API](wallet.md) - ProPay wallet operations
- [Transactions API](transactions.md) - Transaction history
- [Payments API](payments.md) - Payment processing

### 🎯 Business Features
- [ProZone API](prozone.md) - Agent and merchant services
- [Jobs API](jobs.md) - Job postings and applications
- [Events API](events.md) - Event management
- [Tickets API](tickets.md) - Event ticketing

### 📺 Media & Content
- [Live Streams API](live-streams.md) - Live streaming
- [Videos API](videos.md) - Video management
- [News API](news.md) - News and articles
- [Media API](media.md) - File uploads

### 🎁 Engagement
- [Tasks API](tasks.md) - Task and reward system
- [Donations API](donations.md) - Donation campaigns
- [Gifts API](gifts.md) - Virtual gifts

### ⚙️ System
- [Admin API](admin.md) - Administrative functions
- [Settings API](settings.md) - System settings
- [Analytics API](analytics.md) - Platform analytics

## 📊 Pagination

List endpoints support pagination using query parameters:

```http
GET /api/posts?page=0&size=20&sort=createdAt,desc
```

### Pagination Parameters
- `page` - Page number (0-based, default: 0)
- `size` - Items per page (default: 20, max: 100)
- `sort` - Sort field and direction (e.g., `createdAt,desc`)

### Pagination Response
```json
{
  "success": true,
  "data": {
    "content": [...],
    "page": {
      "number": 0,
      "size": 20,
      "totalElements": 150,
      "totalPages": 8,
      "first": true,
      "last": false
    }
  }
}
```

## 🔍 Filtering & Search

Many endpoints support filtering and search:

```http
GET /api/posts?category=TECHNOLOGY&keywords=react&startDate=2023-01-01
```

### Common Filter Parameters
- `category` - Filter by category
- `keywords` - Search in text fields
- `startDate` / `endDate` - Date range filtering
- `status` - Filter by status
- `userId` - Filter by user

## 🌐 Internationalization

The API supports multiple languages through the `Accept-Language` header:

```http
Accept-Language: sw-TZ  # Swahili (Tanzania)
Accept-Language: en-US  # English (US)
```

Supported languages:
- `sw-TZ` - Kiswahili (Tanzania)
- `en-US` - English (United States)

## 🧪 Testing

### Postman Collection
Import our Postman collection for easy API testing:
[Download ProChat API Collection](../assets/ProChat-API.postman_collection.json)

### Example Requests
See individual endpoint documentation for detailed examples and use cases.

## 📞 Support

Need help with the API?
- **Email**: <EMAIL>
- **Documentation**: https://docs.prochat.co.tz
- **GitHub Issues**: https://github.com/prochat/platform/issues

---

**Happy coding! 🚀**
