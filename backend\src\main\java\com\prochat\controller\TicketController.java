package com.prochat.controller;

import com.prochat.model.Ticket;
import com.prochat.model.TicketPurchase;
import com.prochat.service.TicketService;
import com.prochat.security.CurrentUser;
import com.prochat.security.UserPrincipal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

@RestController
@RequestMapping("/api/tickets")
@PreAuthorize("hasRole('USER')")
public class TicketController {

    @Autowired
    private TicketService ticketService;

    @GetMapping("/event/{eventId}")
    public ResponseEntity<?> getEventTickets(@PathVariable Long eventId) {
        try {
            Map<String, Object> eventData = ticketService.getEventWithTickets(eventId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", eventData);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata tiketi: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/purchase")
    public ResponseEntity<?> purchaseTicket(
            @CurrentUser UserPrincipal currentUser,
            @Valid @RequestBody TicketPurchaseRequest request) {
        try {
            TicketPurchase purchase = ticketService.purchaseTicket(currentUser.getId(), request);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Tiketi imenunuliwa kwa mafanikio");
            response.put("data", purchase);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kununua tiketi: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/my-tickets")
    public ResponseEntity<?> getUserTickets(
            @CurrentUser UserPrincipal currentUser,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            List<TicketPurchase> tickets = ticketService.getUserTickets(currentUser.getId(), page, size);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", tickets);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata tiketi zako: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/purchase/{purchaseId}")
    public ResponseEntity<?> getTicketPurchase(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable Long purchaseId) {
        try {
            TicketPurchase purchase = ticketService.getTicketPurchase(purchaseId, currentUser.getId());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", purchase);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata tiketi: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/validate/{ticketNumber}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('EVENT_ORGANIZER')")
    public ResponseEntity<?> validateTicket(
            @PathVariable String ticketNumber,
            @RequestParam Long eventId) {
        try {
            Map<String, Object> validationResult = ticketService.validateTicket(ticketNumber, eventId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", validationResult);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kuthibitisha tiketi: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/check-in/{ticketNumber}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('EVENT_ORGANIZER')")
    public ResponseEntity<?> checkInTicket(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable String ticketNumber,
            @RequestParam Long eventId) {
        try {
            Map<String, Object> checkInResult = ticketService.checkInTicket(ticketNumber, eventId, currentUser.getId());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Tiketi imehakikiwa kwa mafanikio");
            response.put("data", checkInResult);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kuhakiki tiketi: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/qr-code/{ticketNumber}")
    public ResponseEntity<?> getTicketQRCode(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable String ticketNumber) {
        try {
            String qrCode = ticketService.generateTicketQRCode(ticketNumber, currentUser.getId());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", Map.of("qrCode", qrCode));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata QR code: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/transfer")
    public ResponseEntity<?> transferTicket(
            @CurrentUser UserPrincipal currentUser,
            @Valid @RequestBody TicketTransferRequest request) {
        try {
            TicketPurchase transferredTicket = ticketService.transferTicket(
                currentUser.getId(), 
                request.getTicketNumber(), 
                request.getRecipientEmail()
            );
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Tiketi imehamishwa kwa mafanikio");
            response.put("data", transferredTicket);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kuhamisha tiketi: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/refund/{ticketNumber}")
    public ResponseEntity<?> requestRefund(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable String ticketNumber,
            @RequestBody RefundRequest request) {
        try {
            Map<String, Object> refundResult = ticketService.requestRefund(
                currentUser.getId(), 
                ticketNumber, 
                request.getReason()
            );
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Ombi la kurudishiwa pesa limepokewa");
            response.put("data", refundResult);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kuomba kurudishiwa pesa: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/sales/event/{eventId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('EVENT_ORGANIZER')")
    public ResponseEntity<?> getEventTicketSales(
            @PathVariable Long eventId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "50") int size) {
        try {
            Map<String, Object> salesData = ticketService.getEventTicketSales(eventId, page, size);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", salesData);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata takwimu za mauzo: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/analytics/event/{eventId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('EVENT_ORGANIZER')")
    public ResponseEntity<?> getEventTicketAnalytics(@PathVariable Long eventId) {
        try {
            Map<String, Object> analytics = ticketService.getEventTicketAnalytics(eventId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", analytics);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata takwimu: " + e.getMessage()
            ));
        }
    }

    // Admin endpoints
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> createTicketType(
            @Valid @RequestBody CreateTicketRequest request) {
        try {
            Ticket ticket = ticketService.createTicketType(request);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Aina ya tiketi imeundwa");
            response.put("data", ticket);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kuunda aina ya tiketi: " + e.getMessage()
            ));
        }
    }

    @PutMapping("/{ticketId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateTicketType(
            @PathVariable Long ticketId,
            @Valid @RequestBody UpdateTicketRequest request) {
        try {
            Ticket ticket = ticketService.updateTicketType(ticketId, request);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Aina ya tiketi imesasishwa");
            response.put("data", ticket);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kusasisha: " + e.getMessage()
            ));
        }
    }

    @DeleteMapping("/{ticketId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteTicketType(@PathVariable Long ticketId) {
        try {
            ticketService.deleteTicketType(ticketId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Aina ya tiketi imefutwa");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kufuta: " + e.getMessage()
            ));
        }
    }

    // Request DTOs
    public static class TicketPurchaseRequest {
        private Long eventId;
        private Long ticketTypeId;
        private Integer quantity;
        private Double totalAmount;
        
        // Getters and setters
        public Long getEventId() { return eventId; }
        public void setEventId(Long eventId) { this.eventId = eventId; }
        public Long getTicketTypeId() { return ticketTypeId; }
        public void setTicketTypeId(Long ticketTypeId) { this.ticketTypeId = ticketTypeId; }
        public Integer getQuantity() { return quantity; }
        public void setQuantity(Integer quantity) { this.quantity = quantity; }
        public Double getTotalAmount() { return totalAmount; }
        public void setTotalAmount(Double totalAmount) { this.totalAmount = totalAmount; }
    }

    public static class TicketTransferRequest {
        private String ticketNumber;
        private String recipientEmail;
        
        // Getters and setters
        public String getTicketNumber() { return ticketNumber; }
        public void setTicketNumber(String ticketNumber) { this.ticketNumber = ticketNumber; }
        public String getRecipientEmail() { return recipientEmail; }
        public void setRecipientEmail(String recipientEmail) { this.recipientEmail = recipientEmail; }
    }

    public static class RefundRequest {
        private String reason;
        
        // Getters and setters
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }

    public static class CreateTicketRequest {
        private Long eventId;
        private String name;
        private String description;
        private String type;
        private Double price;
        private Integer totalQuantity;
        private Integer maxPerPerson;
        private String saleStartDate;
        private String saleEndDate;
        
        // Getters and setters
        public Long getEventId() { return eventId; }
        public void setEventId(Long eventId) { this.eventId = eventId; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public Double getPrice() { return price; }
        public void setPrice(Double price) { this.price = price; }
        public Integer getTotalQuantity() { return totalQuantity; }
        public void setTotalQuantity(Integer totalQuantity) { this.totalQuantity = totalQuantity; }
        public Integer getMaxPerPerson() { return maxPerPerson; }
        public void setMaxPerPerson(Integer maxPerPerson) { this.maxPerPerson = maxPerPerson; }
        public String getSaleStartDate() { return saleStartDate; }
        public void setSaleStartDate(String saleStartDate) { this.saleStartDate = saleStartDate; }
        public String getSaleEndDate() { return saleEndDate; }
        public void setSaleEndDate(String saleEndDate) { this.saleEndDate = saleEndDate; }
    }

    public static class UpdateTicketRequest {
        private String name;
        private String description;
        private Double price;
        private Integer totalQuantity;
        private Integer maxPerPerson;
        private String saleStartDate;
        private String saleEndDate;
        
        // Getters and setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public Double getPrice() { return price; }
        public void setPrice(Double price) { this.price = price; }
        public Integer getTotalQuantity() { return totalQuantity; }
        public void setTotalQuantity(Integer totalQuantity) { this.totalQuantity = totalQuantity; }
        public Integer getMaxPerPerson() { return maxPerPerson; }
        public void setMaxPerPerson(Integer maxPerPerson) { this.maxPerPerson = maxPerPerson; }
        public String getSaleStartDate() { return saleStartDate; }
        public void setSaleStartDate(String saleStartDate) { this.saleStartDate = saleStartDate; }
        public String getSaleEndDate() { return saleEndDate; }
        public void setSaleEndDate(String saleEndDate) { this.saleEndDate = saleEndDate; }
    }
}
