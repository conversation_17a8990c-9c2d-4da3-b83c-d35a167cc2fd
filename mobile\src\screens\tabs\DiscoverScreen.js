import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
  FlatList,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const { width } = Dimensions.get('window');

// Mock data
const mockStories = [
  { id: '1', user: 'Wewe', avatar: 'https://via.placeholder.com/60', hasStory: false, isAddStory: true },
  { id: '2', user: '<PERSON>', avatar: 'https://via.placeholder.com/60', hasStory: true },
  { id: '3', user: 'Mary', avatar: 'https://via.placeholder.com/60', hasStory: true },
  { id: '4', user: 'Peter', avatar: 'https://via.placeholder.com/60', hasStory: true },
  { id: '5', user: 'Grace', avatar: 'https://via.placeholder.com/60', hasStory: true },
];

const mockNews = [
  {
    id: '1',
    title: '<PERSON><PERSON><PERSON> wa Teknolojia',
    summary: 'Mradi huu utasaidia vijana kupata ajira za teknolojia...',
    image: 'https://via.placeholder.com/150x100',
    time: '2 saa zilizopita',
    likes: 45,
    comments: 12,
    source: 'Daily News',
  },
  {
    id: '2',
    title: 'Uchumi wa Tanzania Unaongezeka',
    summary: 'Takwimu mpya zinaonyesha ukuaji wa asilimia 6.8...',
    image: 'https://via.placeholder.com/150x100',
    time: '4 saa zilizopita',
    likes: 78,
    comments: 23,
    source: 'The Citizen',
  },
];

const mockVideos = [
  {
    id: '1',
    title: 'Jinsi ya Kutumia ProPay',
    thumbnail: 'https://via.placeholder.com/200x150',
    duration: '2:34',
    views: '1.2K',
    creator: 'ProChat Tutorial',
  },
  {
    id: '2',
    title: 'Mazungumzo ya Biashara',
    thumbnail: 'https://via.placeholder.com/200x150',
    duration: '5:12',
    views: '856',
    creator: 'Business Tips TZ',
  },
];

const mockLiveStreams = [
  {
    id: '1',
    title: 'Mazungumzo ya Moja kwa Moja',
    thumbnail: 'https://via.placeholder.com/200x150',
    viewers: '234',
    streamer: 'John Mwangi',
    isLive: true,
  },
];

export default function DiscoverScreen({ navigation }) {
  const [activeTab, setActiveTab] = useState('stories');

  const renderStoryItem = ({ item }) => (
    <TouchableOpacity style={styles.storyItem}>
      <View style={[styles.storyAvatar, item.hasStory && styles.storyAvatarWithStory]}>
        <Image source={{ uri: item.avatar }} style={styles.storyImage} />
        {item.isAddStory && (
          <View style={styles.addStoryButton}>
            <Ionicons name="add" size={16} color="#FFFFFF" />
          </View>
        )}
        {item.isLive && (
          <View style={styles.liveIndicator}>
            <Text style={styles.liveText}>LIVE</Text>
          </View>
        )}
      </View>
      <Text style={styles.storyUsername} numberOfLines={1}>
        {item.user}
      </Text>
    </TouchableOpacity>
  );

  const renderNewsItem = ({ item }) => (
    <TouchableOpacity style={styles.newsItem}>
      <Image source={{ uri: item.image }} style={styles.newsImage} />
      <View style={styles.newsContent}>
        <Text style={styles.newsTitle} numberOfLines={2}>
          {item.title}
        </Text>
        <Text style={styles.newsSummary} numberOfLines={2}>
          {item.summary}
        </Text>
        <View style={styles.newsFooter}>
          <Text style={styles.newsSource}>{item.source}</Text>
          <Text style={styles.newsTime}>{item.time}</Text>
        </View>
        <View style={styles.newsActions}>
          <TouchableOpacity style={styles.newsAction}>
            <Ionicons name="heart-outline" size={16} color="#666" />
            <Text style={styles.newsActionText}>{item.likes}</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.newsAction}>
            <Ionicons name="chatbubble-outline" size={16} color="#666" />
            <Text style={styles.newsActionText}>{item.comments}</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.newsAction}>
            <Ionicons name="bookmark-outline" size={16} color="#666" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.newsAction}>
            <Ionicons name="gift-outline" size={16} color="#666" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.newsAction}>
            <Ionicons name="volume-high-outline" size={16} color="#666" />
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderVideoItem = ({ item }) => (
    <TouchableOpacity style={styles.videoItem}>
      <View style={styles.videoThumbnailContainer}>
        <Image source={{ uri: item.thumbnail }} style={styles.videoThumbnail} />
        <View style={styles.videoDuration}>
          <Text style={styles.videoDurationText}>{item.duration}</Text>
        </View>
        <View style={styles.videoPlayButton}>
          <Ionicons name="play" size={24} color="#FFFFFF" />
        </View>
      </View>
      <Text style={styles.videoTitle} numberOfLines={2}>
        {item.title}
      </Text>
      <Text style={styles.videoCreator}>{item.creator}</Text>
      <Text style={styles.videoViews}>{item.views} views</Text>
    </TouchableOpacity>
  );

  const renderLiveStreamItem = ({ item }) => (
    <TouchableOpacity style={styles.liveStreamItem}>
      <View style={styles.liveStreamThumbnailContainer}>
        <Image source={{ uri: item.thumbnail }} style={styles.liveStreamThumbnail} />
        <View style={styles.liveStreamBadge}>
          <Text style={styles.liveStreamBadgeText}>LIVE</Text>
        </View>
        <View style={styles.liveStreamViewers}>
          <Ionicons name="eye" size={12} color="#FFFFFF" />
          <Text style={styles.liveStreamViewersText}>{item.viewers}</Text>
        </View>
      </View>
      <Text style={styles.liveStreamTitle} numberOfLines={2}>
        {item.title}
      </Text>
      <Text style={styles.liveStreamStreamer}>{item.streamer}</Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Gundua</Text>
        <TouchableOpacity style={styles.searchButton}>
          <Ionicons name="search" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'stories' && styles.activeTab]}
          onPress={() => setActiveTab('stories')}
        >
          <Text style={[styles.tabText, activeTab === 'stories' && styles.activeTabText]}>
            Stories
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'news' && styles.activeTab]}
          onPress={() => setActiveTab('news')}
        >
          <Text style={[styles.tabText, activeTab === 'news' && styles.activeTabText]}>
            Habari
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'videos' && styles.activeTab]}
          onPress={() => setActiveTab('videos')}
        >
          <Text style={[styles.tabText, activeTab === 'videos' && styles.activeTabText]}>
            Video
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'live' && styles.activeTab]}
          onPress={() => setActiveTab('live')}
        >
          <Text style={[styles.tabText, activeTab === 'live' && styles.activeTabText]}>
            Live
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {activeTab === 'stories' && (
          <View>
            <FlatList
              data={mockStories}
              renderItem={renderStoryItem}
              keyExtractor={(item) => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.storiesList}
              contentContainerStyle={styles.storiesContent}
            />
            
            {/* Channels Section */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Channels</Text>
              <TouchableOpacity style={styles.channelItem}>
                <Image source={{ uri: 'https://via.placeholder.com/40' }} style={styles.channelAvatar} />
                <View style={styles.channelInfo}>
                  <Text style={styles.channelName}>ProChat Official</Text>
                  <Text style={styles.channelDescription}>Habari na updates za ProChat</Text>
                </View>
                <TouchableOpacity style={styles.followButton}>
                  <Text style={styles.followButtonText}>Fuata</Text>
                </TouchableOpacity>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {activeTab === 'news' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Habari za Leo</Text>
            <FlatList
              data={mockNews}
              renderItem={renderNewsItem}
              keyExtractor={(item) => item.id}
              scrollEnabled={false}
            />
          </View>
        )}

        {activeTab === 'videos' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Video za Mfupi</Text>
            <FlatList
              data={mockVideos}
              renderItem={renderVideoItem}
              keyExtractor={(item) => item.id}
              numColumns={2}
              scrollEnabled={false}
              columnWrapperStyle={styles.videoRow}
            />
          </View>
        )}

        {activeTab === 'live' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Mazungumzo ya Moja kwa Moja</Text>
            <TouchableOpacity style={styles.goLiveButton}>
              <Ionicons name="videocam" size={24} color="#FFFFFF" />
              <Text style={styles.goLiveText}>Anza Live Stream</Text>
            </TouchableOpacity>
            <FlatList
              data={mockLiveStreams}
              renderItem={renderLiveStreamItem}
              keyExtractor={(item) => item.id}
              scrollEnabled={false}
            />
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  searchButton: {
    padding: 5,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#F8F9FA',
    paddingHorizontal: 20,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#007AFF',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  activeTabText: {
    color: '#007AFF',
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 15,
  },
  // Stories styles
  storiesList: {
    paddingVertical: 15,
  },
  storiesContent: {
    paddingHorizontal: 20,
  },
  storyItem: {
    alignItems: 'center',
    marginRight: 15,
    width: 70,
  },
  storyAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginBottom: 8,
    position: 'relative',
  },
  storyAvatarWithStory: {
    borderWidth: 2,
    borderColor: '#007AFF',
  },
  storyImage: {
    width: '100%',
    height: '100%',
    borderRadius: 30,
  },
  addStoryButton: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  liveIndicator: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FF3B30',
    borderRadius: 8,
    paddingVertical: 2,
    alignItems: 'center',
  },
  liveText: {
    color: '#FFFFFF',
    fontSize: 8,
    fontWeight: 'bold',
  },
  storyUsername: {
    fontSize: 12,
    color: '#333',
    textAlign: 'center',
  },
  // News styles
  newsItem: {
    flexDirection: 'row',
    marginBottom: 20,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  newsImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: 12,
  },
  newsContent: {
    flex: 1,
  },
  newsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  newsSummary: {
    fontSize: 12,
    color: '#666',
    marginBottom: 8,
  },
  newsFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  newsSource: {
    fontSize: 10,
    color: '#007AFF',
    fontWeight: '500',
  },
  newsTime: {
    fontSize: 10,
    color: '#999',
  },
  newsActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  newsAction: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  newsActionText: {
    fontSize: 10,
    color: '#666',
    marginLeft: 4,
  },
  // Video styles
  videoRow: {
    justifyContent: 'space-between',
  },
  videoItem: {
    width: (width - 60) / 2,
    marginBottom: 20,
  },
  videoThumbnailContainer: {
    position: 'relative',
    marginBottom: 8,
  },
  videoThumbnail: {
    width: '100%',
    height: 120,
    borderRadius: 8,
  },
  videoDuration: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  videoDurationText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '500',
  },
  videoPlayButton: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -20 }, { translateY: -20 }],
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  videoTitle: {
    fontSize: 12,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  videoCreator: {
    fontSize: 10,
    color: '#666',
    marginBottom: 2,
  },
  videoViews: {
    fontSize: 10,
    color: '#999',
  },
  // Live stream styles
  goLiveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FF3B30',
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  goLiveText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  liveStreamItem: {
    marginBottom: 20,
  },
  liveStreamThumbnailContainer: {
    position: 'relative',
    marginBottom: 8,
  },
  liveStreamThumbnail: {
    width: '100%',
    height: 180,
    borderRadius: 8,
  },
  liveStreamBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: '#FF3B30',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  liveStreamBadgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  liveStreamViewers: {
    position: 'absolute',
    top: 8,
    right: 8,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 6,
    paddingVertical: 4,
    borderRadius: 4,
  },
  liveStreamViewersText: {
    color: '#FFFFFF',
    fontSize: 10,
    marginLeft: 4,
  },
  liveStreamTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  liveStreamStreamer: {
    fontSize: 12,
    color: '#666',
  },
  // Channel styles
  channelItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  channelAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  channelInfo: {
    flex: 1,
  },
  channelName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  channelDescription: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  followButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 16,
  },
  followButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
});
