{"ast": null, "code": "var isPlainObject = require('./isPlainObject');\n\n/**\n * Used by `_.omit` to customize its `_.cloneDeep` use to only clone plain\n * objects.\n *\n * @private\n * @param {*} value The value to inspect.\n * @param {string} key The key of the property to inspect.\n * @returns {*} Returns the uncloned value or `undefined` to defer cloning to `_.cloneDeep`.\n */\nfunction customOmitClone(value) {\n  return isPlainObject(value) ? undefined : value;\n}\nmodule.exports = customOmitClone;", "map": {"version": 3, "names": ["isPlainObject", "require", "customOmitClone", "value", "undefined", "module", "exports"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/lodash/_customOmitClone.js"], "sourcesContent": ["var isPlainObject = require('./isPlainObject');\n\n/**\n * Used by `_.omit` to customize its `_.cloneDeep` use to only clone plain\n * objects.\n *\n * @private\n * @param {*} value The value to inspect.\n * @param {string} key The key of the property to inspect.\n * @returns {*} Returns the uncloned value or `undefined` to defer cloning to `_.cloneDeep`.\n */\nfunction customOmitClone(value) {\n  return isPlainObject(value) ? undefined : value;\n}\n\nmodule.exports = customOmitClone;\n"], "mappings": "AAAA,IAAIA,aAAa,GAAGC,OAAO,CAAC,iBAAiB,CAAC;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,OAAOH,aAAa,CAACG,KAAK,CAAC,GAAGC,SAAS,GAAGD,KAAK;AACjD;AAEAE,MAAM,CAACC,OAAO,GAAGJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}