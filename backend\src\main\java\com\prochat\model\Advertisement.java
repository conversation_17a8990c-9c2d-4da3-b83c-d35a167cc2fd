package com.prochat.model;

import jakarta.persistence.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "advertisements")
@EntityListeners(AuditingEntityListener.class)
public class Advertisement {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @Column(name = "title", nullable = false)
    private String title;
    
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "ad_type")
    private AdType adType;
    
    @ElementCollection
    @CollectionTable(name = "advertisement_media", joinColumns = @JoinColumn(name = "advertisement_id"))
    @Column(name = "media_url")
    private List<String> mediaUrls;
    
    @Column(name = "business_link")
    private String businessLink;
    
    @Column(name = "contact_info")
    private String contactInfo;
    
    // Targeting options
    @Column(name = "target_age_min")
    private Integer targetAgeMin;
    
    @Column(name = "target_age_max")
    private Integer targetAgeMax;
    
    @Column(name = "target_gender")
    private String targetGender; // MALE, FEMALE, ALL
    
    @Column(name = "target_location")
    private String targetLocation;
    
    @Column(name = "target_radius_km")
    private Double targetRadiusKm;
    
    @Column(name = "target_interests")
    private String targetInterests; // Comma-separated
    
    // Budget and billing
    @Column(name = "budget_amount", precision = 15, scale = 2)
    private BigDecimal budgetAmount;
    
    @Column(name = "cost_per_view", precision = 10, scale = 4)
    private BigDecimal costPerView = new BigDecimal("0.10"); // 100 TSH per view
    
    @Column(name = "cost_per_click", precision = 10, scale = 4)
    private BigDecimal costPerClick = new BigDecimal("0.50"); // 500 TSH per click
    
    @Column(name = "total_spent", precision = 15, scale = 2)
    private BigDecimal totalSpent = BigDecimal.ZERO;
    
    // Performance metrics
    @Column(name = "views_count")
    private Long viewsCount = 0L;
    
    @Column(name = "clicks_count")
    private Long clicksCount = 0L;
    
    @Column(name = "conversions_count")
    private Long conversionsCount = 0L;
    
    @Column(name = "impressions_count")
    private Long impressionsCount = 0L;
    
    // Schedule
    @Column(name = "start_date")
    private LocalDateTime startDate;
    
    @Column(name = "end_date")
    private LocalDateTime endDate;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private AdStatus status = AdStatus.PENDING;
    
    @Column(name = "rejection_reason")
    private String rejectionReason;
    
    @Column(name = "is_featured")
    private Boolean isFeatured = false;
    
    @Column(name = "priority_level")
    private Integer priorityLevel = 1; // 1-10, higher is more priority
    
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Constructors
    public Advertisement() {}
    
    public Advertisement(User user, String title, String description, AdType adType) {
        this.user = user;
        this.title = title;
        this.description = description;
        this.adType = adType;
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public User getUser() { return user; }
    public void setUser(User user) { this.user = user; }
    
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public AdType getAdType() { return adType; }
    public void setAdType(AdType adType) { this.adType = adType; }
    
    public List<String> getMediaUrls() { return mediaUrls; }
    public void setMediaUrls(List<String> mediaUrls) { this.mediaUrls = mediaUrls; }
    
    public String getBusinessLink() { return businessLink; }
    public void setBusinessLink(String businessLink) { this.businessLink = businessLink; }
    
    public String getContactInfo() { return contactInfo; }
    public void setContactInfo(String contactInfo) { this.contactInfo = contactInfo; }
    
    public Integer getTargetAgeMin() { return targetAgeMin; }
    public void setTargetAgeMin(Integer targetAgeMin) { this.targetAgeMin = targetAgeMin; }
    
    public Integer getTargetAgeMax() { return targetAgeMax; }
    public void setTargetAgeMax(Integer targetAgeMax) { this.targetAgeMax = targetAgeMax; }
    
    public String getTargetGender() { return targetGender; }
    public void setTargetGender(String targetGender) { this.targetGender = targetGender; }
    
    public String getTargetLocation() { return targetLocation; }
    public void setTargetLocation(String targetLocation) { this.targetLocation = targetLocation; }
    
    public Double getTargetRadiusKm() { return targetRadiusKm; }
    public void setTargetRadiusKm(Double targetRadiusKm) { this.targetRadiusKm = targetRadiusKm; }
    
    public String getTargetInterests() { return targetInterests; }
    public void setTargetInterests(String targetInterests) { this.targetInterests = targetInterests; }
    
    public BigDecimal getBudgetAmount() { return budgetAmount; }
    public void setBudgetAmount(BigDecimal budgetAmount) { this.budgetAmount = budgetAmount; }
    
    public BigDecimal getCostPerView() { return costPerView; }
    public void setCostPerView(BigDecimal costPerView) { this.costPerView = costPerView; }
    
    public BigDecimal getCostPerClick() { return costPerClick; }
    public void setCostPerClick(BigDecimal costPerClick) { this.costPerClick = costPerClick; }
    
    public BigDecimal getTotalSpent() { return totalSpent; }
    public void setTotalSpent(BigDecimal totalSpent) { this.totalSpent = totalSpent; }
    
    public Long getViewsCount() { return viewsCount; }
    public void setViewsCount(Long viewsCount) { this.viewsCount = viewsCount; }
    
    public Long getClicksCount() { return clicksCount; }
    public void setClicksCount(Long clicksCount) { this.clicksCount = clicksCount; }
    
    public Long getConversionsCount() { return conversionsCount; }
    public void setConversionsCount(Long conversionsCount) { this.conversionsCount = conversionsCount; }
    
    public Long getImpressionsCount() { return impressionsCount; }
    public void setImpressionsCount(Long impressionsCount) { this.impressionsCount = impressionsCount; }
    
    public LocalDateTime getStartDate() { return startDate; }
    public void setStartDate(LocalDateTime startDate) { this.startDate = startDate; }
    
    public LocalDateTime getEndDate() { return endDate; }
    public void setEndDate(LocalDateTime endDate) { this.endDate = endDate; }
    
    public AdStatus getStatus() { return status; }
    public void setStatus(AdStatus status) { this.status = status; }
    
    public String getRejectionReason() { return rejectionReason; }
    public void setRejectionReason(String rejectionReason) { this.rejectionReason = rejectionReason; }
    
    public Boolean getIsFeatured() { return isFeatured; }
    public void setIsFeatured(Boolean isFeatured) { this.isFeatured = isFeatured; }
    
    public Integer getPriorityLevel() { return priorityLevel; }
    public void setPriorityLevel(Integer priorityLevel) { this.priorityLevel = priorityLevel; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
