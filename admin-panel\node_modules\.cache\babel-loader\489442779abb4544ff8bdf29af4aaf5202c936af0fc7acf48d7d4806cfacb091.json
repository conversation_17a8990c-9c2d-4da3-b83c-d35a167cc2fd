{"ast": null, "code": "import memoize from 'lodash/memoize';\n/**\n * This is memoized because the viewBox is unlikely to change often\n * - but because it is computed from offset, any change to it would re-render all children.\n *\n * And because we have many readers of the viewBox, and update it only rarely,\n * then let's optimize with memoization.\n */\nexport var calculateViewBox = memoize(function (offset) {\n  return {\n    x: offset.left,\n    y: offset.top,\n    width: offset.width,\n    height: offset.height\n  };\n}, function (offset) {\n  return ['l', offset.left, 't', offset.top, 'w', offset.width, 'h', offset.height].join('');\n});", "map": {"version": 3, "names": ["memoize", "calculateViewBox", "offset", "x", "left", "y", "top", "width", "height", "join"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/recharts/es6/util/calculateViewBox.js"], "sourcesContent": ["import memoize from 'lodash/memoize';\n/**\n * This is memoized because the viewBox is unlikely to change often\n * - but because it is computed from offset, any change to it would re-render all children.\n *\n * And because we have many readers of the viewBox, and update it only rarely,\n * then let's optimize with memoization.\n */\nexport var calculateViewBox = memoize(function (offset) {\n  return {\n    x: offset.left,\n    y: offset.top,\n    width: offset.width,\n    height: offset.height\n  };\n}, function (offset) {\n  return ['l', offset.left, 't', offset.top, 'w', offset.width, 'h', offset.height].join('');\n});"], "mappings": "AAAA,OAAOA,OAAO,MAAM,gBAAgB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,gBAAgB,GAAGD,OAAO,CAAC,UAAUE,MAAM,EAAE;EACtD,OAAO;IACLC,CAAC,EAAED,MAAM,CAACE,IAAI;IACdC,CAAC,EAAEH,MAAM,CAACI,GAAG;IACbC,KAAK,EAAEL,MAAM,CAACK,KAAK;IACnBC,MAAM,EAAEN,MAAM,CAACM;EACjB,CAAC;AACH,CAAC,EAAE,UAAUN,MAAM,EAAE;EACnB,OAAO,CAAC,GAAG,EAAEA,MAAM,CAACE,IAAI,EAAE,GAAG,EAAEF,MAAM,CAACI,GAAG,EAAE,GAAG,EAAEJ,MAAM,CAACK,KAAK,EAAE,GAAG,EAAEL,MAAM,CAACM,MAAM,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;AAC5F,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}