import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Grid,
  TextField,
  Switch,
  FormControlLabel,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Paper,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Save,
  Edit,
  Download,
  Receipt,
  Assessment,
  AccountBalance,
  TrendingUp,
  Warning,
  CheckCircle,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

export default function TaxManagement() {
  const [currentTab, setCurrentTab] = useState(0);
  const [vatDialogOpen, setVatDialogOpen] = useState(false);
  const [reportDialogOpen, setReportDialogOpen] = useState(false);
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date());

  const [taxConfig] = useState({
    vatRate: 18.0,
    isActive: true,
    taxId: 'PROCHAT-TIN-*********',
    taxAuthority: 'Tanzania Revenue Authority (TRA)',
    appliesToTransactions: true,
    appliesToWithdrawals: true,
    appliesToDonations: true,
    appliesToMerchantPayments: true,
    appliesToGifts: false,
    appliesToTickets: true,
  });

  const [taxSummary] = useState({
    monthlyTaxCollected: 2500000,
    yearlyTaxCollected: 18750000,
    pendingRemittance: 850000,
    totalTransactions: 15420,
  });

  const [recentTransactions] = useState([
    {
      id: 1,
      reference: 'TAX1704123456001',
      user: 'John Doe',
      type: 'SEND_MONEY',
      grossAmount: 100000,
      taxAmount: 18000,
      netAmount: 82000,
      date: '2024-01-15 14:30',
      status: 'COLLECTED',
    },
    {
      id: 2,
      reference: 'TAX1704123456002',
      user: 'Jane Smith',
      type: 'WITHDRAWAL',
      grossAmount: 50000,
      taxAmount: 9000,
      netAmount: 41000,
      date: '2024-01-15 13:45',
      status: 'COLLECTED',
    },
    {
      id: 3,
      reference: 'TAX1704123456003',
      user: 'Mike Johnson',
      type: 'MERCHANT_PAYMENT',
      grossAmount: 75000,
      taxAmount: 13500,
      netAmount: 61500,
      date: '2024-01-15 12:20',
      status: 'COLLECTED',
    },
  ]);

  const tabs = [
    { label: 'Mipangilio ya VAT', icon: <AccountBalance /> },
    { label: 'Muhtasari wa Kodi', icon: <Assessment /> },
    { label: 'Miamala ya Kodi', icon: <Receipt /> },
    { label: 'Ripoti za VAT', icon: <TrendingUp /> },
  ];

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const renderVATSettings = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">Mipangilio ya VAT</Typography>
        <Button variant="contained" startIcon={<Edit />} onClick={() => setVatDialogOpen(true)}>
          Hariri Mipangilio
        </Button>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Kiwango cha VAT
              </Typography>
              <Typography variant="h3" color="primary" gutterBottom>
                {taxConfig.vatRate}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Kiwango cha sasa cha VAT
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Chip
                  label={taxConfig.isActive ? 'Inatumika' : 'Imezimwa'}
                  color={taxConfig.isActive ? 'success' : 'error'}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Taarifa za Kodi
              </Typography>
              <Typography variant="body2" gutterBottom>
                <strong>Tax ID:</strong> {taxConfig.taxId}
              </Typography>
              <Typography variant="body2" gutterBottom>
                <strong>Mamlaka ya Kodi:</strong> {taxConfig.taxAuthority}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Taarifa za kampuni kwa ajili ya VAT
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Shughuli Zinazotozwa VAT
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={4}>
                  <FormControlLabel
                    control={<Switch checked={taxConfig.appliesToTransactions} />}
                    label="Kutuma Pesa (ProPay)"
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <FormControlLabel
                    control={<Switch checked={taxConfig.appliesToWithdrawals} />}
                    label="Kutoa Pesa"
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <FormControlLabel
                    control={<Switch checked={taxConfig.appliesToDonations} />}
                    label="Michango"
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <FormControlLabel
                    control={<Switch checked={taxConfig.appliesToMerchantPayments} />}
                    label="Malipo ya Biashara"
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <FormControlLabel
                    control={<Switch checked={taxConfig.appliesToGifts} />}
                    label="Zawadi"
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <FormControlLabel
                    control={<Switch checked={taxConfig.appliesToTickets} />}
                    label="Tiketi"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  const renderTaxSummary = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Muhtasari wa Kodi
      </Typography>

      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <AccountBalance color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Kodi ya Mwezi</Typography>
              </Box>
              <Typography variant="h4" color="primary">
                {formatCurrency(taxSummary.monthlyTaxCollected)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Kodi iliyokusanywa mwezi huu
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <TrendingUp color="success" sx={{ mr: 1 }} />
                <Typography variant="h6">Kodi ya Mwaka</Typography>
              </Box>
              <Typography variant="h4" color="success.main">
                {formatCurrency(taxSummary.yearlyTaxCollected)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Kodi iliyokusanywa mwaka huu
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Warning color="warning" sx={{ mr: 1 }} />
                <Typography variant="h6">Inasubiri Kutumwa</Typography>
              </Box>
              <Typography variant="h4" color="warning.main">
                {formatCurrency(taxSummary.pendingRemittance)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Kodi inayosubiri kutumwa TRA
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Receipt color="info" sx={{ mr: 1 }} />
                <Typography variant="h6">Miamala</Typography>
              </Box>
              <Typography variant="h4" color="info.main">
                {taxSummary.totalTransactions.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Jumla ya miamala ya kodi
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Alert severity="info" sx={{ mb: 3 }}>
        <strong>Kumbuka:</strong> VAT lazima itumwe kwa TRA kabla ya tarehe 20 ya kila mwezi.
        Kodi inayosubiri kutumwa ni {formatCurrency(taxSummary.pendingRemittance)}.
      </Alert>

      <Box sx={{ display: 'flex', gap: 2 }}>
        <Button variant="contained" startIcon={<Download />}>
          Tuma Kodi kwa TRA
        </Button>
        <Button variant="outlined" startIcon={<Receipt />}>
          Tengeneza Ripoti ya VAT
        </Button>
      </Box>
    </Box>
  );

  const renderTaxTransactions = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">Miamala ya Kodi</Typography>
        <Button variant="outlined" startIcon={<Download />}>
          Export CSV
        </Button>
      </Box>

      <TableContainer component={Card}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Reference</TableCell>
              <TableCell>Mtumiaji</TableCell>
              <TableCell>Aina ya Muamala</TableCell>
              <TableCell>Kiasi cha Jumla</TableCell>
              <TableCell>VAT</TableCell>
              <TableCell>Kiasi Baada ya VAT</TableCell>
              <TableCell>Tarehe</TableCell>
              <TableCell>Hali</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {recentTransactions.map((transaction) => (
              <TableRow key={transaction.id}>
                <TableCell>
                  <Typography variant="body2" fontFamily="monospace">
                    {transaction.reference}
                  </Typography>
                </TableCell>
                <TableCell>{transaction.user}</TableCell>
                <TableCell>
                  <Chip label={transaction.type} size="small" />
                </TableCell>
                <TableCell>{formatCurrency(transaction.grossAmount)}</TableCell>
                <TableCell>
                  <Typography variant="body2" color="error">
                    {formatCurrency(transaction.taxAmount)}
                  </Typography>
                </TableCell>
                <TableCell>{formatCurrency(transaction.netAmount)}</TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {transaction.date}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip
                    label={transaction.status === 'COLLECTED' ? 'Imekusanywa' : transaction.status}
                    color="success"
                    size="small"
                    icon={<CheckCircle />}
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );

  const renderVATReports = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">Ripoti za VAT</Typography>
        <Button variant="contained" startIcon={<Assessment />} onClick={() => setReportDialogOpen(true)}>
          Tengeneza Ripoti
        </Button>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Ripoti za Mwezi
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Ripoti za VAT za kila mwezi
              </Typography>
              <Button variant="outlined" size="small">
                Ona Ripoti za Mwezi
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Ripoti za Mwaka
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Ripoti za VAT za kila mwaka
              </Typography>
              <Button variant="outlined" size="small">
                Ona Ripoti za Mwaka
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Ripoti Maalum
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Tengeneza ripoti ya kipindi maalum
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    label="Tarehe ya Kuanza"
                    value={startDate}
                    onChange={setStartDate}
                    renderInput={(params) => <TextField {...params} size="small" />}
                  />
                  <DatePicker
                    label="Tarehe ya Mwisho"
                    value={endDate}
                    onChange={setEndDate}
                    renderInput={(params) => <TextField {...params} size="small" />}
                  />
                </LocalizationProvider>
                <Button variant="contained">
                  Tengeneza
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  const renderTabContent = () => {
    switch (currentTab) {
      case 0: return renderVATSettings();
      case 1: return renderTaxSummary();
      case 2: return renderTaxTransactions();
      case 3: return renderVATReports();
      default: return renderVATSettings();
    }
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Usimamizi wa Kodi (VAT)
        </Typography>
        <Button variant="contained" startIcon={<Save />}>
          Hifadhi Mabadiliko
        </Button>
      </Box>

      {/* Tabs */}
      <Card sx={{ mb: 3 }}>
        <Tabs
          value={currentTab}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
        >
          {tabs.map((tab, index) => (
            <Tab
              key={index}
              label={tab.label}
              icon={tab.icon}
              iconPosition="start"
            />
          ))}
        </Tabs>
      </Card>

      {/* Content */}
      {renderTabContent()}

      {/* VAT Settings Dialog */}
      <Dialog open={vatDialogOpen} onClose={() => setVatDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Hariri Mipangilio ya VAT</DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Kiwango cha VAT (%)"
                type="number"
                defaultValue={taxConfig.vatRate}
                inputProps={{ step: 0.01, min: 0, max: 100 }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Tax ID"
                defaultValue={taxConfig.taxId}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Mamlaka ya Kodi"
                defaultValue={taxConfig.taxAuthority}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={<Switch defaultChecked={taxConfig.isActive} />}
                label="Washa VAT"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setVatDialogOpen(false)}>
            Ghairi
          </Button>
          <Button variant="contained">
            Hifadhi
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
