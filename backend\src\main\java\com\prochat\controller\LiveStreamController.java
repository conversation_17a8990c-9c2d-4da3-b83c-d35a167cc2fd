package com.prochat.controller;

import com.prochat.model.LiveStream;
import com.prochat.model.StreamComment;
import com.prochat.service.LiveStreamService;
import com.prochat.security.CurrentUser;
import com.prochat.security.UserPrincipal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

@RestController
@RequestMapping("/api/streams")
@PreAuthorize("hasRole('USER')")
public class LiveStreamController {

    @Autowired
    private LiveStreamService liveStreamService;

    @GetMapping("/active")
    public ResponseEntity<?> getActiveStreams(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String category) {
        try {
            List<LiveStream> streams = liveStreamService.getActiveStreams(page, size, category);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", streams);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata live streams: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/{streamId}")
    public ResponseEntity<?> getStreamById(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable Long streamId) {
        try {
            LiveStream stream = liveStreamService.getStreamById(streamId, currentUser.getId());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", stream);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata stream: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/start")
    public ResponseEntity<?> startStream(
            @CurrentUser UserPrincipal currentUser,
            @Valid @RequestBody StartStreamRequest request) {
        try {
            LiveStream stream = liveStreamService.startStream(currentUser.getId(), request);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Live stream imeanza!");
            response.put("data", stream);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kuanza stream: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/{streamId}/end")
    public ResponseEntity<?> endStream(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable Long streamId) {
        try {
            LiveStream stream = liveStreamService.endStream(currentUser.getId(), streamId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Live stream imeisha");
            response.put("data", Map.of(
                "stream", stream,
                "totalViewers", stream.getTotalViewers(),
                "totalLikes", stream.getLikesCount(),
                "totalComments", stream.getCommentsCount(),
                "totalGifts", stream.getGiftsReceived(),
                "duration", stream.getDuration()
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kumaliza stream: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/{streamId}/like")
    public ResponseEntity<?> likeStream(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable Long streamId) {
        try {
            boolean isLiked = liveStreamService.toggleStreamLike(currentUser.getId(), streamId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", isLiked ? "Umependa stream" : "Umeondoa upendelezo");
            response.put("data", Map.of(
                "isLiked", isLiked,
                "totalLikes", liveStreamService.getStreamLikesCount(streamId)
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupenda stream: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/{streamId}/join")
    public ResponseEntity<?> joinStream(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable Long streamId) {
        try {
            liveStreamService.joinStream(currentUser.getId(), streamId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Umejiunga na stream");
            response.put("data", Map.of(
                "viewersCount", liveStreamService.getStreamViewersCount(streamId)
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kujiunga na stream: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/{streamId}/leave")
    public ResponseEntity<?> leaveStream(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable Long streamId) {
        try {
            liveStreamService.leaveStream(currentUser.getId(), streamId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Umeondoka kwenye stream");
            response.put("data", Map.of(
                "viewersCount", liveStreamService.getStreamViewersCount(streamId)
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kuondoka stream: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/{streamId}/comments")
    public ResponseEntity<?> getStreamComments(
            @PathVariable Long streamId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "50") int size) {
        try {
            List<StreamComment> comments = liveStreamService.getStreamComments(streamId, page, size);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", comments);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata maoni: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/{streamId}/comments")
    public ResponseEntity<?> addStreamComment(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable Long streamId,
            @Valid @RequestBody AddCommentRequest request) {
        try {
            StreamComment comment = liveStreamService.addStreamComment(
                currentUser.getId(), streamId, request.getMessage());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Maoni yameongezwa");
            response.put("data", comment);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kuongeza maoni: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/user/{userId}")
    public ResponseEntity<?> getUserStreams(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "all") String status) {
        try {
            List<LiveStream> streams = liveStreamService.getUserStreams(userId, page, size, status);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", streams);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata streams za mtumiaji: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/trending")
    public ResponseEntity<?> getTrendingStreams(
            @RequestParam(defaultValue = "10") int limit) {
        try {
            List<LiveStream> streams = liveStreamService.getTrendingStreams(limit);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", streams);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata streams maarufu: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/{streamId}/stats")
    public ResponseEntity<?> getStreamStats(@PathVariable Long streamId) {
        try {
            Map<String, Object> stats = liveStreamService.getStreamStats(streamId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", stats);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata takwimu: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/{streamId}/report")
    public ResponseEntity<?> reportStream(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable Long streamId,
            @Valid @RequestBody ReportStreamRequest request) {
        try {
            liveStreamService.reportStream(currentUser.getId(), streamId, request.getReason(), request.getDescription());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Ripoti imetumwa kwa msimamizi");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kutuma ripoti: " + e.getMessage()
            ));
        }
    }

    // Admin endpoints
    @GetMapping("/admin/reported")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> getReportedStreams(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            List<LiveStream> streams = liveStreamService.getReportedStreams(page, size);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", streams);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata streams zilizoripotiwa: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/{streamId}/moderate")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> moderateStream(
            @PathVariable Long streamId,
            @Valid @RequestBody ModerateStreamRequest request) {
        try {
            liveStreamService.moderateStream(streamId, request.getAction(), request.getReason());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Hatua ya uongozi imechukuliwa");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kusimamia: " + e.getMessage()
            ));
        }
    }

    // Request DTOs
    public static class StartStreamRequest {
        private String title;
        private String description;
        private String category;
        private String thumbnailUrl;
        private Boolean isPrivate;
        
        // Getters and setters
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }
        public String getThumbnailUrl() { return thumbnailUrl; }
        public void setThumbnailUrl(String thumbnailUrl) { this.thumbnailUrl = thumbnailUrl; }
        public Boolean getIsPrivate() { return isPrivate; }
        public void setIsPrivate(Boolean isPrivate) { this.isPrivate = isPrivate; }
    }

    public static class AddCommentRequest {
        private String message;
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }

    public static class ReportStreamRequest {
        private String reason;
        private String description;
        
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
    }

    public static class ModerateStreamRequest {
        private String action;
        private String reason;
        
        public String getAction() { return action; }
        public void setAction(String action) { this.action = action; }
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }
}
