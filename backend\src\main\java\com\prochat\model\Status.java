package com.prochat.model;

import jakarta.persistence.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "statuses")
@EntityListeners(AuditingEntityListener.class)
public class Status {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @Column(name = "caption", columnDefinition = "TEXT")
    private String caption;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "media_type")
    private StatusMediaType mediaType;
    
    @ElementCollection
    @CollectionTable(name = "status_media", joinColumns = @JoinColumn(name = "status_id"))
    @Column(name = "media_url")
    private List<String> mediaUrls;
    
    @Column(name = "background_color")
    private String backgroundColor = "#007AFF";
    
    @Column(name = "text_color")
    private String textColor = "#FFFFFF";
    
    @Column(name = "font_style")
    private String fontStyle = "normal";
    
    @Enumerated(EnumType.STRING)
    @Column(name = "privacy_setting")
    private StatusPrivacy privacySetting = StatusPrivacy.ALL_CONTACTS;
    
    @Column(name = "views_count")
    private Long viewsCount = 0L;
    
    @Column(name = "likes_count")
    private Long likesCount = 0L;
    
    @Column(name = "replies_count")
    private Long repliesCount = 0L;
    
    @Column(name = "expires_at")
    private LocalDateTime expiresAt;
    
    @Column(name = "is_archived")
    private Boolean isArchived = false;
    
    @Column(name = "location")
    private String location;
    
    @Column(name = "music_url")
    private String musicUrl;
    
    @Column(name = "music_title")
    private String musicTitle;
    
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Constructors
    public Status() {
        this.expiresAt = LocalDateTime.now().plusHours(24); // Status expires in 24 hours
    }
    
    public Status(User user, String caption, StatusMediaType mediaType) {
        this();
        this.user = user;
        this.caption = caption;
        this.mediaType = mediaType;
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public User getUser() { return user; }
    public void setUser(User user) { this.user = user; }
    
    public String getCaption() { return caption; }
    public void setCaption(String caption) { this.caption = caption; }
    
    public StatusMediaType getMediaType() { return mediaType; }
    public void setMediaType(StatusMediaType mediaType) { this.mediaType = mediaType; }
    
    public List<String> getMediaUrls() { return mediaUrls; }
    public void setMediaUrls(List<String> mediaUrls) { this.mediaUrls = mediaUrls; }
    
    public String getBackgroundColor() { return backgroundColor; }
    public void setBackgroundColor(String backgroundColor) { this.backgroundColor = backgroundColor; }
    
    public String getTextColor() { return textColor; }
    public void setTextColor(String textColor) { this.textColor = textColor; }
    
    public String getFontStyle() { return fontStyle; }
    public void setFontStyle(String fontStyle) { this.fontStyle = fontStyle; }
    
    public StatusPrivacy getPrivacySetting() { return privacySetting; }
    public void setPrivacySetting(StatusPrivacy privacySetting) { this.privacySetting = privacySetting; }
    
    public Long getViewsCount() { return viewsCount; }
    public void setViewsCount(Long viewsCount) { this.viewsCount = viewsCount; }
    
    public Long getLikesCount() { return likesCount; }
    public void setLikesCount(Long likesCount) { this.likesCount = likesCount; }
    
    public Long getRepliesCount() { return repliesCount; }
    public void setRepliesCount(Long repliesCount) { this.repliesCount = repliesCount; }
    
    public LocalDateTime getExpiresAt() { return expiresAt; }
    public void setExpiresAt(LocalDateTime expiresAt) { this.expiresAt = expiresAt; }
    
    public Boolean getIsArchived() { return isArchived; }
    public void setIsArchived(Boolean isArchived) { this.isArchived = isArchived; }
    
    public String getLocation() { return location; }
    public void setLocation(String location) { this.location = location; }
    
    public String getMusicUrl() { return musicUrl; }
    public void setMusicUrl(String musicUrl) { this.musicUrl = musicUrl; }
    
    public String getMusicTitle() { return musicTitle; }
    public void setMusicTitle(String musicTitle) { this.musicTitle = musicTitle; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
