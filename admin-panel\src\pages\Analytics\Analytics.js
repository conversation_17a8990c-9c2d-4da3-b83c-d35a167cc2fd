import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Button,
  Grid,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Download,
  TrendingUp,
  People,
  AccountBalance,
  Event,
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';

export default function Analytics() {
  const [timeRange, setTimeRange] = useState('7days');

  const userGrowthData = [
    { date: '2024-01-14', users: 1200, newUsers: 45 },
    { date: '2024-01-15', users: 1245, newUsers: 52 },
    { date: '2024-01-16', users: 1297, newUsers: 38 },
    { date: '2024-01-17', users: 1335, newUsers: 67 },
    { date: '2024-01-18', users: 1402, newUsers: 43 },
    { date: '2024-01-19', users: 1445, newUsers: 58 },
    { date: '2024-01-20', users: 1503, newUsers: 71 },
  ];

  const revenueData = [
    { month: 'Jan', revenue: 2500000, transactions: 1200 },
    { month: 'Feb', revenue: 3200000, transactions: 1800 },
    { month: 'Mar', revenue: 4100000, transactions: 2100 },
    { month: 'Apr', revenue: 3800000, transactions: 1950 },
    { month: 'May', revenue: 4500000, transactions: 2300 },
    { month: 'Jun', revenue: 5200000, transactions: 2650 },
  ];

  const platformUsageData = [
    { name: 'Mobile App', value: 75, color: '#007AFF' },
    { name: 'Public Website', value: 20, color: '#4CAF50' },
    { name: 'Admin Panel', value: 5, color: '#FF9800' },
  ];

  const contentTypeData = [
    { type: 'Machapisho', count: 15420 },
    { type: 'Video Fupi', count: 8930 },
    { type: 'Habari', count: 2340 },
    { type: 'Matukio', count: 1250 },
    { type: 'Kazi', count: 890 },
  ];

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Takwimu na Uchambuzi
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Muda</InputLabel>
            <Select
              value={timeRange}
              label="Muda"
              onChange={(e) => setTimeRange(e.target.value)}
            >
              <MenuItem value="7days">Siku 7</MenuItem>
              <MenuItem value="30days">Siku 30</MenuItem>
              <MenuItem value="90days">Siku 90</MenuItem>
              <MenuItem value="1year">Mwaka 1</MenuItem>
            </Select>
          </FormControl>
          <Button variant="contained" startIcon={<Download />}>
            Pakua Ripoti
          </Button>
        </Box>
      </Box>

      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Watumiaji Wote
                  </Typography>
                  <Typography variant="h4" color="primary.main">
                    125,430
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    +12.5% wiki hii
                  </Typography>
                </Box>
                <People color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Mapato ya Mwezi
                  </Typography>
                  <Typography variant="h4" color="success.main">
                    {formatCurrency(5200000)}
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    +18.3% mwezi uliopita
                  </Typography>
                </Box>
                <AccountBalance color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Matukio ya Mwezi
                  </Typography>
                  <Typography variant="h4" color="warning.main">
                    1,250
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    +8.7% mwezi uliopita
                  </Typography>
                </Box>
                <Event color="warning" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Ukuaji wa Kila Siku
                  </Typography>
                  <Typography variant="h4" color="info.main">
                    +234
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    Watumiaji wapya leo
                  </Typography>
                </Box>
                <TrendingUp color="info" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts Row 1 */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {/* User Growth Chart */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Ukuaji wa Watumiaji
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={userGrowthData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip 
                  formatter={(value, name) => [
                    value.toLocaleString(),
                    name === 'users' ? 'Jumla ya Watumiaji' : 'Watumiaji Wapya'
                  ]}
                />
                <Line type="monotone" dataKey="users" stroke="#007AFF" strokeWidth={3} />
                <Line type="monotone" dataKey="newUsers" stroke="#4CAF50" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Platform Usage */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Matumizi ya Mifumo
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={platformUsageData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {platformUsageData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>

      {/* Charts Row 2 */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {/* Revenue Chart */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Mwelekeo wa Mapato
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={revenueData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip 
                  formatter={(value, name) => [
                    name === 'revenue' ? formatCurrency(value) : value.toLocaleString(),
                    name === 'revenue' ? 'Mapato' : 'Miamala'
                  ]}
                />
                <Bar dataKey="revenue" fill="#4CAF50" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Content Types */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Aina za Maudhui
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={contentTypeData} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="type" type="category" width={80} />
                <Tooltip />
                <Bar dataKey="count" fill="#007AFF" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>

      {/* Performance Metrics */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Utendaji wa Mfumo
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Muda wa Kujibu
                  </Typography>
                  <Typography variant="h6" color="success.main">
                    245ms
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Uptime
                  </Typography>
                  <Typography variant="h6" color="success.main">
                    99.9%
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Makosa ya API
                  </Typography>
                  <Typography variant="h6" color="error.main">
                    0.1%
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Watumiaji Hai
                  </Typography>
                  <Typography variant="h6" color="info.main">
                    12,450
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Takwimu za Biashara
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Kiwango cha Kubadilisha
                  </Typography>
                  <Typography variant="h6" color="success.main">
                    3.2%
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Wastani wa Muamala
                  </Typography>
                  <Typography variant="h6" color="primary.main">
                    {formatCurrency(25000)}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Watumiaji Wanaorudi
                  </Typography>
                  <Typography variant="h6" color="info.main">
                    68%
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Muda wa Kutumia
                  </Typography>
                  <Typography variant="h6" color="warning.main">
                    24min
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}
