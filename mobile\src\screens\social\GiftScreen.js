import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Alert,
  Modal,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors, typography, spacing } from '../../theme/theme';
import { giftsAPI, walletAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

export default function GiftScreen({ navigation, route }) {
  const { recipientId, recipientName, postId } = route.params;
  const { user } = useAuth();
  const [gifts, setGifts] = useState([]);
  const [selectedGift, setSelectedGift] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  useEffect(() => {
    loadGifts();
  }, []);

  const loadGifts = async () => {
    try {
      const response = await giftsAPI.getAvailableGifts();
      if (response.success) {
        setGifts(response.data);
      }
    } catch (error) {
      console.error('Error loading gifts:', error);
      Alert.alert('Hitilafu', 'Imeshindwa kupakia zawadi');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const calculateTotal = () => {
    if (!selectedGift) return 0;
    return selectedGift.price * quantity;
  };

  const calculateRecipientAmount = () => {
    if (!selectedGift) return 0;
    const total = calculateTotal();
    return total * (selectedGift.recipientPercentage / 100);
  };

  const handleGiftSelect = (gift) => {
    setSelectedGift(gift);
    setQuantity(1);
    setShowConfirmModal(true);
  };

  const handleSendGift = async () => {
    if (!selectedGift) return;

    try {
      setSending(true);
      
      const response = await giftsAPI.sendGift({
        giftId: selectedGift.id,
        recipientId: recipientId,
        quantity: quantity,
        message: message,
        postId: postId || null
      });

      if (response.success) {
        Alert.alert(
          'Mafanikio!',
          `Zawadi imetumwa kwa ${recipientName}!\n\nAnapokea: ${formatCurrency(calculateRecipientAmount())}`,
          [
            {
              text: 'Sawa',
              onPress: () => {
                setShowConfirmModal(false);
                navigation.goBack();
              }
            }
          ]
        );
      } else {
        Alert.alert('Hitilafu', response.message || 'Imeshindwa kutuma zawadi');
      }
    } catch (error) {
      console.error('Send gift error:', error);
      Alert.alert('Hitilafu', error.message || 'Hitilafu ya mtandao');
    } finally {
      setSending(false);
    }
  };

  const renderGiftItem = ({ item }) => (
    <TouchableOpacity
      style={styles.giftItem}
      onPress={() => handleGiftSelect(item)}
    >
      <View style={styles.giftIcon}>
        <Text style={styles.giftEmoji}>{item.emoji || '🎁'}</Text>
      </View>
      
      <View style={styles.giftInfo}>
        <Text style={styles.giftName}>{item.displayName}</Text>
        <Text style={styles.giftPrice}>{formatCurrency(item.price)}</Text>
        <Text style={styles.giftCategory}>{item.category}</Text>
      </View>
      
      {item.isPremium && (
        <View style={styles.premiumBadge}>
          <Icon name="star" size={16} color={colors.warning} />
        </View>
      )}
    </TouchableOpacity>
  );

  const renderGiftsByCategory = () => {
    const categories = [...new Set(gifts.map(gift => gift.category))];
    
    return categories.map(category => {
      const categoryGifts = gifts.filter(gift => gift.category === category);
      
      return (
        <View key={category} style={styles.categorySection}>
          <Text style={styles.categoryTitle}>
            {getCategoryDisplayName(category)}
          </Text>
          <FlatList
            data={categoryGifts}
            renderItem={renderGiftItem}
            keyExtractor={(item) => item.id.toString()}
            numColumns={2}
            scrollEnabled={false}
          />
        </View>
      );
    });
  };

  const getCategoryDisplayName = (category) => {
    switch (category) {
      case 'BASIC': return 'Zawadi za Msingi';
      case 'FLOWERS': return 'Maua';
      case 'JEWELRY': return 'Vito';
      case 'VEHICLES': return 'Magari';
      case 'ANIMALS': return 'Wanyamapori';
      case 'SPECIAL': return 'Maalum';
      case 'SEASONAL': return 'Msimu';
      default: return category;
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Inapakia zawadi...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Tuma Zawadi</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Recipient Info */}
      <View style={styles.recipientInfo}>
        <Icon name="person" size={24} color={colors.primary} />
        <Text style={styles.recipientText}>
          Kutuma zawadi kwa <Text style={styles.recipientName}>{recipientName}</Text>
        </Text>
      </View>

      {/* Gifts List */}
      <FlatList
        data={gifts}
        renderItem={renderGiftItem}
        keyExtractor={(item) => item.id.toString()}
        numColumns={2}
        contentContainerStyle={styles.giftsList}
        showsVerticalScrollIndicator={false}
      />

      {/* Confirmation Modal */}
      <Modal
        visible={showConfirmModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowConfirmModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Thibitisha Zawadi</Text>
            
            {selectedGift && (
              <>
                <View style={styles.selectedGiftInfo}>
                  <Text style={styles.selectedGiftEmoji}>
                    {selectedGift.emoji || '🎁'}
                  </Text>
                  <Text style={styles.selectedGiftName}>
                    {selectedGift.displayName}
                  </Text>
                  <Text style={styles.selectedGiftPrice}>
                    {formatCurrency(selectedGift.price)}
                  </Text>
                </View>

                {/* Quantity Selector */}
                <View style={styles.quantitySection}>
                  <Text style={styles.quantityLabel}>Idadi:</Text>
                  <View style={styles.quantityControls}>
                    <TouchableOpacity
                      style={styles.quantityButton}
                      onPress={() => setQuantity(Math.max(1, quantity - 1))}
                    >
                      <Icon name="remove" size={20} color={colors.primary} />
                    </TouchableOpacity>
                    <Text style={styles.quantityText}>{quantity}</Text>
                    <TouchableOpacity
                      style={styles.quantityButton}
                      onPress={() => setQuantity(quantity + 1)}
                    >
                      <Icon name="add" size={20} color={colors.primary} />
                    </TouchableOpacity>
                  </View>
                </View>

                {/* Message Input */}
                <View style={styles.messageSection}>
                  <Text style={styles.messageLabel}>Ujumbe (si lazima):</Text>
                  <TextInput
                    style={styles.messageInput}
                    placeholder="Andika ujumbe..."
                    value={message}
                    onChangeText={setMessage}
                    multiline
                    numberOfLines={3}
                  />
                </View>

                {/* Cost Breakdown */}
                <View style={styles.costBreakdown}>
                  <View style={styles.costRow}>
                    <Text style={styles.costLabel}>Jumla ya malipo:</Text>
                    <Text style={styles.costValue}>
                      {formatCurrency(calculateTotal())}
                    </Text>
                  </View>
                  <View style={styles.costRow}>
                    <Text style={styles.costLabel}>Anapokea:</Text>
                    <Text style={[styles.costValue, { color: colors.success }]}>
                      {formatCurrency(calculateRecipientAmount())}
                    </Text>
                  </View>
                </View>

                {/* Action Buttons */}
                <View style={styles.modalActions}>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={() => setShowConfirmModal(false)}
                  >
                    <Text style={styles.cancelButtonText}>Ghairi</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={styles.sendButton}
                    onPress={handleSendGift}
                    disabled={sending}
                  >
                    <Text style={styles.sendButtonText}>
                      {sending ? 'Inatuma...' : 'Tuma Zawadi'}
                    </Text>
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
  },
  placeholder: {
    width: 40,
  },
  recipientInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.primary + '10',
  },
  recipientText: {
    fontSize: typography.fontSize.md,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  recipientName: {
    fontWeight: typography.fontWeight.semiBold,
    color: colors.primary,
  },
  giftsList: {
    padding: spacing.lg,
  },
  giftItem: {
    flex: 1,
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.md,
    margin: spacing.sm,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
  },
  giftIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  giftEmoji: {
    fontSize: 32,
  },
  giftInfo: {
    alignItems: 'center',
  },
  giftName: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
  giftPrice: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary,
    marginBottom: spacing.xs,
  },
  giftCategory: {
    fontSize: typography.fontSize.xs,
    color: colors.textSecondary,
  },
  premiumBadge: {
    position: 'absolute',
    top: spacing.sm,
    right: spacing.sm,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.lg,
    width: '90%',
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  selectedGiftInfo: {
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  selectedGiftEmoji: {
    fontSize: 48,
    marginBottom: spacing.sm,
  },
  selectedGiftName: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  selectedGiftPrice: {
    fontSize: typography.fontSize.md,
    color: colors.primary,
    fontWeight: typography.fontWeight.bold,
  },
  quantitySection: {
    marginBottom: spacing.lg,
  },
  quantityLabel: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityText: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
    marginHorizontal: spacing.lg,
  },
  messageSection: {
    marginBottom: spacing.lg,
  },
  messageLabel: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  messageInput: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    padding: spacing.md,
    fontSize: typography.fontSize.md,
    color: colors.text,
    textAlignVertical: 'top',
  },
  costBreakdown: {
    backgroundColor: colors.surface,
    borderRadius: 8,
    padding: spacing.md,
    marginBottom: spacing.lg,
  },
  costRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  costLabel: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
  },
  costValue: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
  },
  modalActions: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: spacing.md,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
  },
  sendButton: {
    flex: 1,
    paddingVertical: spacing.md,
    borderRadius: 8,
    backgroundColor: colors.primary,
    alignItems: 'center',
  },
  sendButtonText: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.white,
  },
});
