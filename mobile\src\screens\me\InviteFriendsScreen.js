import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Share,
  Clipboard,
  Alert,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors, typography, spacing } from '../../theme/theme';
import { referralAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

export default function InviteFriendsScreen({ navigation }) {
  const { user } = useAuth();
  const [referralCode] = useState(user?.referralCode || 'PROCHAT123');
  const [referralStats, setReferralStats] = useState({
    totalReferrals: 0,
    totalEarnings: 0,
    pendingRewards: 0,
  });

  const referralLink = `https://prochat.app/invite/${referralCode}`;
  const shareMessage = `Jiunge na ProChat - platform ya kisasa ya mitandao ya kijamii na kifedha!\n\nTumia kodi yangu ya ualikaji: ${referralCode}\n\nPakua app: ${referralLink}\n\n🎁 Utapata bonus ya kuanza!`;

  const handleShare = async () => {
    try {
      await Share.share({
        message: shareMessage,
        url: referralLink,
        title: 'Jiunge na ProChat',
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  const handleCopyLink = () => {
    Clipboard.setString(referralLink);
    Alert.alert('Imekamilika', 'Kiungo kimehifadhiwa kwenye clipboard');
  };

  const handleCopyCode = () => {
    Clipboard.setString(referralCode);
    Alert.alert('Imekamilika', 'Kodi imehifadhiwa kwenye clipboard');
  };

  const shareOptions = [
    {
      name: 'WhatsApp',
      icon: 'message',
      color: colors.success,
      action: () => {
        // Implement WhatsApp sharing
        Alert.alert('WhatsApp', 'Fungua WhatsApp ili kushiriki');
      }
    },
    {
      name: 'SMS',
      icon: 'sms',
      color: colors.info,
      action: () => {
        // Implement SMS sharing
        Alert.alert('SMS', 'Fungua SMS ili kushiriki');
      }
    },
    {
      name: 'Email',
      icon: 'email',
      color: colors.warning,
      action: () => {
        // Implement Email sharing
        Alert.alert('Email', 'Fungua Email ili kushiriki');
      }
    },
    {
      name: 'Facebook',
      icon: 'facebook',
      color: '#1877F2',
      action: () => {
        // Implement Facebook sharing
        Alert.alert('Facebook', 'Fungua Facebook ili kushiriki');
      }
    },
    {
      name: 'Twitter',
      icon: 'alternate-email',
      color: '#1DA1F2',
      action: () => {
        // Implement Twitter sharing
        Alert.alert('Twitter', 'Fungua Twitter ili kushiriki');
      }
    },
    {
      name: 'Instagram',
      icon: 'camera-alt',
      color: '#E4405F',
      action: () => {
        // Implement Instagram sharing
        Alert.alert('Instagram', 'Fungua Instagram ili kushiriki');
      }
    }
  ];

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Alika Marafiki</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content}>
        {/* Hero Section */}
        <View style={styles.heroSection}>
          <Image
            source={require('../../assets/invite-friends.png')}
            style={styles.heroImage}
            resizeMode="contain"
          />
          <Text style={styles.heroTitle}>Alika Marafiki, Pata Tuzo!</Text>
          <Text style={styles.heroSubtitle}>
            Kila mrafiki anayejiunga kwa kutumia kodi yako, wewe na yeye mtapata bonus ya kuanza!
          </Text>
        </View>

        {/* Referral Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{referralStats.totalReferrals}</Text>
            <Text style={styles.statLabel}>Marafiki Waliojiunge</Text>
          </View>
          
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{referralStats.totalEarnings.toLocaleString()}</Text>
            <Text style={styles.statLabel}>Jumla ya Mapato (TZS)</Text>
          </View>
          
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{referralStats.pendingRewards.toLocaleString()}</Text>
            <Text style={styles.statLabel}>Tuzo Zinazosubiri (TZS)</Text>
          </View>
        </View>

        {/* Referral Code */}
        <View style={styles.codeSection}>
          <Text style={styles.sectionTitle}>Kodi Yako ya Ualikaji</Text>
          <View style={styles.codeContainer}>
            <Text style={styles.referralCode}>{referralCode}</Text>
            <TouchableOpacity style={styles.copyButton} onPress={handleCopyCode}>
              <Icon name="content-copy" size={20} color={colors.primary} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Referral Link */}
        <View style={styles.linkSection}>
          <Text style={styles.sectionTitle}>Kiungo cha Ualikaji</Text>
          <View style={styles.linkContainer}>
            <Text style={styles.referralLink} numberOfLines={2}>
              {referralLink}
            </Text>
            <TouchableOpacity style={styles.copyButton} onPress={handleCopyLink}>
              <Icon name="content-copy" size={20} color={colors.primary} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Share Options */}
        <View style={styles.shareSection}>
          <Text style={styles.sectionTitle}>Shiriki Kupitia</Text>
          <View style={styles.shareOptions}>
            {shareOptions.map((option, index) => (
              <TouchableOpacity
                key={index}
                style={styles.shareOption}
                onPress={option.action}
              >
                <View style={[styles.shareIcon, { backgroundColor: option.color + '20' }]}>
                  <Icon name={option.icon} size={24} color={option.color} />
                </View>
                <Text style={styles.shareOptionText}>{option.name}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Quick Share Button */}
        <TouchableOpacity style={styles.shareButton} onPress={handleShare}>
          <Icon name="share" size={20} color={colors.white} />
          <Text style={styles.shareButtonText}>Shiriki Sasa</Text>
        </TouchableOpacity>

        {/* How it Works */}
        <View style={styles.howItWorksSection}>
          <Text style={styles.sectionTitle}>Jinsi Inavyofanya Kazi</Text>
          
          <View style={styles.stepItem}>
            <View style={styles.stepNumber}>
              <Text style={styles.stepNumberText}>1</Text>
            </View>
            <View style={styles.stepContent}>
              <Text style={styles.stepTitle}>Shiriki Kodi Yako</Text>
              <Text style={styles.stepDescription}>
                Tumia kodi yako ya ualikaji au kiungo kushiriki na marafiki
              </Text>
            </View>
          </View>

          <View style={styles.stepItem}>
            <View style={styles.stepNumber}>
              <Text style={styles.stepNumberText}>2</Text>
            </View>
            <View style={styles.stepContent}>
              <Text style={styles.stepTitle}>Mrafiki Ajisajili</Text>
              <Text style={styles.stepDescription}>
                Mrafiki wako atapakua app na kujisajili kwa kutumia kodi yako
              </Text>
            </View>
          </View>

          <View style={styles.stepItem}>
            <View style={styles.stepNumber}>
              <Text style={styles.stepNumberText}>3</Text>
            </View>
            <View style={styles.stepContent}>
              <Text style={styles.stepTitle}>Mpate Tuzo Wote</Text>
              <Text style={styles.stepDescription}>
                Wewe utapata 10,000 TZS na mrafiki wako 5,000 TZS kama bonus
              </Text>
            </View>
          </View>
        </View>

        {/* Terms */}
        <View style={styles.termsSection}>
          <Text style={styles.termsTitle}>Masharti</Text>
          <Text style={styles.termsText}>
            • Mrafiki lazima ajisajili kwa kutumia kodi yako ya ualikaji{'\n'}
            • Tuzo itapokewa baada ya mrafiki kuthibitisha akaunti yake{'\n'}
            • Kila mtu anaweza kualikwa mara moja tu{'\n'}
            • Tuzo zitaongezwa kwenye ProPay wallet yako
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  heroSection: {
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.xl,
    backgroundColor: colors.primary + '10',
  },
  heroImage: {
    width: 200,
    height: 150,
    marginBottom: spacing.lg,
  },
  heroTitle: {
    fontSize: typography.fontSize.xxl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  heroSubtitle: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    paddingVertical: spacing.lg,
    marginTop: spacing.sm,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary,
  },
  statLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: spacing.xs,
  },
  codeSection: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginTop: spacing.sm,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  codeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    borderRadius: 8,
    padding: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
  },
  referralCode: {
    flex: 1,
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary,
    letterSpacing: 2,
  },
  copyButton: {
    padding: spacing.sm,
  },
  linkSection: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginTop: spacing.sm,
  },
  linkContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    borderRadius: 8,
    padding: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
  },
  referralLink: {
    flex: 1,
    fontSize: typography.fontSize.sm,
    color: colors.text,
  },
  shareSection: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginTop: spacing.sm,
  },
  shareOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  shareOption: {
    alignItems: 'center',
    width: '30%',
    marginBottom: spacing.lg,
  },
  shareIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  shareOptionText: {
    fontSize: typography.fontSize.sm,
    color: colors.text,
    textAlign: 'center',
  },
  shareButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.primary,
    marginHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
    borderRadius: 8,
    marginTop: spacing.lg,
  },
  shareButtonText: {
    color: colors.white,
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    marginLeft: spacing.sm,
  },
  howItWorksSection: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginTop: spacing.sm,
  },
  stepItem: {
    flexDirection: 'row',
    marginBottom: spacing.lg,
  },
  stepNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  stepNumberText: {
    color: colors.white,
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.bold,
  },
  stepContent: {
    flex: 1,
  },
  stepTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  stepDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  termsSection: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginTop: spacing.sm,
    marginBottom: spacing.xl,
  },
  termsTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  termsText: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    lineHeight: 20,
  },
});
