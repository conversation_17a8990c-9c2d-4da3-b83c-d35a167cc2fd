{"ast": null, "code": "export var ifOverflowMatches = function ifOverflowMatches(props, value) {\n  var alwaysShow = props.alwaysShow;\n  var ifOverflow = props.ifOverflow;\n  if (alwaysShow) {\n    ifOverflow = 'extendDomain';\n  }\n  return ifOverflow === value;\n};", "map": {"version": 3, "names": ["ifOverflowMatches", "props", "value", "alwaysShow", "ifOverflow"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/recharts/es6/util/IfOverflowMatches.js"], "sourcesContent": ["export var ifOverflowMatches = function ifOverflowMatches(props, value) {\n  var alwaysShow = props.alwaysShow;\n  var ifOverflow = props.ifOverflow;\n  if (alwaysShow) {\n    ifOverflow = 'extendDomain';\n  }\n  return ifOverflow === value;\n};"], "mappings": "AAAA,OAAO,IAAIA,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAEC,KAAK,EAAE;EACtE,IAAIC,UAAU,GAAGF,KAAK,CAACE,UAAU;EACjC,IAAIC,UAAU,GAAGH,KAAK,CAACG,UAAU;EACjC,IAAID,UAAU,EAAE;IACdC,UAAU,GAAG,cAAc;EAC7B;EACA,OAAOA,UAAU,KAAKF,KAAK;AAC7B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}