{"ast": null, "code": "export default function ascending(a, b) {\n  return a == null || b == null ? NaN : a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}", "map": {"version": 3, "names": ["ascending", "a", "b", "NaN"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/d3-array/src/ascending.js"], "sourcesContent": ["export default function ascending(a, b) {\n  return a == null || b == null ? NaN : a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\n"], "mappings": "AAAA,eAAe,SAASA,SAASA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACtC,OAAOD,CAAC,IAAI,IAAI,IAAIC,CAAC,IAAI,IAAI,GAAGC,GAAG,GAAGF,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAGD,CAAC,IAAIC,CAAC,GAAG,CAAC,GAAGC,GAAG;AACjF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}