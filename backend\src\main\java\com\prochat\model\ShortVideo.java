package com.prochat.model;

import jakarta.persistence.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "short_videos")
@EntityListeners(AuditingEntityListener.class)
public class ShortVideo {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "creator_id", nullable = false)
    private User creator;
    
    @Column(name = "title")
    private String title;
    
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    @Column(name = "video_url", nullable = false)
    private String videoUrl;
    
    @Column(name = "thumbnail_url")
    private String thumbnailUrl;
    
    @Column(name = "duration_seconds")
    private Integer durationSeconds;
    
    @Column(name = "file_size_bytes")
    private Long fileSizeBytes;
    
    @Column(name = "resolution")
    private String resolution; // e.g., "1080x1920"
    
    @Enumerated(EnumType.STRING)
    @Column(name = "category")
    private VideoCategory category;
    
    @Column(name = "hashtags")
    private String hashtags; // Comma-separated
    
    @Column(name = "music_url")
    private String musicUrl;
    
    @Column(name = "music_title")
    private String musicTitle;
    
    @Column(name = "music_artist")
    private String musicArtist;
    
    @Column(name = "location")
    private String location;
    
    @Column(name = "latitude")
    private Double latitude;
    
    @Column(name = "longitude")
    private Double longitude;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "privacy_setting")
    private VideoPrivacy privacySetting = VideoPrivacy.PUBLIC;
    
    @Column(name = "allows_comments")
    private Boolean allowsComments = true;
    
    @Column(name = "allows_duet")
    private Boolean allowsDuet = true;
    
    @Column(name = "allows_download")
    private Boolean allowsDownload = true;
    
    @Column(name = "is_monetized")
    private Boolean isMonetized = false;
    
    @Column(name = "views_count")
    private Long viewsCount = 0L;
    
    @Column(name = "likes_count")
    private Long likesCount = 0L;
    
    @Column(name = "comments_count")
    private Long commentsCount = 0L;
    
    @Column(name = "shares_count")
    private Long sharesCount = 0L;
    
    @Column(name = "downloads_count")
    private Long downloadsCount = 0L;
    
    @Column(name = "gifts_count")
    private Long giftsCount = 0L;
    
    @Column(name = "total_gifts_value", precision = 15, scale = 2)
    private BigDecimal totalGiftsValue = BigDecimal.ZERO;
    
    @Column(name = "total_watch_time_seconds")
    private Long totalWatchTimeSeconds = 0L;
    
    @Column(name = "average_watch_percentage", precision = 5, scale = 2)
    private BigDecimal averageWatchPercentage = BigDecimal.ZERO;
    
    @Column(name = "is_featured")
    private Boolean isFeatured = false;
    
    @Column(name = "is_trending")
    private Boolean isTrending = false;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private VideoStatus status = VideoStatus.PROCESSING;
    
    @Column(name = "moderation_status")
    private String moderationStatus = "PENDING"; // PENDING, APPROVED, REJECTED
    
    @Column(name = "rejection_reason")
    private String rejectionReason;
    
    @Column(name = "age_restriction")
    private String ageRestriction = "ALL"; // ALL, 13+, 16+, 18+
    
    @Column(name = "language")
    private String language = "sw";
    
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Constructors
    public ShortVideo() {}
    
    public ShortVideo(User creator, String videoUrl, Integer durationSeconds) {
        this.creator = creator;
        this.videoUrl = videoUrl;
        this.durationSeconds = durationSeconds;
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public User getCreator() { return creator; }
    public void setCreator(User creator) { this.creator = creator; }
    
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getVideoUrl() { return videoUrl; }
    public void setVideoUrl(String videoUrl) { this.videoUrl = videoUrl; }
    
    public String getThumbnailUrl() { return thumbnailUrl; }
    public void setThumbnailUrl(String thumbnailUrl) { this.thumbnailUrl = thumbnailUrl; }
    
    public Integer getDurationSeconds() { return durationSeconds; }
    public void setDurationSeconds(Integer durationSeconds) { this.durationSeconds = durationSeconds; }
    
    public Long getFileSizeBytes() { return fileSizeBytes; }
    public void setFileSizeBytes(Long fileSizeBytes) { this.fileSizeBytes = fileSizeBytes; }
    
    public String getResolution() { return resolution; }
    public void setResolution(String resolution) { this.resolution = resolution; }
    
    public VideoCategory getCategory() { return category; }
    public void setCategory(VideoCategory category) { this.category = category; }
    
    public String getHashtags() { return hashtags; }
    public void setHashtags(String hashtags) { this.hashtags = hashtags; }
    
    public String getMusicUrl() { return musicUrl; }
    public void setMusicUrl(String musicUrl) { this.musicUrl = musicUrl; }
    
    public String getMusicTitle() { return musicTitle; }
    public void setMusicTitle(String musicTitle) { this.musicTitle = musicTitle; }
    
    public String getMusicArtist() { return musicArtist; }
    public void setMusicArtist(String musicArtist) { this.musicArtist = musicArtist; }
    
    public String getLocation() { return location; }
    public void setLocation(String location) { this.location = location; }
    
    public Double getLatitude() { return latitude; }
    public void setLatitude(Double latitude) { this.latitude = latitude; }
    
    public Double getLongitude() { return longitude; }
    public void setLongitude(Double longitude) { this.longitude = longitude; }
    
    public VideoPrivacy getPrivacySetting() { return privacySetting; }
    public void setPrivacySetting(VideoPrivacy privacySetting) { this.privacySetting = privacySetting; }
    
    public Boolean getAllowsComments() { return allowsComments; }
    public void setAllowsComments(Boolean allowsComments) { this.allowsComments = allowsComments; }
    
    public Boolean getAllowsDuet() { return allowsDuet; }
    public void setAllowsDuet(Boolean allowsDuet) { this.allowsDuet = allowsDuet; }
    
    public Boolean getAllowsDownload() { return allowsDownload; }
    public void setAllowsDownload(Boolean allowsDownload) { this.allowsDownload = allowsDownload; }
    
    public Boolean getIsMonetized() { return isMonetized; }
    public void setIsMonetized(Boolean isMonetized) { this.isMonetized = isMonetized; }
    
    public Long getViewsCount() { return viewsCount; }
    public void setViewsCount(Long viewsCount) { this.viewsCount = viewsCount; }
    
    public Long getLikesCount() { return likesCount; }
    public void setLikesCount(Long likesCount) { this.likesCount = likesCount; }
    
    public Long getCommentsCount() { return commentsCount; }
    public void setCommentsCount(Long commentsCount) { this.commentsCount = commentsCount; }
    
    public Long getSharesCount() { return sharesCount; }
    public void setSharesCount(Long sharesCount) { this.sharesCount = sharesCount; }
    
    public Long getDownloadsCount() { return downloadsCount; }
    public void setDownloadsCount(Long downloadsCount) { this.downloadsCount = downloadsCount; }
    
    public Long getGiftsCount() { return giftsCount; }
    public void setGiftsCount(Long giftsCount) { this.giftsCount = giftsCount; }
    
    public BigDecimal getTotalGiftsValue() { return totalGiftsValue; }
    public void setTotalGiftsValue(BigDecimal totalGiftsValue) { this.totalGiftsValue = totalGiftsValue; }
    
    public Long getTotalWatchTimeSeconds() { return totalWatchTimeSeconds; }
    public void setTotalWatchTimeSeconds(Long totalWatchTimeSeconds) { this.totalWatchTimeSeconds = totalWatchTimeSeconds; }
    
    public BigDecimal getAverageWatchPercentage() { return averageWatchPercentage; }
    public void setAverageWatchPercentage(BigDecimal averageWatchPercentage) { this.averageWatchPercentage = averageWatchPercentage; }
    
    public Boolean getIsFeatured() { return isFeatured; }
    public void setIsFeatured(Boolean isFeatured) { this.isFeatured = isFeatured; }
    
    public Boolean getIsTrending() { return isTrending; }
    public void setIsTrending(Boolean isTrending) { this.isTrending = isTrending; }
    
    public VideoStatus getStatus() { return status; }
    public void setStatus(VideoStatus status) { this.status = status; }
    
    public String getModerationStatus() { return moderationStatus; }
    public void setModerationStatus(String moderationStatus) { this.moderationStatus = moderationStatus; }
    
    public String getRejectionReason() { return rejectionReason; }
    public void setRejectionReason(String rejectionReason) { this.rejectionReason = rejectionReason; }
    
    public String getAgeRestriction() { return ageRestriction; }
    public void setAgeRestriction(String ageRestriction) { this.ageRestriction = ageRestriction; }
    
    public String getLanguage() { return language; }
    public void setLanguage(String language) { this.language = language; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
