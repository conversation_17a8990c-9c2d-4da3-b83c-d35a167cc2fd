import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors, typography, spacing } from '../../theme/theme';
import { proZoneAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

export default function ProZoneScreen({ navigation }) {
  const { user } = useAuth();
  const [agentData, setAgentData] = useState(null);
  const [stats, setStats] = useState({
    totalEarnings: 0,
    monthlyEarnings: 0,
    totalCustomers: 0,
    activeServices: 0,
  });
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadProZoneData();
  }, []);

  const loadProZoneData = async () => {
    try {
      const [agentResponse, statsResponse] = await Promise.all([
        proZoneAPI.getAgentProfile(),
        proZoneAPI.getAgentStats()
      ]);

      if (agentResponse.success) {
        setAgentData(agentResponse.data);
      }

      if (statsResponse.success) {
        setStats(statsResponse.data);
      }
    } catch (error) {
      console.error('Error loading ProZone data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadProZoneData();
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const handleBecomeAgent = () => {
    navigation.navigate('BecomeAgent');
  };

  const services = [
    {
      title: 'Huduma za Kifedha',
      icon: 'account-balance',
      description: 'Toa huduma za ProPay kwa wateja',
      earnings: '5% kwa muamala',
      color: colors.primary,
    },
    {
      title: 'Uuzaji wa Tiketi',
      icon: 'confirmation-number',
      description: 'Uza tiketi za matukio',
      earnings: '10% kwa tiketi',
      color: colors.success,
    },
    {
      title: 'Uongozaji wa Biashara',
      icon: 'business',
      description: 'Saidia biashara kuongeza mauzo',
      earnings: '15% kwa mauzo',
      color: colors.warning,
    },
    {
      title: 'Mafunzo ya Teknolojia',
      icon: 'school',
      description: 'Fundisha watu kutumia ProChat',
      earnings: '20,000 TZS kwa mtu',
      color: colors.info,
    },
  ];

  const quickActions = [
    {
      title: 'Wateja Wangu',
      icon: 'people',
      onPress: () => navigation.navigate('MyCustomers'),
    },
    {
      title: 'Miamala',
      icon: 'receipt',
      onPress: () => navigation.navigate('AgentTransactions'),
    },
    {
      title: 'Mapato',
      icon: 'trending-up',
      onPress: () => navigation.navigate('AgentEarnings'),
    },
    {
      title: 'Mafunzo',
      icon: 'school',
      onPress: () => navigation.navigate('AgentTraining'),
    },
  ];

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Inapakia ProZone...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // If user is not an agent
  if (!agentData) {
    return (
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>ProZone</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content}>
          {/* Welcome Section */}
          <View style={styles.welcomeSection}>
            <Icon name="business-center" size={64} color={colors.primary} />
            <Text style={styles.welcomeTitle}>Karibu ProZone</Text>
            <Text style={styles.welcomeSubtitle}>
              Jiunge na timu ya mawakala wa ProChat na uanze kupata mapato
            </Text>
          </View>

          {/* Benefits */}
          <View style={styles.benefitsSection}>
            <Text style={styles.sectionTitle}>Faida za Kuwa Wakala</Text>
            
            <View style={styles.benefit}>
              <Icon name="monetization-on" size={24} color={colors.success} />
              <Text style={styles.benefitText}>
                Pata mapato ya ziada kwa kutoa huduma za ProChat
              </Text>
            </View>

            <View style={styles.benefit}>
              <Icon name="schedule" size={24} color={colors.primary} />
              <Text style={styles.benefitText}>
                Fanya kazi wakati wako mwenyewe
              </Text>
            </View>

            <View style={styles.benefit}>
              <Icon name="support" size={24} color={colors.info} />
              <Text style={styles.benefitText}>
                Pata msaada na mafunzo ya bure
              </Text>
            </View>

            <View style={styles.benefit}>
              <Icon name="trending-up" size={24} color={colors.warning} />
              <Text style={styles.benefitText}>
                Kua sehemu ya biashara inayokua haraka
              </Text>
            </View>
          </View>

          {/* Services */}
          <View style={styles.servicesSection}>
            <Text style={styles.sectionTitle}>Huduma Unazoweza Kutoa</Text>
            {services.map((service, index) => (
              <View key={index} style={styles.serviceItem}>
                <View style={[
                  styles.serviceIcon,
                  { backgroundColor: service.color + '20' }
                ]}>
                  <Icon name={service.icon} size={24} color={service.color} />
                </View>
                <View style={styles.serviceContent}>
                  <Text style={styles.serviceTitle}>{service.title}</Text>
                  <Text style={styles.serviceDescription}>{service.description}</Text>
                  <Text style={styles.serviceEarnings}>Mapato: {service.earnings}</Text>
                </View>
              </View>
            ))}
          </View>

          {/* CTA Button */}
          <TouchableOpacity style={styles.becomeAgentButton} onPress={handleBecomeAgent}>
            <Icon name="business-center" size={20} color={colors.white} />
            <Text style={styles.becomeAgentText}>Jiunge Sasa</Text>
          </TouchableOpacity>
        </ScrollView>
      </SafeAreaView>
    );
  }

  // If user is an agent
  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.agentHeader}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color={colors.white} />
        </TouchableOpacity>
        <Text style={styles.agentHeaderTitle}>ProZone Dashboard</Text>
        <TouchableOpacity style={styles.notificationButton}>
          <Icon name="notifications" size={24} color={colors.white} />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Agent Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{formatCurrency(stats.totalEarnings)}</Text>
            <Text style={styles.statLabel}>Jumla ya Mapato</Text>
          </View>
          
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{formatCurrency(stats.monthlyEarnings)}</Text>
            <Text style={styles.statLabel}>Mapato ya Mwezi</Text>
          </View>
          
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{stats.totalCustomers}</Text>
            <Text style={styles.statLabel}>Wateja</Text>
          </View>
          
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{stats.activeServices}</Text>
            <Text style={styles.statLabel}>Huduma Hai</Text>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActionsSection}>
          <Text style={styles.sectionTitle}>Vitendo vya Haraka</Text>
          <View style={styles.quickActionsGrid}>
            {quickActions.map((action, index) => (
              <TouchableOpacity
                key={index}
                style={styles.quickActionItem}
                onPress={action.onPress}
              >
                <Icon name={action.icon} size={32} color={colors.primary} />
                <Text style={styles.quickActionText}>{action.title}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Agent Level */}
        <View style={styles.levelSection}>
          <Text style={styles.sectionTitle}>Kiwango cha Uwakala</Text>
          <View style={styles.levelCard}>
            <View style={styles.levelInfo}>
              <Text style={styles.levelTitle}>{agentData.level || 'Bronze'}</Text>
              <Text style={styles.levelDescription}>
                Kiwango chako cha sasa cha uwakala
              </Text>
            </View>
            <View style={styles.levelProgress}>
              <View style={styles.progressBar}>
                <View 
                  style={[
                    styles.progressFill,
                    { width: `${agentData.levelProgress || 60}%` }
                  ]} 
                />
              </View>
              <Text style={styles.progressText}>
                {agentData.levelProgress || 60}% hadi kiwango kijacho
              </Text>
            </View>
          </View>
        </View>

        {/* Recent Activity */}
        <View style={styles.activitySection}>
          <Text style={styles.sectionTitle}>Shughuli za Hivi Karibuni</Text>
          {agentData.recentActivity?.length > 0 ? (
            agentData.recentActivity.map((activity, index) => (
              <View key={index} style={styles.activityItem}>
                <Icon name="circle" size={8} color={colors.primary} />
                <Text style={styles.activityText}>{activity.description}</Text>
                <Text style={styles.activityTime}>
                  {new Date(activity.timestamp).toLocaleDateString('sw-TZ')}
                </Text>
              </View>
            ))
          ) : (
            <View style={styles.emptyActivity}>
              <Icon name="history" size={48} color={colors.gray} />
              <Text style={styles.emptyText}>Hakuna shughuli bado</Text>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  agentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
    backgroundColor: colors.primary,
  },
  backButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
  },
  agentHeaderTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.white,
  },
  notificationButton: {
    padding: spacing.sm,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  welcomeSection: {
    alignItems: 'center',
    paddingVertical: spacing.xxxl,
    paddingHorizontal: spacing.lg,
  },
  welcomeTitle: {
    fontSize: typography.fontSize.xxl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  welcomeSubtitle: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  benefitsSection: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginBottom: spacing.sm,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  benefit: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  benefitText: {
    fontSize: typography.fontSize.md,
    color: colors.text,
    marginLeft: spacing.md,
    flex: 1,
  },
  servicesSection: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginBottom: spacing.sm,
  },
  serviceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  serviceIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  serviceContent: {
    flex: 1,
  },
  serviceTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  serviceDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  serviceEarnings: {
    fontSize: typography.fontSize.sm,
    color: colors.success,
    fontWeight: typography.fontWeight.medium,
  },
  becomeAgentButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.primary,
    marginHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
    borderRadius: 8,
    marginBottom: spacing.xl,
  },
  becomeAgentText: {
    color: colors.white,
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    marginLeft: spacing.sm,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginBottom: spacing.sm,
  },
  statItem: {
    width: '50%',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  statNumber: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary,
  },
  statLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: spacing.xs,
  },
  quickActionsSection: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginBottom: spacing.sm,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionItem: {
    width: '48%',
    alignItems: 'center',
    padding: spacing.md,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    marginBottom: spacing.md,
  },
  quickActionText: {
    fontSize: typography.fontSize.sm,
    color: colors.text,
    marginTop: spacing.sm,
    textAlign: 'center',
  },
  levelSection: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginBottom: spacing.sm,
  },
  levelCard: {
    backgroundColor: colors.surface,
    padding: spacing.md,
    borderRadius: 8,
  },
  levelInfo: {
    marginBottom: spacing.md,
  },
  levelTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary,
    marginBottom: spacing.xs,
  },
  levelDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
  levelProgress: {
    marginTop: spacing.sm,
  },
  progressBar: {
    height: 8,
    backgroundColor: colors.border,
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: spacing.sm,
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
  },
  progressText: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
  activitySection: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginBottom: spacing.xl,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  activityText: {
    flex: 1,
    fontSize: typography.fontSize.sm,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  activityTime: {
    fontSize: typography.fontSize.xs,
    color: colors.textSecondary,
  },
  emptyActivity: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  emptyText: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
    marginTop: spacing.sm,
  },
});
