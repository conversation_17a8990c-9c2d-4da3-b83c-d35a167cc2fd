[{"E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\index.js": "1", "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\App.js": "2", "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\contexts\\AuthContext.js": "3", "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\pages\\Auth\\Login.js": "4", "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\pages\\Dashboard\\Dashboard.js": "5", "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\pages\\Content\\ContentModeration.js": "6", "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\components\\Layout\\Layout.js": "7", "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\pages\\Jobs\\JobManagement.js": "8", "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\pages\\Support\\SupportTickets.js": "9", "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\pages\\Users\\UsersManagement.js": "10", "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\pages\\Settings\\SystemSettings.js": "11", "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\pages\\Events\\EventManagement.js": "12", "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\pages\\Admin\\AdminManagement.js": "13", "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\pages\\Analytics\\Analytics.js": "14", "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\pages\\Finance\\FinancePanel.js": "15"}, {"size": 254, "mtime": 1748469796734, "results": "16", "hashOfConfig": "17"}, {"size": 5453, "mtime": 1748384955886, "results": "18", "hashOfConfig": "17"}, {"size": 4641, "mtime": 1748384988150, "results": "19", "hashOfConfig": "17"}, {"size": 7224, "mtime": 1748385026045, "results": "20", "hashOfConfig": "17"}, {"size": 8983, "mtime": 1748385125451, "results": "21", "hashOfConfig": "17"}, {"size": 15643, "mtime": 1748386799175, "results": "22", "hashOfConfig": "17"}, {"size": 8492, "mtime": 1748385070576, "results": "23", "hashOfConfig": "17"}, {"size": 9085, "mtime": 1748386960876, "results": "24", "hashOfConfig": "17"}, {"size": 11480, "mtime": 1748387008864, "results": "25", "hashOfConfig": "17"}, {"size": 12403, "mtime": 1748386730998, "results": "26", "hashOfConfig": "17"}, {"size": 11128, "mtime": 1748387150673, "results": "27", "hashOfConfig": "17"}, {"size": 7928, "mtime": 1748386922944, "results": "28", "hashOfConfig": "17"}, {"size": 9293, "mtime": 1748387048569, "results": "29", "hashOfConfig": "17"}, {"size": 13018, "mtime": 1748387101340, "results": "30", "hashOfConfig": "17"}, {"size": 21291, "mtime": 1748386887103, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1twr66k", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\index.js", [], [], "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\App.js", [], [], "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\contexts\\AuthContext.js", ["77"], [], "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\pages\\Auth\\Login.js", [], [], "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\pages\\Dashboard\\Dashboard.js", ["78", "79", "80", "81"], [], "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\pages\\Content\\ContentModeration.js", ["82", "83", "84"], [], "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\components\\Layout\\Layout.js", [], [], "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\pages\\Jobs\\JobManagement.js", ["85"], [], "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\pages\\Support\\SupportTickets.js", ["86"], [], "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\pages\\Users\\UsersManagement.js", [], [], "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\pages\\Settings\\SystemSettings.js", ["87", "88", "89"], [], "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\pages\\Events\\EventManagement.js", ["90"], [], "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\pages\\Admin\\AdminManagement.js", ["91"], [], "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\pages\\Analytics\\Analytics.js", [], [], "E:\\RamsTech\\App\\ProChat\\admin-panel\\src\\pages\\Finance\\FinancePanel.js", ["92", "93", "94", "95", "96", "97", "98"], [], {"ruleId": "99", "severity": 1, "message": "100", "line": 30, "column": 6, "nodeType": "101", "endLine": 30, "endColumn": 8, "suggestions": "102"}, {"ruleId": "103", "severity": 1, "message": "104", "line": 1, "column": 27, "nodeType": "105", "messageId": "106", "endLine": 1, "endColumn": 36}, {"ruleId": "103", "severity": 1, "message": "107", "line": 14, "column": 3, "nodeType": "105", "messageId": "106", "endLine": 14, "endColumn": 13}, {"ruleId": "103", "severity": 1, "message": "108", "line": 30, "column": 86, "nodeType": "105", "messageId": "106", "endLine": 30, "endColumn": 94}, {"ruleId": "103", "severity": 1, "message": "109", "line": 30, "column": 96, "nodeType": "105", "messageId": "106", "endLine": 30, "endColumn": 99}, {"ruleId": "103", "severity": 1, "message": "110", "line": 40, "column": 3, "nodeType": "105", "messageId": "106", "endLine": 40, "endColumn": 10}, {"ruleId": "103", "severity": 1, "message": "111", "line": 42, "column": 3, "nodeType": "105", "messageId": "106", "endLine": 42, "endColumn": 12}, {"ruleId": "99", "severity": 1, "message": "112", "line": 128, "column": 6, "nodeType": "101", "endLine": 128, "endColumn": 40, "suggestions": "113"}, {"ruleId": "103", "severity": 1, "message": "114", "line": 67, "column": 10, "nodeType": "105", "messageId": "106", "endLine": 67, "endColumn": 21}, {"ruleId": "103", "severity": 1, "message": "115", "line": 31, "column": 3, "nodeType": "105", "messageId": "106", "endLine": 31, "endColumn": 9}, {"ruleId": "103", "severity": 1, "message": "116", "line": 25, "column": 3, "nodeType": "105", "messageId": "106", "endLine": 25, "endColumn": 8}, {"ruleId": "103", "severity": 1, "message": "117", "line": 26, "column": 3, "nodeType": "105", "messageId": "106", "endLine": 26, "endColumn": 6}, {"ruleId": "118", "severity": 2, "message": "119", "line": 67, "column": 30, "nodeType": "120", "messageId": "121", "endLine": 67, "endColumn": 38}, {"ruleId": "103", "severity": 1, "message": "122", "line": 62, "column": 10, "nodeType": "105", "messageId": "106", "endLine": 62, "endColumn": 23}, {"ruleId": "103", "severity": 1, "message": "123", "line": 76, "column": 10, "nodeType": "105", "messageId": "106", "endLine": 76, "endColumn": 23}, {"ruleId": "103", "severity": 1, "message": "124", "line": 28, "column": 3, "nodeType": "105", "messageId": "106", "endLine": 28, "endColumn": 8}, {"ruleId": "103", "severity": 1, "message": "125", "line": 29, "column": 3, "nodeType": "105", "messageId": "106", "endLine": 29, "endColumn": 17}, {"ruleId": "103", "severity": 1, "message": "126", "line": 39, "column": 3, "nodeType": "105", "messageId": "106", "endLine": 39, "endColumn": 15}, {"ruleId": "103", "severity": 1, "message": "108", "line": 46, "column": 86, "nodeType": "105", "messageId": "106", "endLine": 46, "endColumn": 94}, {"ruleId": "103", "severity": 1, "message": "109", "line": 46, "column": 96, "nodeType": "105", "messageId": "106", "endLine": 46, "endColumn": 99}, {"ruleId": "103", "severity": 1, "message": "127", "line": 69, "column": 26, "nodeType": "105", "messageId": "106", "endLine": 69, "endColumn": 43}, {"ruleId": "99", "severity": 1, "message": "112", "line": 173, "column": 6, "nodeType": "101", "endLine": 173, "endColumn": 45, "suggestions": "128"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'verifyToken'. Either include it or remove the dependency array.", "ArrayExpression", ["129"], "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'IconButton' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'Warning' is defined but never used.", "'ThumbDown' is defined but never used.", "React Hook useEffect has a missing dependency: 'tabs'. Either include it or remove the dependency array.", ["130"], "'selectedJob' is assigned a value but never used.", "'Cancel' is defined but never used.", "'Email' is defined but never used.", "'Sms' is defined but never used.", "react/jsx-no-undef", "'Settings' is not defined.", "JSXIdentifier", "undefined", "'selectedEvent' is assigned a value but never used.", "'selectedAdmin' is assigned a value but never used.", "'Alert' is defined but never used.", "'LinearProgress' is defined but never used.", "'TrendingDown' is defined but never used.", "'setFinancialStats' is assigned a value but never used.", ["131"], {"desc": "132", "fix": "133"}, {"desc": "134", "fix": "135"}, {"desc": "136", "fix": "137"}, "Update the dependencies array to be: [verifyToken]", {"range": "138", "text": "139"}, "Update the dependencies array to be: [currentTab, searchQuery, content, tabs]", {"range": "140", "text": "141"}, "Update the dependencies array to be: [currentTab, searchQuery, tabs, transactions]", {"range": "142", "text": "143"}, [869, 871], "[verifyToken]", [3372, 3406], "[currentTab, searchQuery, content, tabs]", [4822, 4861], "[currentTab, searchQuery, tabs, transactions]"]