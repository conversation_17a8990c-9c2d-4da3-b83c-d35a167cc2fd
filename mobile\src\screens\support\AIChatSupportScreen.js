import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

export default function AIChatSupportScreen({ navigation }) {
  const [messages, setMessages] = useState([
    {
      id: '1',
      text: 'Ha<PERSON>! Mimi ni msaidizi wa ProChat. Ninaweza kukusaidia na maswali yoyote kuhusu app. Je, una swali gani?',
      sender: 'ai',
      timestamp: new Date().toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
      }),
      type: 'text',
    },
  ]);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const flatListRef = useRef(null);

  // Quick questions for easy access
  const quickQuestions = [
    '<PERSON><PERSON> ya kutuma pesa',
    '<PERSON><PERSON> ya kuweka PIN',
    '<PERSON><PERSON> ya kuongeza pesa',
    '<PERSON><PERSON> ya kufanya live stream',
    '<PERSON><PERSON> ya kutengeneza tiketi',
    'Jinsi ya kuwa wakala',
  ];

  useEffect(() => {
    // Scroll to bottom when messages change
    if (flatListRef.current && messages.length > 0) {
      flatListRef.current.scrollToEnd({ animated: true });
    }
  }, [messages]);

  const sendMessage = async (messageText = inputText) => {
    if (!messageText.trim()) return;

    const userMessage = {
      id: Date.now().toString(),
      text: messageText.trim(),
      sender: 'user',
      timestamp: new Date().toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
      }),
      type: 'text',
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsTyping(true);

    try {
      // Simulate AI response
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const aiResponse = generateAIResponse(messageText);
      
      const aiMessage = {
        id: (Date.now() + 1).toString(),
        text: aiResponse,
        sender: 'ai',
        timestamp: new Date().toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: false,
        }),
        type: 'text',
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      Alert.alert('Kosa', 'Imeshindikana kupata jibu. Jaribu tena.');
    } finally {
      setIsTyping(false);
    }
  };

  const generateAIResponse = (userMessage) => {
    const lowerMessage = userMessage.toLowerCase();
    
    // Simple rule-based responses (in production, this would use actual AI)
    if (lowerMessage.includes('pesa') || lowerMessage.includes('tuma') || lowerMessage.includes('propay')) {
      return `Ili kutuma pesa kwa ProPay:
1. Nenda kwenye tab ya "Me"
2. Bonyeza "ProPay Wallet"
3. Chagua "Tuma Pesa"
4. Ingiza namba ya wallet ya mpokeaji
5. Ingiza kiasi
6. Ingiza PIN yako
7. Thibitisha muamala

Je, una swali lingine?`;
    }
    
    if (lowerMessage.includes('pin') || lowerMessage.includes('nywila')) {
      return `Ili kuweka PIN ya ProPay:
1. Nenda kwenye "ProPay Wallet"
2. Bonyeza "Mipangilio"
3. Chagua "Weka PIN"
4. Ingiza PIN ya nambari 4
5. Thibitisha PIN

Kumbuka: PIN yako ni siri, usimshirikishe mtu yeyote!`;
    }
    
    if (lowerMessage.includes('weka') || lowerMessage.includes('deposit')) {
      return `Ili kuongeza pesa kwenye ProPay:
1. Bonyeza "Weka Pesa" kwenye wallet
2. Chagua njia ya malipo (Wakala, Bank, Mobile Money)
3. Ingiza kiasi
4. Fuata maagizo ya malipo
5. Subiri uthibitisho

Miamala inachukua dakika 1-5 kuthibitishwa.`;
    }
    
    if (lowerMessage.includes('live') || lowerMessage.includes('stream')) {
      return `Ili kuanza live stream:
1. Nenda kwenye tab ya "Discover"
2. Bonyeza "Go Live"
3. Weka jina la stream yako
4. Chagua category
5. Weka maelezo
6. Bonyeza "Anza Stream"

Unaweza kupata zawadi kutoka kwa waonaji!`;
    }
    
    if (lowerMessage.includes('tiketi') || lowerMessage.includes('event')) {
      return `Ili kutengeneza tiketi za event:
1. Nenda kwenye "Discover" > "Tickets"
2. Bonyeza "Tengeneza Event"
3. Jaza maelezo ya event
4. Weka bei ya tiketi
5. Ongeza picha
6. Chapisha event

Wateja wataweza kununua tiketi kwa ProPay!`;
    }
    
    if (lowerMessage.includes('wakala') || lowerMessage.includes('agent')) {
      return `Ili kuwa wakala wa ProPay:
1. Nenda kwenye "Me" > "ProZone"
2. Chagua "Kuwa Wakala"
3. Jaza fomu ya maombi
4. Pakia hati zinazohitajika
5. Subiri uthibitisho

Utapata commission kwa kila muamala!`;
    }
    
    if (lowerMessage.includes('usalama') || lowerMessage.includes('security')) {
      return `ProChat ina usalama wa hali ya juu:
- Mazungumzo yamefichwa kwa encryption
- PIN za wallet zimehifadhiwa kwa usalama
- Miamala inahakikiwa kwa AI
- Taarifa za kibinafsi zinalindwa

Usiwahi kushirikisha PIN au password yako!`;
    }
    
    if (lowerMessage.includes('asante') || lowerMessage.includes('shukran')) {
      return 'Karibu sana! Nimefurahi kukusaidia. Je, una swali lingine?';
    }
    
    // Default response
    return `Nimesikia swali lako kuhusu "${userMessage}". 

Hapa ni baadhi ya mada ninazoweza kukusaidia nazo:
• ProPay Wallet na miamala
• Live streaming na zawadi
• Tiketi za matukio
• Kuwa wakala wa ProPay
• Usalama na faragha
• Matumizi ya app

Je, unaweza kuniuliza swali maalum zaidi?`;
  };

  const renderMessage = ({ item, index }) => {
    const isAI = item.sender === 'ai';
    
    return (
      <View style={[
        styles.messageContainer,
        isAI ? styles.aiMessageContainer : styles.userMessageContainer,
      ]}>
        {isAI && (
          <View style={styles.aiAvatar}>
            <Ionicons name="chatbot" size={20} color="#007AFF" />
          </View>
        )}
        
        <View style={[
          styles.messageBubble,
          isAI ? styles.aiMessageBubble : styles.userMessageBubble,
        ]}>
          <Text style={[
            styles.messageText,
            isAI ? styles.aiMessageText : styles.userMessageText,
          ]}>
            {item.text}
          </Text>
          <Text style={[
            styles.messageTime,
            isAI ? styles.aiMessageTime : styles.userMessageTime,
          ]}>
            {item.timestamp}
          </Text>
        </View>
      </View>
    );
  };

  const renderQuickQuestion = (question) => (
    <TouchableOpacity
      key={question}
      style={styles.quickQuestionButton}
      onPress={() => sendMessage(question)}
    >
      <Text style={styles.quickQuestionText}>{question}</Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        
        <View style={styles.headerInfo}>
          <View style={styles.aiIndicator}>
            <Ionicons name="chatbot" size={24} color="#007AFF" />
          </View>
          <View>
            <Text style={styles.headerName}>AI Msaidizi</Text>
            <Text style={styles.headerStatus}>Uko mtandaoni • Jibu la haraka</Text>
          </View>
        </View>

        <TouchableOpacity style={styles.headerButton}>
          <Ionicons name="refresh" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      {/* Messages */}
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContent}
        showsVerticalScrollIndicator={false}
        onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: true })}
      />

      {/* Quick Questions */}
      {messages.length <= 1 && (
        <View style={styles.quickQuestionsContainer}>
          <Text style={styles.quickQuestionsTitle}>Maswali ya Haraka:</Text>
          <View style={styles.quickQuestionsGrid}>
            {quickQuestions.map(renderQuickQuestion)}
          </View>
        </View>
      )}

      {/* Typing Indicator */}
      {isTyping && (
        <View style={styles.typingContainer}>
          <View style={styles.aiAvatar}>
            <Ionicons name="chatbot" size={16} color="#007AFF" />
          </View>
          <View style={styles.typingBubble}>
            <Text style={styles.typingText}>AI msaidizi anaandika...</Text>
          </View>
        </View>
      )}

      {/* Input Area */}
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.inputContainer}
      >
        <View style={styles.inputRow}>
          <View style={styles.textInputContainer}>
            <TextInput
              style={styles.textInput}
              value={inputText}
              onChangeText={setInputText}
              placeholder="Andika swali lako hapa..."
              placeholderTextColor="#999"
              multiline
              maxLength={500}
            />
          </View>

          <TouchableOpacity
            style={[styles.sendButton, !inputText.trim() && styles.sendButtonDisabled]}
            onPress={() => sendMessage()}
            disabled={!inputText.trim() || isTyping}
          >
            <Ionicons name="send" size={20} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 10,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  backButton: {
    marginRight: 10,
  },
  headerInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  aiIndicator: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E3F2FD',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  headerName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  headerStatus: {
    fontSize: 12,
    color: '#4CAF50',
    marginTop: 2,
  },
  headerButton: {
    marginLeft: 10,
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingVertical: 10,
  },
  messageContainer: {
    paddingHorizontal: 15,
    marginVertical: 4,
  },
  aiMessageContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  userMessageContainer: {
    alignItems: 'flex-end',
  },
  aiAvatar: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#E3F2FD',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    marginTop: 5,
  },
  messageBubble: {
    maxWidth: '80%',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 18,
  },
  aiMessageBubble: {
    backgroundColor: '#FFFFFF',
    borderBottomLeftRadius: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  userMessageBubble: {
    backgroundColor: '#007AFF',
    borderBottomRightRadius: 4,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
  },
  aiMessageText: {
    color: '#333',
  },
  userMessageText: {
    color: '#FFFFFF',
  },
  messageTime: {
    fontSize: 11,
    marginTop: 4,
  },
  aiMessageTime: {
    color: '#999',
  },
  userMessageTime: {
    color: 'rgba(255, 255, 255, 0.7)',
  },
  quickQuestionsContainer: {
    padding: 15,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  quickQuestionsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
    marginBottom: 10,
  },
  quickQuestionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  quickQuestionButton: {
    backgroundColor: '#F0F8FF',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  quickQuestionText: {
    fontSize: 12,
    color: '#007AFF',
    fontWeight: '500',
  },
  typingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 8,
  },
  typingBubble: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 18,
    borderBottomLeftRadius: 4,
  },
  typingText: {
    fontSize: 14,
    color: '#999',
    fontStyle: 'italic',
  },
  inputContainer: {
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
  textInputContainer: {
    flex: 1,
    backgroundColor: '#F8F9FA',
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 8,
    marginRight: 10,
    maxHeight: 100,
  },
  textInput: {
    fontSize: 16,
    color: '#333',
    minHeight: 20,
  },
  sendButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: '#B0B0B0',
  },
});
