# 🚀 ProChat Platform - Complete Social & Financial Platform

![ProChat Logo](https://via.placeholder.com/200x80/007AFF/FFFFFF?text=ProChat)

[![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)](https://github.com/prochat/platform)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](https://github.com/prochat/platform/actions)

## 📋 Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Architecture](#architecture)
- [Quick Start](#quick-start)
- [Documentation](#documentation)
- [Contributing](#contributing)
- [License](#license)

## 🌟 Overview

ProChat is a comprehensive social and financial platform that combines social media features with integrated financial services. Built with modern technologies, it provides users with seamless communication, content sharing, and financial transactions in one unified platform.

### 🎯 Key Highlights

- **Social Media Platform** - Complete social networking features
- **Integrated Wallet (ProPay)** - Digital wallet with payment processing
- **Business Hub (ProZone)** - Agent and merchant services
- **Live Streaming** - Real-time video streaming capabilities
- **Events & Ticketing** - Event management and ticket sales
- **Jobs & Tenders** - Job board and tender management
- **News & Media** - News aggregation and content management

## 🚀 Features

### 📱 Mobile App (React Native)
- ✅ **4 Main Tabs**: Chats, Home, Discover, Me
- ✅ **25+ Screens**: Complete user interface
- ✅ **Real-time Chat**: WebSocket-based messaging
- ✅ **Dark/Light Theme**: User preference support
- ✅ **Offline Support**: Cached data and sync

### 🖥️ Backend API (Spring Boot)
- ✅ **REST APIs**: 15+ controllers with full CRUD operations
- ✅ **Authentication**: JWT-based security
- ✅ **Authorization**: Role-based access control
- ✅ **Database**: MySQL with JPA/Hibernate
- ✅ **File Storage**: AWS S3 integration
- ✅ **Real-time**: WebSocket support

### 🌐 Admin Panel (React)
- ✅ **8 Admin Modules**: Complete management system
- ✅ **7 User Roles**: Granular permission system
- ✅ **Dashboard**: Analytics and insights
- ✅ **Content Moderation**: User-generated content management
- ✅ **Financial Management**: Transaction and wallet oversight

### 🌍 Public Website (React)
- ✅ **Landing Page**: Marketing and information
- ✅ **Event Listings**: Public event discovery
- ✅ **Job Board**: Public job and tender listings
- ✅ **News Portal**: Public news and articles

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile App    │    │   Admin Panel   │    │ Public Website  │
│  (React Native) │    │     (React)     │    │     (React)     │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴───────────┐
                    │     Backend API        │
                    │   (Spring Boot)        │
                    └─────────────┬───────────┘
                                 │
                    ┌─────────────┴───────────┐
                    │      Database          │
                    │       (MySQL)          │
                    └─────────────────────────┘
```

### 🛠️ Technology Stack

| Component | Technology | Version |
|-----------|------------|---------|
| **Mobile** | React Native | 0.72+ |
| **Backend** | Spring Boot | 3.1+ |
| **Database** | MySQL | 8.0+ |
| **Admin Panel** | React | 18+ |
| **Public Site** | React | 18+ |
| **File Storage** | AWS S3 | Latest |
| **Authentication** | JWT | Latest |
| **Real-time** | WebSocket | Latest |

## ⚡ Quick Start

### Prerequisites
- Node.js 18+
- Java 17+
- MySQL 8.0+
- AWS Account (for S3)

### 1. Clone Repository
```bash
git clone https://github.com/prochat/platform.git
cd prochat-platform
```

### 2. Setup Backend
```bash
cd backend
./mvnw spring-boot:run
```

### 3. Setup Mobile App
```bash
cd mobile
npm install
npx react-native run-android  # or run-ios
```

### 4. Setup Admin Panel
```bash
cd admin
npm install
npm start
```

### 5. Setup Public Website
```bash
cd website
npm install
npm start
```

## 📚 Documentation

### 📖 User Guides
- [Installation Guide](setup/installation.md)
- [Configuration Guide](setup/configuration.md)
- [Deployment Guide](setup/deployment.md)

### 🔌 API Documentation
- [Authentication API](api/authentication.md)
- [Users API](api/users.md)
- [Posts API](api/posts.md)
- [Wallet API](api/wallet.md)
- [Events API](api/events.md)

### 🎯 Feature Documentation
- [ProPay Wallet](features/propay-wallet.md)
- [Live Streaming](features/live-streaming.md)
- [ProZone Business](features/prozone.md)
- [Social Features](features/social-features.md)

### 👨‍💼 Admin Documentation
- [User Management](admin/user-management.md)
- [Content Moderation](admin/content-moderation.md)
- [System Settings](admin/system-settings.md)

### 👨‍💻 Developer Documentation
- [Coding Standards](development/coding-standards.md)
- [Database Schema](development/database-schema.md)
- [Testing Guide](development/testing-guide.md)

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Workflow
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

- **Email**: <EMAIL>
- **Phone**: +255 123 456 789
- **Website**: https://prochat.co.tz
- **Documentation**: https://docs.prochat.co.tz

## 🙏 Acknowledgments

- React Native Community
- Spring Boot Team
- MySQL Team
- AWS Team
- All contributors and supporters

---

**Made with ❤️ by ProChat Team**
