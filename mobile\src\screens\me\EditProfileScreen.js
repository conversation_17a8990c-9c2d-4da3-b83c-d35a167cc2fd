import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Image,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import * as ImagePicker from 'expo-image-picker';
import { colors, typography, spacing } from '../../theme/theme';
import { userAPI, uploadAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

export default function EditProfileScreen({ navigation }) {
  const { user, updateUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    username: '',
    email: '',
    phoneNumber: '',
    bio: '',
    location: '',
    website: '',
    dateOfBirth: '',
    gender: '',
    profilePicture: '',
  });

  useEffect(() => {
    if (user) {
      setFormData({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        username: user.username || '',
        email: user.email || '',
        phoneNumber: user.phoneNumber || '',
        bio: user.bio || '',
        location: user.location || '',
        website: user.website || '',
        dateOfBirth: user.dateOfBirth || '',
        gender: user.gender || '',
        profilePicture: user.profilePicture || '',
      });
    }
  }, [user]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleImagePicker = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert('Ruhusa', 'Tunahitaji ruhusa ya kufikia picha zako');
        return;
      }

      Alert.alert(
        'Chagua Picha',
        'Unataka kuchagua picha kutoka wapi?',
        [
          { text: 'Ghairi', style: 'cancel' },
          { text: 'Kamera', onPress: () => openCamera() },
          { text: 'Gallery', onPress: () => openGallery() },
        ]
      );
    } catch (error) {
      console.error('Image picker error:', error);
    }
  };

  const openCamera = async () => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert('Ruhusa', 'Tunahitaji ruhusa ya kutumia kamera');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled) {
        uploadProfileImage(result.assets[0]);
      }
    } catch (error) {
      console.error('Camera error:', error);
    }
  };

  const openGallery = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled) {
        uploadProfileImage(result.assets[0]);
      }
    } catch (error) {
      console.error('Gallery error:', error);
    }
  };

  const uploadProfileImage = async (imageAsset) => {
    try {
      setUploading(true);
      const response = await uploadAPI.uploadImage(imageAsset, 'profile');
      
      if (response.success) {
        setFormData(prev => ({
          ...prev,
          profilePicture: response.data.url
        }));
      } else {
        Alert.alert('Hitilafu', 'Imeshindwa kupakia picha');
      }
    } catch (error) {
      console.error('Upload error:', error);
      Alert.alert('Hitilafu', 'Imeshindwa kupakia picha');
    } finally {
      setUploading(false);
    }
  };

  const validateForm = () => {
    if (!formData.firstName.trim()) {
      Alert.alert('Hitilafu', 'Jina la kwanza ni lazima');
      return false;
    }
    
    if (!formData.lastName.trim()) {
      Alert.alert('Hitilafu', 'Jina la mwisho ni lazima');
      return false;
    }
    
    if (!formData.username.trim()) {
      Alert.alert('Hitilafu', 'Jina la mtumiaji ni lazima');
      return false;
    }
    
    if (!formData.email.trim()) {
      Alert.alert('Hitilafu', 'Barua pepe ni lazima');
      return false;
    }
    
    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      Alert.alert('Hitilafu', 'Barua pepe si sahihi');
      return false;
    }
    
    // Phone validation
    if (formData.phoneNumber && !/^[+]?[0-9\s-()]+$/.test(formData.phoneNumber)) {
      Alert.alert('Hitilafu', 'Namba ya simu si sahihi');
      return false;
    }
    
    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);
      const response = await userAPI.updateProfile(user.id, formData);
      
      if (response.success) {
        await updateUser(response.data);
        Alert.alert(
          'Mafanikio!',
          'Wasifu wako umesasishwa',
          [
            {
              text: 'Sawa',
              onPress: () => navigation.goBack()
            }
          ]
        );
      } else {
        Alert.alert('Hitilafu', response.message || 'Imeshindwa kusasisha wasifu');
      }
    } catch (error) {
      console.error('Update error:', error);
      Alert.alert('Hitilafu', 'Imeshindwa kusasisha wasifu');
    } finally {
      setLoading(false);
    }
  };

  const genderOptions = [
    { value: 'MALE', label: 'Mwanaume' },
    { value: 'FEMALE', label: 'Mwanamke' },
    { value: 'OTHER', label: 'Nyingine' },
    { value: 'PREFER_NOT_TO_SAY', label: 'Sipendi kusema' },
  ];

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="close" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Hariri Wasifu</Text>
        <TouchableOpacity
          style={[styles.saveButton, loading && styles.saveButtonDisabled]}
          onPress={handleSave}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color={colors.primary} />
          ) : (
            <Text style={styles.saveButtonText}>Hifadhi</Text>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {/* Profile Picture */}
        <View style={styles.profilePictureSection}>
          <TouchableOpacity
            style={styles.profilePictureContainer}
            onPress={handleImagePicker}
            disabled={uploading}
          >
            <Image
              source={{ 
                uri: formData.profilePicture || 'https://via.placeholder.com/120' 
              }}
              style={styles.profilePicture}
            />
            <View style={styles.cameraOverlay}>
              {uploading ? (
                <ActivityIndicator size="small" color={colors.white} />
              ) : (
                <Icon name="camera-alt" size={20} color={colors.white} />
              )}
            </View>
          </TouchableOpacity>
          <Text style={styles.profilePictureText}>Bonyeza kubadilisha picha</Text>
        </View>

        {/* Basic Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Taarifa za Msingi</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Jina la Kwanza *</Text>
            <TextInput
              style={styles.textInput}
              placeholder="Andika jina lako la kwanza"
              placeholderTextColor={colors.textSecondary}
              value={formData.firstName}
              onChangeText={(text) => handleInputChange('firstName', text)}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Jina la Mwisho *</Text>
            <TextInput
              style={styles.textInput}
              placeholder="Andika jina lako la mwisho"
              placeholderTextColor={colors.textSecondary}
              value={formData.lastName}
              onChangeText={(text) => handleInputChange('lastName', text)}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Jina la Mtumiaji *</Text>
            <TextInput
              style={styles.textInput}
              placeholder="@jinalako"
              placeholderTextColor={colors.textSecondary}
              value={formData.username}
              onChangeText={(text) => handleInputChange('username', text.toLowerCase())}
              autoCapitalize="none"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Barua Pepe *</Text>
            <TextInput
              style={styles.textInput}
              placeholder="<EMAIL>"
              placeholderTextColor={colors.textSecondary}
              value={formData.email}
              onChangeText={(text) => handleInputChange('email', text)}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Namba ya Simu</Text>
            <TextInput
              style={styles.textInput}
              placeholder="+255 123 456 789"
              placeholderTextColor={colors.textSecondary}
              value={formData.phoneNumber}
              onChangeText={(text) => handleInputChange('phoneNumber', text)}
              keyboardType="phone-pad"
            />
          </View>
        </View>

        {/* Personal Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Taarifa za Kibinafsi</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Maelezo Mafupi</Text>
            <TextInput
              style={[styles.textInput, styles.bioInput]}
              placeholder="Eleza kidogo kuhusu wewe..."
              placeholderTextColor={colors.textSecondary}
              value={formData.bio}
              onChangeText={(text) => handleInputChange('bio', text)}
              multiline
              numberOfLines={3}
              maxLength={150}
            />
            <Text style={styles.characterCount}>
              {formData.bio.length}/150
            </Text>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Mahali</Text>
            <TextInput
              style={styles.textInput}
              placeholder="Mji, Nchi"
              placeholderTextColor={colors.textSecondary}
              value={formData.location}
              onChangeText={(text) => handleInputChange('location', text)}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Tovuti</Text>
            <TextInput
              style={styles.textInput}
              placeholder="https://example.com"
              placeholderTextColor={colors.textSecondary}
              value={formData.website}
              onChangeText={(text) => handleInputChange('website', text)}
              keyboardType="url"
              autoCapitalize="none"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Jinsia</Text>
            <View style={styles.genderOptions}>
              {genderOptions.map((option) => (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    styles.genderOption,
                    formData.gender === option.value && styles.selectedGender
                  ]}
                  onPress={() => handleInputChange('gender', option.value)}
                >
                  <Text style={[
                    styles.genderText,
                    formData.gender === option.value && styles.selectedGenderText
                  ]}>
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>

        {/* Privacy Note */}
        <View style={styles.privacyNote}>
          <Icon name="info" size={20} color={colors.info} />
          <Text style={styles.privacyText}>
            Baadhi ya taarifa hizi zitaonekana kwa umma kwenye wasifu wako. 
            Unaweza kubadilisha mipangilio ya faragha kwenye sehemu ya mipangilio.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
  },
  saveButton: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
  },
  saveButtonDisabled: {
    opacity: 0.5,
  },
  saveButtonText: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.primary,
  },
  content: {
    flex: 1,
  },
  profilePictureSection: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
    backgroundColor: colors.white,
    marginBottom: spacing.sm,
  },
  profilePictureContainer: {
    position: 'relative',
    marginBottom: spacing.md,
  },
  profilePicture: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 3,
    borderColor: colors.primary,
  },
  cameraOverlay: {
    position: 'absolute',
    bottom: 5,
    right: 5,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profilePictureText: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
  section: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginBottom: spacing.sm,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.lg,
  },
  inputGroup: {
    marginBottom: spacing.lg,
  },
  inputLabel: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  textInput: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    fontSize: typography.fontSize.md,
    color: colors.text,
    backgroundColor: colors.surface,
  },
  bioInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  characterCount: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    textAlign: 'right',
    marginTop: spacing.xs,
  },
  genderOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  genderOption: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: colors.border,
    backgroundColor: colors.surface,
  },
  selectedGender: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  genderText: {
    fontSize: typography.fontSize.sm,
    color: colors.text,
  },
  selectedGenderText: {
    color: colors.white,
  },
  privacyNote: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: colors.info + '10',
    padding: spacing.md,
    margin: spacing.lg,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.info + '30',
  },
  privacyText: {
    fontSize: typography.fontSize.sm,
    color: colors.text,
    marginLeft: spacing.sm,
    flex: 1,
    lineHeight: 20,
  },
});
