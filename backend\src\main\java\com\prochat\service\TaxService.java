package com.prochat.service;

import com.prochat.model.tax.TaxConfiguration;
import com.prochat.model.tax.TaxTransaction;
import com.prochat.model.User;
import com.prochat.repository.TaxConfigurationRepository;
import com.prochat.repository.TaxTransactionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

@Service
@Transactional
public class TaxService {
    
    @Autowired
    private TaxConfigurationRepository taxConfigRepository;
    
    @Autowired
    private TaxTransactionRepository taxTransactionRepository;
    
    /**
     * Calculate and apply VAT to a transaction
     */
    public TaxCalculationResult calculateVAT(BigDecimal amount, String transactionType) {
        TaxConfiguration vatConfig = getActiveVATConfiguration();
        
        if (vatConfig == null || !shouldApplyTax(vatConfig, transactionType)) {
            return new TaxCalculationResult(amount, BigDecimal.ZERO, amount, null);
        }
        
        BigDecimal taxAmount = vatConfig.calculateTaxAmount(amount);
        BigDecimal netAmount = amount.subtract(taxAmount);
        
        return new TaxCalculationResult(amount, taxAmount, netAmount, vatConfig);
    }
    
    /**
     * Record tax transaction in database
     */
    public TaxTransaction recordTaxTransaction(User user, Long originalTransactionId, 
                                            String transactionType, TaxCalculationResult result) {
        if (result.getTaxAmount().compareTo(BigDecimal.ZERO) == 0) {
            return null; // No tax to record
        }
        
        TaxTransaction taxTransaction = new TaxTransaction();
        taxTransaction.setTaxConfiguration(result.getTaxConfiguration());
        taxTransaction.setUser(user);
        taxTransaction.setOriginalTransactionId(originalTransactionId);
        taxTransaction.setTransactionType(transactionType);
        taxTransaction.setGrossAmount(result.getGrossAmount());
        taxTransaction.setTaxRate(result.getTaxConfiguration().getTaxRate());
        taxTransaction.setTaxAmount(result.getTaxAmount());
        taxTransaction.setNetAmount(result.getNetAmount());
        taxTransaction.setDescription("VAT for " + transactionType);
        
        return taxTransactionRepository.save(taxTransaction);
    }
    
    /**
     * Get active VAT configuration
     */
    public TaxConfiguration getActiveVATConfiguration() {
        return taxConfigRepository.findByTaxTypeAndIsActiveTrue(
            TaxConfiguration.TaxType.VAT).orElse(null);
    }
    
    /**
     * Check if tax should be applied to transaction type
     */
    private boolean shouldApplyTax(TaxConfiguration config, String transactionType) {
        switch (transactionType.toUpperCase()) {
            case "SEND_MONEY":
            case "PROPAY_TRANSACTION":
                return config.getAppliesToTransactions();
            case "WITHDRAW":
            case "WITHDRAWAL":
                return config.getAppliesToWithdrawals();
            case "DONATION":
                return config.getAppliesToDonations();
            case "MERCHANT_PAYMENT":
            case "LIPA_NAMBA":
                return config.getAppliesToMerchantPayments();
            case "GIFT":
                return config.getAppliesToGifts();
            case "TICKET":
                return config.getAppliesToTickets();
            default:
                return false;
        }
    }
    
    /**
     * Generate VAT report for a period
     */
    public VATReport generateVATReport(LocalDateTime startDate, LocalDateTime endDate) {
        List<TaxTransaction> transactions = taxTransactionRepository
            .findByCollectionDateBetweenAndTaxConfiguration_TaxType(
                startDate, endDate, TaxConfiguration.TaxType.VAT);
        
        VATReport report = new VATReport();
        report.setStartDate(startDate);
        report.setEndDate(endDate);
        report.setGeneratedAt(LocalDateTime.now());
        
        BigDecimal totalTaxCollected = BigDecimal.ZERO;
        BigDecimal totalGrossAmount = BigDecimal.ZERO;
        Map<String, VATReportItem> itemsByType = new HashMap<>();
        
        for (TaxTransaction transaction : transactions) {
            totalTaxCollected = totalTaxCollected.add(transaction.getTaxAmount());
            totalGrossAmount = totalGrossAmount.add(transaction.getGrossAmount());
            
            String type = transaction.getTransactionType();
            VATReportItem item = itemsByType.getOrDefault(type, new VATReportItem(type));
            item.addTransaction(transaction);
            itemsByType.put(type, item);
        }
        
        report.setTotalTaxCollected(totalTaxCollected);
        report.setTotalGrossAmount(totalGrossAmount);
        report.setTotalTransactions((long) transactions.size());
        report.setItemsByType(itemsByType);
        
        return report;
    }
    
    /**
     * Get tax summary for dashboard
     */
    public TaxSummary getTaxSummary() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfMonth = now.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
        LocalDateTime startOfYear = now.withDayOfYear(1).withHour(0).withMinute(0).withSecond(0);
        
        TaxSummary summary = new TaxSummary();
        
        // Monthly summary
        BigDecimal monthlyTax = taxTransactionRepository
            .sumTaxAmountByDateRange(startOfMonth, now);
        summary.setMonthlyTaxCollected(monthlyTax != null ? monthlyTax : BigDecimal.ZERO);
        
        // Yearly summary
        BigDecimal yearlyTax = taxTransactionRepository
            .sumTaxAmountByDateRange(startOfYear, now);
        summary.setYearlyTaxCollected(yearlyTax != null ? yearlyTax : BigDecimal.ZERO);
        
        // Pending remittance
        BigDecimal pendingRemittance = taxTransactionRepository
            .sumTaxAmountByRemittanceStatus(false);
        summary.setPendingRemittance(pendingRemittance != null ? pendingRemittance : BigDecimal.ZERO);
        
        return summary;
    }
    
    /**
     * Update VAT rate (Admin function)
     */
    public TaxConfiguration updateVATRate(BigDecimal newRate, Long adminId) {
        TaxConfiguration vatConfig = getActiveVATConfiguration();
        
        if (vatConfig == null) {
            // Create new VAT configuration
            vatConfig = new TaxConfiguration();
            vatConfig.setTaxType(TaxConfiguration.TaxType.VAT);
            vatConfig.setTaxName("Value Added Tax");
            vatConfig.setTaxAuthority("Tanzania Revenue Authority (TRA)");
            vatConfig.setTaxId("ProChat-TIN-*********"); // Replace with actual TIN
        }
        
        vatConfig.setTaxRate(newRate);
        vatConfig.setTaxPercentage(newRate.multiply(new BigDecimal("100")));
        vatConfig.setUpdatedBy(adminId);
        vatConfig.setEffectiveFrom(LocalDateTime.now());
        
        return taxConfigRepository.save(vatConfig);
    }
    
    // Inner classes for return types
    public static class TaxCalculationResult {
        private BigDecimal grossAmount;
        private BigDecimal taxAmount;
        private BigDecimal netAmount;
        private TaxConfiguration taxConfiguration;
        
        public TaxCalculationResult(BigDecimal grossAmount, BigDecimal taxAmount, 
                                  BigDecimal netAmount, TaxConfiguration taxConfiguration) {
            this.grossAmount = grossAmount;
            this.taxAmount = taxAmount;
            this.netAmount = netAmount;
            this.taxConfiguration = taxConfiguration;
        }
        
        // Getters
        public BigDecimal getGrossAmount() { return grossAmount; }
        public BigDecimal getTaxAmount() { return taxAmount; }
        public BigDecimal getNetAmount() { return netAmount; }
        public TaxConfiguration getTaxConfiguration() { return taxConfiguration; }
    }
    
    public static class VATReport {
        private LocalDateTime startDate;
        private LocalDateTime endDate;
        private LocalDateTime generatedAt;
        private BigDecimal totalTaxCollected;
        private BigDecimal totalGrossAmount;
        private Long totalTransactions;
        private Map<String, VATReportItem> itemsByType;
        
        // Getters and setters
        public LocalDateTime getStartDate() { return startDate; }
        public void setStartDate(LocalDateTime startDate) { this.startDate = startDate; }
        public LocalDateTime getEndDate() { return endDate; }
        public void setEndDate(LocalDateTime endDate) { this.endDate = endDate; }
        public LocalDateTime getGeneratedAt() { return generatedAt; }
        public void setGeneratedAt(LocalDateTime generatedAt) { this.generatedAt = generatedAt; }
        public BigDecimal getTotalTaxCollected() { return totalTaxCollected; }
        public void setTotalTaxCollected(BigDecimal totalTaxCollected) { this.totalTaxCollected = totalTaxCollected; }
        public BigDecimal getTotalGrossAmount() { return totalGrossAmount; }
        public void setTotalGrossAmount(BigDecimal totalGrossAmount) { this.totalGrossAmount = totalGrossAmount; }
        public Long getTotalTransactions() { return totalTransactions; }
        public void setTotalTransactions(Long totalTransactions) { this.totalTransactions = totalTransactions; }
        public Map<String, VATReportItem> getItemsByType() { return itemsByType; }
        public void setItemsByType(Map<String, VATReportItem> itemsByType) { this.itemsByType = itemsByType; }
    }
    
    public static class VATReportItem {
        private String transactionType;
        private BigDecimal totalGrossAmount = BigDecimal.ZERO;
        private BigDecimal totalTaxAmount = BigDecimal.ZERO;
        private Long transactionCount = 0L;
        
        public VATReportItem(String transactionType) {
            this.transactionType = transactionType;
        }
        
        public void addTransaction(TaxTransaction transaction) {
            totalGrossAmount = totalGrossAmount.add(transaction.getGrossAmount());
            totalTaxAmount = totalTaxAmount.add(transaction.getTaxAmount());
            transactionCount++;
        }
        
        // Getters
        public String getTransactionType() { return transactionType; }
        public BigDecimal getTotalGrossAmount() { return totalGrossAmount; }
        public BigDecimal getTotalTaxAmount() { return totalTaxAmount; }
        public Long getTransactionCount() { return transactionCount; }
    }
    
    public static class TaxSummary {
        private BigDecimal monthlyTaxCollected;
        private BigDecimal yearlyTaxCollected;
        private BigDecimal pendingRemittance;
        
        // Getters and setters
        public BigDecimal getMonthlyTaxCollected() { return monthlyTaxCollected; }
        public void setMonthlyTaxCollected(BigDecimal monthlyTaxCollected) { this.monthlyTaxCollected = monthlyTaxCollected; }
        public BigDecimal getYearlyTaxCollected() { return yearlyTaxCollected; }
        public void setYearlyTaxCollected(BigDecimal yearlyTaxCollected) { this.yearlyTaxCollected = yearlyTaxCollected; }
        public BigDecimal getPendingRemittance() { return pendingRemittance; }
        public void setPendingRemittance(BigDecimal pendingRemittance) { this.pendingRemittance = pendingRemittance; }
    }
}
