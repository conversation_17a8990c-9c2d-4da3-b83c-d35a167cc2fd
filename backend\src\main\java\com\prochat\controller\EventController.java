package com.prochat.controller;

import com.prochat.model.Event;
import com.prochat.model.EventAttendee;
import com.prochat.service.EventService;
import com.prochat.security.CurrentUser;
import com.prochat.security.UserPrincipal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

@RestController
@RequestMapping("/api/events")
@PreAuthorize("hasRole('USER')")
public class EventController {

    @Autowired
    private EventService eventService;

    @GetMapping
    public ResponseEntity<?> getEvents(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String location,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            List<Event> events = eventService.getEvents(page, size, category, location, startDate, endDate);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", events);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata matukio: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/{eventId}")
    public ResponseEntity<?> getEventById(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable Long eventId) {
        try {
            Event event = eventService.getEventById(eventId, currentUser.getId());
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", event);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata tukio: " + e.getMessage()
            ));
        }
    }

    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('EVENT_ORGANIZER')")
    public ResponseEntity<?> createEvent(
            @CurrentUser UserPrincipal currentUser,
            @Valid @RequestBody CreateEventRequest request) {
        try {
            Event event = eventService.createEvent(currentUser.getId(), request);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Tukio limeundwa kwa mafanikio");
            response.put("data", event);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kuunda tukio: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/{eventId}/attend")
    public ResponseEntity<?> attendEvent(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable Long eventId) {
        try {
            EventAttendee attendee = eventService.attendEvent(currentUser.getId(), eventId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Umejiandikisha kwa tukio");
            response.put("data", attendee);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kujiandikisha: " + e.getMessage()
            ));
        }
    }

    @DeleteMapping("/{eventId}/attend")
    public ResponseEntity<?> cancelAttendance(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable Long eventId) {
        try {
            eventService.cancelAttendance(currentUser.getId(), eventId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Umejiondoa kwenye tukio");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kujiondoa: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/{eventId}/attendees")
    public ResponseEntity<?> getEventAttendees(
            @PathVariable Long eventId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            List<EventAttendee> attendees = eventService.getEventAttendees(eventId, page, size);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", attendees);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata washiriki: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/categories")
    public ResponseEntity<?> getEventCategories() {
        try {
            List<String> categories = eventService.getEventCategories();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", categories);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata makundi: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/trending")
    public ResponseEntity<?> getTrendingEvents(
            @RequestParam(defaultValue = "10") int limit) {
        try {
            List<Event> events = eventService.getTrendingEvents(limit);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", events);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata matukio maarufu: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/user/{userId}")
    public ResponseEntity<?> getUserEvents(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "all") String type) {
        try {
            List<Event> events = eventService.getUserEvents(userId, page, size, type);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", events);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata matukio ya mtumiaji: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/{eventId}/share")
    public ResponseEntity<?> shareEvent(
            @CurrentUser UserPrincipal currentUser,
            @PathVariable Long eventId) {
        try {
            String shareUrl = eventService.generateShareUrl(eventId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", Map.of(
                "shareUrl", shareUrl,
                "message", "Shiriki tukio hili na marafiki zako!"
            ));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kushiriki: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/{eventId}/qr-code")
    public ResponseEntity<?> getEventQRCode(@PathVariable Long eventId) {
        try {
            String qrCode = eventService.generateEventQRCode(eventId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", Map.of("qrCode", qrCode));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kupata QR code: " + e.getMessage()
            ));
        }
    }

    // Admin endpoints
    @PutMapping("/{eventId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateEvent(
            @PathVariable Long eventId,
            @Valid @RequestBody UpdateEventRequest request) {
        try {
            Event event = eventService.updateEvent(eventId, request);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Tukio limesasishwa");
            response.put("data", event);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kusasisha: " + e.getMessage()
            ));
        }
    }

    @DeleteMapping("/{eventId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteEvent(@PathVariable Long eventId) {
        try {
            eventService.deleteEvent(eventId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Tukio limefutwa");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Hitilafu ya kufuta: " + e.getMessage()
            ));
        }
    }

    // Request DTOs
    public static class CreateEventRequest {
        private String title;
        private String description;
        private String category;
        private String location;
        private String startDate;
        private String endDate;
        private String imageUrl;
        private Boolean isPublic;
        private Integer maxAttendees;
        
        // Getters and setters
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }
        public String getLocation() { return location; }
        public void setLocation(String location) { this.location = location; }
        public String getStartDate() { return startDate; }
        public void setStartDate(String startDate) { this.startDate = startDate; }
        public String getEndDate() { return endDate; }
        public void setEndDate(String endDate) { this.endDate = endDate; }
        public String getImageUrl() { return imageUrl; }
        public void setImageUrl(String imageUrl) { this.imageUrl = imageUrl; }
        public Boolean getIsPublic() { return isPublic; }
        public void setIsPublic(Boolean isPublic) { this.isPublic = isPublic; }
        public Integer getMaxAttendees() { return maxAttendees; }
        public void setMaxAttendees(Integer maxAttendees) { this.maxAttendees = maxAttendees; }
    }

    public static class UpdateEventRequest {
        private String title;
        private String description;
        private String location;
        private String startDate;
        private String endDate;
        private String imageUrl;
        private Boolean isPublic;
        private Integer maxAttendees;
        
        // Getters and setters
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getLocation() { return location; }
        public void setLocation(String location) { this.location = location; }
        public String getStartDate() { return startDate; }
        public void setStartDate(String startDate) { this.startDate = startDate; }
        public String getEndDate() { return endDate; }
        public void setEndDate(String endDate) { this.endDate = endDate; }
        public String getImageUrl() { return imageUrl; }
        public void setImageUrl(String imageUrl) { this.imageUrl = imageUrl; }
        public Boolean getIsPublic() { return isPublic; }
        public void setIsPublic(Boolean isPublic) { this.isPublic = isPublic; }
        public Integer getMaxAttendees() { return maxAttendees; }
        public void setMaxAttendees(Integer maxAttendees) { this.maxAttendees = maxAttendees; }
    }
}
