import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Chip,
  Button,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  Divider,
} from '@mui/material';
import {
  Search,
  Work,
  LocationOn,
  Business,
  AttachMoney,
  Schedule,
  TrendingUp,
} from '@mui/icons-material';

export default function JobsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [locationFilter, setLocationFilter] = useState('');
  const [typeFilter, setTypeFilter] = useState('');

  const jobs = [
    {
      id: 1,
      title: 'Software Developer - React & Node.js',
      company: 'TechCorp Tanzania',
      companyLogo: '/images/techcorp-logo.jpg',
      location: 'Dar es Salaam',
      jobType: 'TECHNOLOGY',
      employmentType: 'FULL_TIME',
      salaryMin: 800000,
      salaryMax: 1500000,
      currency: 'TSH',
      description: 'Tunatafuta msanidi programu mwenye ujuzi wa React na Node.js...',
      requirements: ['Shahada ya kwanza katika Computer Science', 'Miaka 2+ ya uzoefu', 'JavaScript, React, Node.js'],
      postedDate: '2024-01-18',
      applications: 25,
      featured: true,
    },
    {
      id: 2,
      title: 'Marketing Manager',
      company: 'Vodacom Tanzania',
      companyLogo: '/images/vodacom-logo.jpg',
      location: 'Dodoma',
      jobType: 'MARKETING',
      employmentType: 'FULL_TIME',
      salaryMin: 1200000,
      salaryMax: 2000000,
      currency: 'TSH',
      description: 'Nafasi ya meneja wa uuzaji kwa kampuni kubwa ya mawasiliano...',
      requirements: ['Shahada ya Marketing au sawa', 'Miaka 3+ ya uzoefu', 'Ujuzi wa digital marketing'],
      postedDate: '2024-01-15',
      applications: 45,
      featured: false,
    },
    {
      id: 3,
      title: 'Accountant - Finance Department',
      company: 'CRDB Bank',
      companyLogo: '/images/crdb-logo.jpg',
      location: 'Mwanza',
      jobType: 'FINANCE',
      employmentType: 'FULL_TIME',
      salaryMin: 600000,
      salaryMax: 1000000,
      currency: 'TSH',
      description: 'Tunahitaji mhasibu mwenye uzoefu katika sekta ya benki...',
      requirements: ['CPA Tanzania', 'Miaka 2+ ya uzoefu', 'Ujuzi wa accounting software'],
      postedDate: '2024-01-12',
      applications: 67,
      featured: true,
    },
    {
      id: 4,
      title: 'Graphic Designer',
      company: 'Creative Agency TZ',
      companyLogo: '/images/creative-logo.jpg',
      location: 'Arusha',
      jobType: 'ARTS',
      employmentType: 'PART_TIME',
      salaryMin: 400000,
      salaryMax: 800000,
      currency: 'TSH',
      description: 'Nafasi ya mbunifu wa michoro kwa wakala wa ubunifu...',
      requirements: ['Diploma ya Graphic Design', 'Adobe Creative Suite', 'Portfolio ya kazi'],
      postedDate: '2024-01-10',
      applications: 32,
      featured: false,
    },
  ];

  const jobCategories = [
    'TECHNOLOGY', 'MARKETING', 'FINANCE', 'HEALTHCARE', 'EDUCATION', 
    'ENGINEERING', 'ARTS', 'SALES', 'ADMINISTRATION'
  ];

  const employmentTypes = [
    'FULL_TIME', 'PART_TIME', 'CONTRACT', 'INTERNSHIP', 'FREELANCE'
  ];

  const locations = ['Dar es Salaam', 'Dodoma', 'Mwanza', 'Arusha', 'Mbeya'];

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TSH',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getJobTypeLabel = (type) => {
    const types = {
      TECHNOLOGY: 'Teknolojia',
      MARKETING: 'Uuzaji',
      FINANCE: 'Fedha',
      HEALTHCARE: 'Afya',
      EDUCATION: 'Elimu',
      ENGINEERING: 'Uhandisi',
      ARTS: 'Sanaa',
      SALES: 'Mauzo',
      ADMINISTRATION: 'Utawala',
    };
    return types[type] || type;
  };

  const getEmploymentTypeLabel = (type) => {
    const types = {
      FULL_TIME: 'Wakati wote',
      PART_TIME: 'Wakati wa nusu',
      CONTRACT: 'Mkataba',
      INTERNSHIP: 'Mafunzo',
      FREELANCE: 'Huru',
    };
    return types[type] || type;
  };

  const getJobTypeColor = (type) => {
    const colors = {
      TECHNOLOGY: 'primary',
      MARKETING: 'secondary',
      FINANCE: 'success',
      HEALTHCARE: 'error',
      EDUCATION: 'info',
      ENGINEERING: 'warning',
      ARTS: 'secondary',
      SALES: 'primary',
      ADMINISTRATION: 'default',
    };
    return colors[type] || 'default';
  };

  const filteredJobs = jobs.filter(job => {
    const matchesSearch = job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         job.company.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         job.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = !categoryFilter || job.jobType === categoryFilter;
    const matchesLocation = !locationFilter || job.location === locationFilter;
    const matchesType = !typeFilter || job.employmentType === typeFilter;
    
    return matchesSearch && matchesCategory && matchesLocation && matchesType;
  });

  const featuredJobs = filteredJobs.filter(job => job.featured);
  const regularJobs = filteredJobs.filter(job => !job.featured);

  return (
    <Box sx={{ py: 4 }}>
      <Container maxWidth="lg">
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Typography variant="h3" component="h1" gutterBottom>
            Kazi na Zabuni
          </Typography>
          <Typography variant="h6" color="text.secondary" sx={{ mb: 3 }}>
            Pata fursa za kazi na zabuni kutoka Tanzania
          </Typography>
        </Box>

        {/* Search and Filters */}
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  placeholder="Tafuta kazi au kampuni..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Aina ya Kazi</InputLabel>
                  <Select
                    value={categoryFilter}
                    label="Aina ya Kazi"
                    onChange={(e) => setCategoryFilter(e.target.value)}
                  >
                    <MenuItem value="">Vyote</MenuItem>
                    {jobCategories.map((category) => (
                      <MenuItem key={category} value={category}>
                        {getJobTypeLabel(category)}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Mahali</InputLabel>
                  <Select
                    value={locationFilter}
                    label="Mahali"
                    onChange={(e) => setLocationFilter(e.target.value)}
                  >
                    <MenuItem value="">Mahali Pote</MenuItem>
                    {locations.map((location) => (
                      <MenuItem key={location} value={location}>
                        {location}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Aina ya Ajira</InputLabel>
                  <Select
                    value={typeFilter}
                    label="Aina ya Ajira"
                    onChange={(e) => setTypeFilter(e.target.value)}
                  >
                    <MenuItem value="">Vyote</MenuItem>
                    {employmentTypes.map((type) => (
                      <MenuItem key={type} value={type}>
                        {getEmploymentTypeLabel(type)}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Featured Jobs */}
        {featuredJobs.length > 0 && (
          <Box sx={{ mb: 4 }}>
            <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              <TrendingUp sx={{ mr: 1 }} />
              Kazi za Maalum
            </Typography>
            <Grid container spacing={3}>
              {featuredJobs.map((job) => (
                <Grid item xs={12} key={job.id}>
                  <Card 
                    sx={{ 
                      cursor: 'pointer',
                      border: '2px solid',
                      borderColor: 'primary.main',
                      '&:hover': { 
                        transform: 'translateY(-2px)',
                        boxShadow: 4,
                      }, 
                      transition: 'all 0.3s' 
                    }}
                  >
                    <CardContent>
                      <Grid container spacing={3} alignItems="center">
                        <Grid item xs={12} md={2}>
                          <Avatar
                            src={job.companyLogo}
                            sx={{ width: 80, height: 80, mx: 'auto' }}
                          >
                            {job.company[0]}
                          </Avatar>
                        </Grid>
                        <Grid item xs={12} md={7}>
                          <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                            <Chip 
                              label={getJobTypeLabel(job.jobType)} 
                              color={getJobTypeColor(job.jobType)} 
                              size="small" 
                            />
                            <Chip 
                              label={getEmploymentTypeLabel(job.employmentType)} 
                              variant="outlined" 
                              size="small" 
                            />
                            <Chip label="MAALUM" color="error" size="small" />
                          </Box>
                          
                          <Typography variant="h5" component="h3" gutterBottom>
                            {job.title}
                          </Typography>
                          
                          <Typography variant="h6" color="text.secondary" gutterBottom>
                            {job.company}
                          </Typography>
                          
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                            {job.description}
                          </Typography>

                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, mb: 1 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <LocationOn sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                              <Typography variant="body2" color="text.secondary">
                                {job.location}
                              </Typography>
                            </Box>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <AttachMoney sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                              <Typography variant="body2" color="text.secondary">
                                {formatCurrency(job.salaryMin)} - {formatCurrency(job.salaryMax)}
                              </Typography>
                            </Box>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Work sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                              <Typography variant="body2" color="text.secondary">
                                {job.applications} maombi
                              </Typography>
                            </Box>
                          </Box>
                        </Grid>
                        <Grid item xs={12} md={3}>
                          <Box sx={{ textAlign: 'center' }}>
                            <Button variant="contained" size="large" fullWidth sx={{ mb: 2 }}>
                              Omba Sasa
                            </Button>
                            <Button variant="outlined" size="small" fullWidth>
                              Hifadhi Kazi
                            </Button>
                          </Box>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
        )}

        {/* Regular Jobs */}
        <Box>
          <Typography variant="h4" gutterBottom>
            Kazi Zingine
          </Typography>
          <Grid container spacing={3}>
            {regularJobs.map((job) => (
              <Grid item xs={12} md={6} key={job.id}>
                <Card 
                  sx={{ 
                    height: '100%', 
                    cursor: 'pointer',
                    '&:hover': { 
                      transform: 'translateY(-4px)',
                      boxShadow: 3,
                    }, 
                    transition: 'all 0.3s' 
                  }}
                >
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar
                        src={job.companyLogo}
                        sx={{ width: 50, height: 50, mr: 2 }}
                      >
                        {job.company[0]}
                      </Avatar>
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant="h6" component="h3">
                          {job.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {job.company}
                        </Typography>
                      </Box>
                    </Box>

                    <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                      <Chip 
                        label={getJobTypeLabel(job.jobType)} 
                        color={getJobTypeColor(job.jobType)} 
                        size="small" 
                      />
                      <Chip 
                        label={getEmploymentTypeLabel(job.employmentType)} 
                        variant="outlined" 
                        size="small" 
                      />
                    </Box>
                    
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {job.description.length > 120 
                        ? `${job.description.substring(0, 120)}...` 
                        : job.description
                      }
                    </Typography>

                    <Box sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <LocationOn sx={{ fontSize: 14, mr: 1, color: 'text.secondary' }} />
                        <Typography variant="caption" color="text.secondary">
                          {job.location}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <AttachMoney sx={{ fontSize: 14, mr: 1, color: 'text.secondary' }} />
                        <Typography variant="caption" color="text.secondary">
                          {formatCurrency(job.salaryMin)} - {formatCurrency(job.salaryMax)}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Schedule sx={{ fontSize: 14, mr: 1, color: 'text.secondary' }} />
                        <Typography variant="caption" color="text.secondary">
                          Imechapishwa {new Date(job.postedDate).toLocaleDateString('sw-TZ')}
                        </Typography>
                      </Box>
                    </Box>

                    <Divider sx={{ mb: 2 }} />

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="caption" color="text.secondary">
                        {job.applications} maombi
                      </Typography>
                      <Button variant="contained" size="small">
                        Angalia
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Load More */}
        <Box sx={{ textAlign: 'center', mt: 4 }}>
          <Button variant="outlined" size="large">
            Pakia Kazi Zaidi
          </Button>
        </Box>
      </Container>
    </Box>
  );
}
