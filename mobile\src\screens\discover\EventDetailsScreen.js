import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  Alert,
  Share,
  Linking,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors, typography, spacing } from '../../theme/theme';
import { eventsAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

export default function EventDetailsScreen({ navigation, route }) {
  const { eventId } = route.params;
  const { user } = useAuth();
  const [event, setEvent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAttending, setIsAttending] = useState(false);

  useEffect(() => {
    loadEventDetails();
  }, [eventId]);

  const loadEventDetails = async () => {
    try {
      const response = await eventsAPI.getEventById(eventId);
      if (response.success) {
        setEvent(response.data);
        setIsAttending(response.data.isUserAttending || false);
      }
    } catch (error) {
      console.error('Error loading event:', error);
      Alert.alert('Hitilafu', 'Imeshindwa kupakia taarifa za tukio');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('sw-TZ', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('sw-TZ', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleBuyTicket = () => {
    if (!user) {
      Alert.alert('Ingia Kwanza', 'Unahitaji kuingia ili kununua tiketi');
      return;
    }

    navigation.navigate('BuyTicket', { 
      eventId: event.id,
      eventTitle: event.title,
      ticketPrice: event.ticketPrice 
    });
  };

  const handleAttendEvent = async () => {
    try {
      const response = await eventsAPI.attendEvent(eventId);
      if (response.success) {
        setIsAttending(!isAttending);
        Alert.alert(
          'Mafanikio!',
          isAttending ? 'Umejiondoa kwenye tukio' : 'Umejisajili kwenye tukio'
        );
      }
    } catch (error) {
      Alert.alert('Hitilafu', 'Imeshindwa kusajili');
    }
  };

  const handleShare = async () => {
    try {
      await Share.share({
        message: `Angalia tukio hili la kipekee: ${event.title}\n\n${event.description}\n\nTarehe: ${formatDate(event.startDate)}\nMahali: ${event.location}\n\nPakua ProChat app kuona zaidi!`,
        url: event.shareUrl || '',
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  const handleGetDirections = () => {
    if (event.latitude && event.longitude) {
      const url = `https://maps.google.com/?q=${event.latitude},${event.longitude}`;
      Linking.openURL(url);
    } else if (event.location) {
      const url = `https://maps.google.com/?q=${encodeURIComponent(event.location)}`;
      Linking.openURL(url);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Inapakia...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!event) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text>Tukio halipatikani</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Header Image */}
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: event.imageUrl || 'https://via.placeholder.com/400x200' }}
            style={styles.eventImage}
            resizeMode="cover"
          />
          
          {/* Header Buttons */}
          <View style={styles.headerButtons}>
            <TouchableOpacity
              style={styles.headerButton}
              onPress={() => navigation.goBack()}
            >
              <Icon name="arrow-back" size={24} color={colors.white} />
            </TouchableOpacity>
            
            <View style={styles.headerRightButtons}>
              <TouchableOpacity style={styles.headerButton} onPress={handleShare}>
                <Icon name="share" size={24} color={colors.white} />
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.headerButton}>
                <Icon name="favorite-border" size={24} color={colors.white} />
              </TouchableOpacity>
            </View>
          </View>

          {/* Event Category Badge */}
          <View style={styles.categoryBadge}>
            <Text style={styles.categoryText}>{event.category}</Text>
          </View>
        </View>

        {/* Event Content */}
        <View style={styles.content}>
          {/* Title and Basic Info */}
          <View style={styles.titleSection}>
            <Text style={styles.eventTitle}>{event.title}</Text>
            <Text style={styles.eventOrganizer}>na {event.organizerName}</Text>
          </View>

          {/* Date and Time */}
          <View style={styles.infoSection}>
            <View style={styles.infoRow}>
              <Icon name="event" size={20} color={colors.primary} />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Tarehe na Muda</Text>
                <Text style={styles.infoValue}>
                  {formatDate(event.startDate)}
                </Text>
                <Text style={styles.infoSubValue}>
                  {formatTime(event.startDate)} - {formatTime(event.endDate)}
                </Text>
              </View>
            </View>
          </View>

          {/* Location */}
          <View style={styles.infoSection}>
            <View style={styles.infoRow}>
              <Icon name="location-on" size={20} color={colors.primary} />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Mahali</Text>
                <Text style={styles.infoValue}>{event.location}</Text>
                {event.address && (
                  <Text style={styles.infoSubValue}>{event.address}</Text>
                )}
              </View>
              <TouchableOpacity onPress={handleGetDirections}>
                <Icon name="directions" size={20} color={colors.primary} />
              </TouchableOpacity>
            </View>
          </View>

          {/* Price */}
          <View style={styles.infoSection}>
            <View style={styles.infoRow}>
              <Icon name="local-offer" size={20} color={colors.primary} />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Bei ya Tiketi</Text>
                <Text style={styles.priceValue}>
                  {event.ticketPrice > 0 ? formatCurrency(event.ticketPrice) : 'Bure'}
                </Text>
              </View>
            </View>
          </View>

          {/* Attendees */}
          <View style={styles.infoSection}>
            <View style={styles.infoRow}>
              <Icon name="people" size={20} color={colors.primary} />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Wahudhuriaji</Text>
                <Text style={styles.infoValue}>
                  {event.attendeesCount} wamejiandikisha
                </Text>
                {event.maxAttendees && (
                  <Text style={styles.infoSubValue}>
                    Kiwango cha juu: {event.maxAttendees}
                  </Text>
                )}
              </View>
            </View>
          </View>

          {/* Description */}
          <View style={styles.descriptionSection}>
            <Text style={styles.sectionTitle}>Maelezo ya Tukio</Text>
            <Text style={styles.description}>{event.description}</Text>
          </View>

          {/* Requirements */}
          {event.requirements && (
            <View style={styles.requirementsSection}>
              <Text style={styles.sectionTitle}>Mahitaji</Text>
              <Text style={styles.requirements}>{event.requirements}</Text>
            </View>
          )}

          {/* Contact Info */}
          {event.contactInfo && (
            <View style={styles.contactSection}>
              <Text style={styles.sectionTitle}>Mawasiliano</Text>
              <Text style={styles.contactInfo}>{event.contactInfo}</Text>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Bottom Action Buttons */}
      <View style={styles.bottomActions}>
        {event.ticketPrice > 0 ? (
          <TouchableOpacity style={styles.buyButton} onPress={handleBuyTicket}>
            <Icon name="confirmation-number" size={20} color={colors.white} />
            <Text style={styles.buyButtonText}>
              Nunua Tiketi - {formatCurrency(event.ticketPrice)}
            </Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity 
            style={[
              styles.attendButton,
              isAttending && styles.attendingButton
            ]} 
            onPress={handleAttendEvent}
          >
            <Icon 
              name={isAttending ? "check-circle" : "event-available"} 
              size={20} 
              color={colors.white} 
            />
            <Text style={styles.attendButtonText}>
              {isAttending ? 'Umejiandikisha' : 'Jiandikishe'}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  imageContainer: {
    position: 'relative',
    height: 250,
  },
  eventImage: {
    width: '100%',
    height: '100%',
  },
  headerButtons: {
    position: 'absolute',
    top: spacing.lg,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerRightButtons: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  categoryBadge: {
    position: 'absolute',
    bottom: spacing.lg,
    left: spacing.lg,
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
  },
  categoryText: {
    color: colors.white,
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
  },
  content: {
    padding: spacing.lg,
  },
  titleSection: {
    marginBottom: spacing.lg,
  },
  eventTitle: {
    fontSize: typography.fontSize.xxxl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  eventOrganizer: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
  },
  infoSection: {
    marginBottom: spacing.lg,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  infoContent: {
    flex: 1,
    marginLeft: spacing.md,
  },
  infoLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  infoValue: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
  },
  infoSubValue: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  priceValue: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.success,
  },
  descriptionSection: {
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  description: {
    fontSize: typography.fontSize.md,
    color: colors.text,
    lineHeight: 24,
  },
  requirementsSection: {
    marginBottom: spacing.lg,
  },
  requirements: {
    fontSize: typography.fontSize.md,
    color: colors.text,
    lineHeight: 24,
  },
  contactSection: {
    marginBottom: spacing.lg,
  },
  contactInfo: {
    fontSize: typography.fontSize.md,
    color: colors.text,
    lineHeight: 24,
  },
  bottomActions: {
    padding: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    backgroundColor: colors.white,
  },
  buyButton: {
    backgroundColor: colors.primary,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.lg,
    borderRadius: 12,
  },
  buyButtonText: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.white,
    marginLeft: spacing.sm,
  },
  attendButton: {
    backgroundColor: colors.success,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.lg,
    borderRadius: 12,
  },
  attendingButton: {
    backgroundColor: colors.gray,
  },
  attendButtonText: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.white,
    marginLeft: spacing.sm,
  },
});
