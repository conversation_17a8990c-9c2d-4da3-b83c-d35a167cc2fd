{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M4 6c0-1.1.9-2 2-2s2 .9 2 2-.9 2-2 2-2-.9-2-2m9 16c-.56 0-1.02-.44-1.05-1l-.45-9L8 11H2c-.55 0-1-.45-1-1s.45-1 1-1h5l6.26-4.47c.42-.3 1-.23 1.34.16.38.45.3 1.12-.18 1.46L11.14 8.5H14l7.09-4.09c.41-.24.93-.15 1.24.21.36.43.3 1.07-.14 1.41L14.5 12l-.45 9c-.03.56-.49 1-1.05 1\"\n}), 'SportsGymnasticsRounded');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/@mui/icons-material/esm/SportsGymnasticsRounded.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"M4 6c0-1.1.9-2 2-2s2 .9 2 2-.9 2-2 2-2-.9-2-2m9 16c-.56 0-1.02-.44-1.05-1l-.45-9L8 11H2c-.55 0-1-.45-1-1s.45-1 1-1h5l6.26-4.47c.42-.3 1-.23 1.34.16.38.45.3 1.12-.18 1.46L11.14 8.5H14l7.09-4.09c.41-.24.93-.15 1.24.21.36.43.3 1.07-.14 1.41L14.5 12l-.45 9c-.03.56-.49 1-1.05 1\"\n}), 'SportsGymnasticsRounded');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAE,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,yBAAyB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}