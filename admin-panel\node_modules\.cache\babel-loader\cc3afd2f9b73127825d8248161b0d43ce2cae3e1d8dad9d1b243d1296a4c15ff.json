{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTooltipUtilityClass(slot) {\n  return generateUtilityClass('MuiTooltip', slot);\n}\nconst tooltipClasses = generateUtilityClasses('MuiTooltip', ['popper', 'popperInteractive', 'popperArrow', 'popperClose', 'tooltip', 'tooltipArrow', 'touch', 'tooltipPlacementLeft', 'tooltipPlacementRight', 'tooltipPlacementTop', 'tooltipPlacementBottom', 'arrow']);\nexport default tooltipClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getTooltipUtilityClass", "slot", "tooltipClasses"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/@mui/material/Tooltip/tooltipClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTooltipUtilityClass(slot) {\n  return generateUtilityClass('MuiTooltip', slot);\n}\nconst tooltipClasses = generateUtilityClasses('MuiTooltip', ['popper', 'popperInteractive', 'popperArrow', 'popperClose', 'tooltip', 'tooltipArrow', 'touch', 'tooltipPlacementLeft', 'tooltipPlacementRight', 'tooltipPlacementTop', 'tooltipPlacementBottom', 'arrow']);\nexport default tooltipClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,sBAAsBA,CAACC,IAAI,EAAE;EAC3C,OAAOF,oBAAoB,CAAC,YAAY,EAAEE,IAAI,CAAC;AACjD;AACA,MAAMC,cAAc,GAAGJ,sBAAsB,CAAC,YAAY,EAAE,CAAC,QAAQ,EAAE,mBAAmB,EAAE,aAAa,EAAE,aAAa,EAAE,SAAS,EAAE,cAAc,EAAE,OAAO,EAAE,sBAAsB,EAAE,uBAAuB,EAAE,qBAAqB,EAAE,wBAAwB,EAAE,OAAO,CAAC,CAAC;AACzQ,eAAeI,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}