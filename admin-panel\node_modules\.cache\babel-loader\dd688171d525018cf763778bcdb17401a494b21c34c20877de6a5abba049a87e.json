{"ast": null, "code": "import array from \"./array.js\";\nimport constant from \"./constant.js\";\nimport curveLinear from \"./curve/linear.js\";\nimport line from \"./line.js\";\nimport { withPath } from \"./path.js\";\nimport { x as pointX, y as pointY } from \"./point.js\";\nexport default function (x0, y0, y1) {\n  var x1 = null,\n    defined = constant(true),\n    context = null,\n    curve = curveLinear,\n    output = null,\n    path = withPath(area);\n  x0 = typeof x0 === \"function\" ? x0 : x0 === undefined ? pointX : constant(+x0);\n  y0 = typeof y0 === \"function\" ? y0 : y0 === undefined ? constant(0) : constant(+y0);\n  y1 = typeof y1 === \"function\" ? y1 : y1 === undefined ? pointY : constant(+y1);\n  function area(data) {\n    var i,\n      j,\n      k,\n      n = (data = array(data)).length,\n      d,\n      defined0 = false,\n      buffer,\n      x0z = new Array(n),\n      y0z = new Array(n);\n    if (context == null) output = curve(buffer = path());\n    for (i = 0; i <= n; ++i) {\n      if (!(i < n && defined(d = data[i], i, data)) === defined0) {\n        if (defined0 = !defined0) {\n          j = i;\n          output.areaStart();\n          output.lineStart();\n        } else {\n          output.lineEnd();\n          output.lineStart();\n          for (k = i - 1; k >= j; --k) {\n            output.point(x0z[k], y0z[k]);\n          }\n          output.lineEnd();\n          output.areaEnd();\n        }\n      }\n      if (defined0) {\n        x0z[i] = +x0(d, i, data), y0z[i] = +y0(d, i, data);\n        output.point(x1 ? +x1(d, i, data) : x0z[i], y1 ? +y1(d, i, data) : y0z[i]);\n      }\n    }\n    if (buffer) return output = null, buffer + \"\" || null;\n  }\n  function arealine() {\n    return line().defined(defined).curve(curve).context(context);\n  }\n  area.x = function (_) {\n    return arguments.length ? (x0 = typeof _ === \"function\" ? _ : constant(+_), x1 = null, area) : x0;\n  };\n  area.x0 = function (_) {\n    return arguments.length ? (x0 = typeof _ === \"function\" ? _ : constant(+_), area) : x0;\n  };\n  area.x1 = function (_) {\n    return arguments.length ? (x1 = _ == null ? null : typeof _ === \"function\" ? _ : constant(+_), area) : x1;\n  };\n  area.y = function (_) {\n    return arguments.length ? (y0 = typeof _ === \"function\" ? _ : constant(+_), y1 = null, area) : y0;\n  };\n  area.y0 = function (_) {\n    return arguments.length ? (y0 = typeof _ === \"function\" ? _ : constant(+_), area) : y0;\n  };\n  area.y1 = function (_) {\n    return arguments.length ? (y1 = _ == null ? null : typeof _ === \"function\" ? _ : constant(+_), area) : y1;\n  };\n  area.lineX0 = area.lineY0 = function () {\n    return arealine().x(x0).y(y0);\n  };\n  area.lineY1 = function () {\n    return arealine().x(x0).y(y1);\n  };\n  area.lineX1 = function () {\n    return arealine().x(x1).y(y0);\n  };\n  area.defined = function (_) {\n    return arguments.length ? (defined = typeof _ === \"function\" ? _ : constant(!!_), area) : defined;\n  };\n  area.curve = function (_) {\n    return arguments.length ? (curve = _, context != null && (output = curve(context)), area) : curve;\n  };\n  area.context = function (_) {\n    return arguments.length ? (_ == null ? context = output = null : output = curve(context = _), area) : context;\n  };\n  return area;\n}", "map": {"version": 3, "names": ["array", "constant", "curveLinear", "line", "with<PERSON><PERSON>", "x", "pointX", "y", "pointY", "x0", "y0", "y1", "x1", "defined", "context", "curve", "output", "path", "area", "undefined", "data", "i", "j", "k", "n", "length", "d", "defined0", "buffer", "x0z", "Array", "y0z", "areaStart", "lineStart", "lineEnd", "point", "areaEnd", "arealine", "_", "arguments", "lineX0", "lineY0", "lineY1", "lineX1"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/d3-shape/src/area.js"], "sourcesContent": ["import array from \"./array.js\";\nimport constant from \"./constant.js\";\nimport curveLinear from \"./curve/linear.js\";\nimport line from \"./line.js\";\nimport {withPath} from \"./path.js\";\nimport {x as pointX, y as pointY} from \"./point.js\";\n\nexport default function(x0, y0, y1) {\n  var x1 = null,\n      defined = constant(true),\n      context = null,\n      curve = curveLinear,\n      output = null,\n      path = withPath(area);\n\n  x0 = typeof x0 === \"function\" ? x0 : (x0 === undefined) ? pointX : constant(+x0);\n  y0 = typeof y0 === \"function\" ? y0 : (y0 === undefined) ? constant(0) : constant(+y0);\n  y1 = typeof y1 === \"function\" ? y1 : (y1 === undefined) ? pointY : constant(+y1);\n\n  function area(data) {\n    var i,\n        j,\n        k,\n        n = (data = array(data)).length,\n        d,\n        defined0 = false,\n        buffer,\n        x0z = new Array(n),\n        y0z = new Array(n);\n\n    if (context == null) output = curve(buffer = path());\n\n    for (i = 0; i <= n; ++i) {\n      if (!(i < n && defined(d = data[i], i, data)) === defined0) {\n        if (defined0 = !defined0) {\n          j = i;\n          output.areaStart();\n          output.lineStart();\n        } else {\n          output.lineEnd();\n          output.lineStart();\n          for (k = i - 1; k >= j; --k) {\n            output.point(x0z[k], y0z[k]);\n          }\n          output.lineEnd();\n          output.areaEnd();\n        }\n      }\n      if (defined0) {\n        x0z[i] = +x0(d, i, data), y0z[i] = +y0(d, i, data);\n        output.point(x1 ? +x1(d, i, data) : x0z[i], y1 ? +y1(d, i, data) : y0z[i]);\n      }\n    }\n\n    if (buffer) return output = null, buffer + \"\" || null;\n  }\n\n  function arealine() {\n    return line().defined(defined).curve(curve).context(context);\n  }\n\n  area.x = function(_) {\n    return arguments.length ? (x0 = typeof _ === \"function\" ? _ : constant(+_), x1 = null, area) : x0;\n  };\n\n  area.x0 = function(_) {\n    return arguments.length ? (x0 = typeof _ === \"function\" ? _ : constant(+_), area) : x0;\n  };\n\n  area.x1 = function(_) {\n    return arguments.length ? (x1 = _ == null ? null : typeof _ === \"function\" ? _ : constant(+_), area) : x1;\n  };\n\n  area.y = function(_) {\n    return arguments.length ? (y0 = typeof _ === \"function\" ? _ : constant(+_), y1 = null, area) : y0;\n  };\n\n  area.y0 = function(_) {\n    return arguments.length ? (y0 = typeof _ === \"function\" ? _ : constant(+_), area) : y0;\n  };\n\n  area.y1 = function(_) {\n    return arguments.length ? (y1 = _ == null ? null : typeof _ === \"function\" ? _ : constant(+_), area) : y1;\n  };\n\n  area.lineX0 =\n  area.lineY0 = function() {\n    return arealine().x(x0).y(y0);\n  };\n\n  area.lineY1 = function() {\n    return arealine().x(x0).y(y1);\n  };\n\n  area.lineX1 = function() {\n    return arealine().x(x1).y(y0);\n  };\n\n  area.defined = function(_) {\n    return arguments.length ? (defined = typeof _ === \"function\" ? _ : constant(!!_), area) : defined;\n  };\n\n  area.curve = function(_) {\n    return arguments.length ? (curve = _, context != null && (output = curve(context)), area) : curve;\n  };\n\n  area.context = function(_) {\n    return arguments.length ? (_ == null ? context = output = null : output = curve(context = _), area) : context;\n  };\n\n  return area;\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,YAAY;AAC9B,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,IAAI,MAAM,WAAW;AAC5B,SAAQC,QAAQ,QAAO,WAAW;AAClC,SAAQC,CAAC,IAAIC,MAAM,EAAEC,CAAC,IAAIC,MAAM,QAAO,YAAY;AAEnD,eAAe,UAASC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAClC,IAAIC,EAAE,GAAG,IAAI;IACTC,OAAO,GAAGZ,QAAQ,CAAC,IAAI,CAAC;IACxBa,OAAO,GAAG,IAAI;IACdC,KAAK,GAAGb,WAAW;IACnBc,MAAM,GAAG,IAAI;IACbC,IAAI,GAAGb,QAAQ,CAACc,IAAI,CAAC;EAEzBT,EAAE,GAAG,OAAOA,EAAE,KAAK,UAAU,GAAGA,EAAE,GAAIA,EAAE,KAAKU,SAAS,GAAIb,MAAM,GAAGL,QAAQ,CAAC,CAACQ,EAAE,CAAC;EAChFC,EAAE,GAAG,OAAOA,EAAE,KAAK,UAAU,GAAGA,EAAE,GAAIA,EAAE,KAAKS,SAAS,GAAIlB,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAACS,EAAE,CAAC;EACrFC,EAAE,GAAG,OAAOA,EAAE,KAAK,UAAU,GAAGA,EAAE,GAAIA,EAAE,KAAKQ,SAAS,GAAIX,MAAM,GAAGP,QAAQ,CAAC,CAACU,EAAE,CAAC;EAEhF,SAASO,IAAIA,CAACE,IAAI,EAAE;IAClB,IAAIC,CAAC;MACDC,CAAC;MACDC,CAAC;MACDC,CAAC,GAAG,CAACJ,IAAI,GAAGpB,KAAK,CAACoB,IAAI,CAAC,EAAEK,MAAM;MAC/BC,CAAC;MACDC,QAAQ,GAAG,KAAK;MAChBC,MAAM;MACNC,GAAG,GAAG,IAAIC,KAAK,CAACN,CAAC,CAAC;MAClBO,GAAG,GAAG,IAAID,KAAK,CAACN,CAAC,CAAC;IAEtB,IAAIV,OAAO,IAAI,IAAI,EAAEE,MAAM,GAAGD,KAAK,CAACa,MAAM,GAAGX,IAAI,CAAC,CAAC,CAAC;IAEpD,KAAKI,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIG,CAAC,EAAE,EAAEH,CAAC,EAAE;MACvB,IAAI,EAAEA,CAAC,GAAGG,CAAC,IAAIX,OAAO,CAACa,CAAC,GAAGN,IAAI,CAACC,CAAC,CAAC,EAAEA,CAAC,EAAED,IAAI,CAAC,CAAC,KAAKO,QAAQ,EAAE;QAC1D,IAAIA,QAAQ,GAAG,CAACA,QAAQ,EAAE;UACxBL,CAAC,GAAGD,CAAC;UACLL,MAAM,CAACgB,SAAS,CAAC,CAAC;UAClBhB,MAAM,CAACiB,SAAS,CAAC,CAAC;QACpB,CAAC,MAAM;UACLjB,MAAM,CAACkB,OAAO,CAAC,CAAC;UAChBlB,MAAM,CAACiB,SAAS,CAAC,CAAC;UAClB,KAAKV,CAAC,GAAGF,CAAC,GAAG,CAAC,EAAEE,CAAC,IAAID,CAAC,EAAE,EAAEC,CAAC,EAAE;YAC3BP,MAAM,CAACmB,KAAK,CAACN,GAAG,CAACN,CAAC,CAAC,EAAEQ,GAAG,CAACR,CAAC,CAAC,CAAC;UAC9B;UACAP,MAAM,CAACkB,OAAO,CAAC,CAAC;UAChBlB,MAAM,CAACoB,OAAO,CAAC,CAAC;QAClB;MACF;MACA,IAAIT,QAAQ,EAAE;QACZE,GAAG,CAACR,CAAC,CAAC,GAAG,CAACZ,EAAE,CAACiB,CAAC,EAAEL,CAAC,EAAED,IAAI,CAAC,EAAEW,GAAG,CAACV,CAAC,CAAC,GAAG,CAACX,EAAE,CAACgB,CAAC,EAAEL,CAAC,EAAED,IAAI,CAAC;QAClDJ,MAAM,CAACmB,KAAK,CAACvB,EAAE,GAAG,CAACA,EAAE,CAACc,CAAC,EAAEL,CAAC,EAAED,IAAI,CAAC,GAAGS,GAAG,CAACR,CAAC,CAAC,EAAEV,EAAE,GAAG,CAACA,EAAE,CAACe,CAAC,EAAEL,CAAC,EAAED,IAAI,CAAC,GAAGW,GAAG,CAACV,CAAC,CAAC,CAAC;MAC5E;IACF;IAEA,IAAIO,MAAM,EAAE,OAAOZ,MAAM,GAAG,IAAI,EAAEY,MAAM,GAAG,EAAE,IAAI,IAAI;EACvD;EAEA,SAASS,QAAQA,CAAA,EAAG;IAClB,OAAOlC,IAAI,CAAC,CAAC,CAACU,OAAO,CAACA,OAAO,CAAC,CAACE,KAAK,CAACA,KAAK,CAAC,CAACD,OAAO,CAACA,OAAO,CAAC;EAC9D;EAEAI,IAAI,CAACb,CAAC,GAAG,UAASiC,CAAC,EAAE;IACnB,OAAOC,SAAS,CAACd,MAAM,IAAIhB,EAAE,GAAG,OAAO6B,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGrC,QAAQ,CAAC,CAACqC,CAAC,CAAC,EAAE1B,EAAE,GAAG,IAAI,EAAEM,IAAI,IAAIT,EAAE;EACnG,CAAC;EAEDS,IAAI,CAACT,EAAE,GAAG,UAAS6B,CAAC,EAAE;IACpB,OAAOC,SAAS,CAACd,MAAM,IAAIhB,EAAE,GAAG,OAAO6B,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGrC,QAAQ,CAAC,CAACqC,CAAC,CAAC,EAAEpB,IAAI,IAAIT,EAAE;EACxF,CAAC;EAEDS,IAAI,CAACN,EAAE,GAAG,UAAS0B,CAAC,EAAE;IACpB,OAAOC,SAAS,CAACd,MAAM,IAAIb,EAAE,GAAG0B,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,OAAOA,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGrC,QAAQ,CAAC,CAACqC,CAAC,CAAC,EAAEpB,IAAI,IAAIN,EAAE;EAC3G,CAAC;EAEDM,IAAI,CAACX,CAAC,GAAG,UAAS+B,CAAC,EAAE;IACnB,OAAOC,SAAS,CAACd,MAAM,IAAIf,EAAE,GAAG,OAAO4B,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGrC,QAAQ,CAAC,CAACqC,CAAC,CAAC,EAAE3B,EAAE,GAAG,IAAI,EAAEO,IAAI,IAAIR,EAAE;EACnG,CAAC;EAEDQ,IAAI,CAACR,EAAE,GAAG,UAAS4B,CAAC,EAAE;IACpB,OAAOC,SAAS,CAACd,MAAM,IAAIf,EAAE,GAAG,OAAO4B,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGrC,QAAQ,CAAC,CAACqC,CAAC,CAAC,EAAEpB,IAAI,IAAIR,EAAE;EACxF,CAAC;EAEDQ,IAAI,CAACP,EAAE,GAAG,UAAS2B,CAAC,EAAE;IACpB,OAAOC,SAAS,CAACd,MAAM,IAAId,EAAE,GAAG2B,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,OAAOA,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGrC,QAAQ,CAAC,CAACqC,CAAC,CAAC,EAAEpB,IAAI,IAAIP,EAAE;EAC3G,CAAC;EAEDO,IAAI,CAACsB,MAAM,GACXtB,IAAI,CAACuB,MAAM,GAAG,YAAW;IACvB,OAAOJ,QAAQ,CAAC,CAAC,CAAChC,CAAC,CAACI,EAAE,CAAC,CAACF,CAAC,CAACG,EAAE,CAAC;EAC/B,CAAC;EAEDQ,IAAI,CAACwB,MAAM,GAAG,YAAW;IACvB,OAAOL,QAAQ,CAAC,CAAC,CAAChC,CAAC,CAACI,EAAE,CAAC,CAACF,CAAC,CAACI,EAAE,CAAC;EAC/B,CAAC;EAEDO,IAAI,CAACyB,MAAM,GAAG,YAAW;IACvB,OAAON,QAAQ,CAAC,CAAC,CAAChC,CAAC,CAACO,EAAE,CAAC,CAACL,CAAC,CAACG,EAAE,CAAC;EAC/B,CAAC;EAEDQ,IAAI,CAACL,OAAO,GAAG,UAASyB,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACd,MAAM,IAAIZ,OAAO,GAAG,OAAOyB,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAACqC,CAAC,CAAC,EAAEpB,IAAI,IAAIL,OAAO;EACnG,CAAC;EAEDK,IAAI,CAACH,KAAK,GAAG,UAASuB,CAAC,EAAE;IACvB,OAAOC,SAAS,CAACd,MAAM,IAAIV,KAAK,GAAGuB,CAAC,EAAExB,OAAO,IAAI,IAAI,KAAKE,MAAM,GAAGD,KAAK,CAACD,OAAO,CAAC,CAAC,EAAEI,IAAI,IAAIH,KAAK;EACnG,CAAC;EAEDG,IAAI,CAACJ,OAAO,GAAG,UAASwB,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACd,MAAM,IAAIa,CAAC,IAAI,IAAI,GAAGxB,OAAO,GAAGE,MAAM,GAAG,IAAI,GAAGA,MAAM,GAAGD,KAAK,CAACD,OAAO,GAAGwB,CAAC,CAAC,EAAEpB,IAAI,IAAIJ,OAAO;EAC/G,CAAC;EAED,OAAOI,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}