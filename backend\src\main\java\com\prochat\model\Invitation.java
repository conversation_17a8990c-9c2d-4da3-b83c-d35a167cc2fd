package com.prochat.model;

import jakarta.persistence.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "invitations")
@EntityListeners(AuditingEntityListener.class)
public class Invitation {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "inviter_id", nullable = false)
    private User inviter;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "invitee_id")
    private User invitee; // Null until someone joins
    
    @Column(name = "invitation_code", unique = true, nullable = false)
    private String invitationCode;
    
    @Column(name = "invitation_link", nullable = false)
    private String invitationLink;
    
    @Column(name = "invitee_phone")
    private String inviteePhone;
    
    @Column(name = "invitee_email")
    private String inviteeEmail;
    
    @Column(name = "invitee_name")
    private String inviteeName;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private InvitationStatus status = InvitationStatus.SENT;
    
    @Column(name = "bonus_amount", precision = 15, scale = 2)
    private BigDecimal bonusAmount = new BigDecimal("5000.00"); // 5000 TSH default
    
    @Column(name = "inviter_bonus", precision = 15, scale = 2)
    private BigDecimal inviterBonus = new BigDecimal("2000.00"); // 2000 TSH for inviter
    
    @Column(name = "invitee_bonus", precision = 15, scale = 2)
    private BigDecimal inviteeBonus = new BigDecimal("3000.00"); // 3000 TSH for invitee
    
    @Column(name = "bonus_paid")
    private Boolean bonusPaid = false;
    
    @Column(name = "bonus_paid_at")
    private LocalDateTime bonusPaidAt;
    
    @Column(name = "joined_at")
    private LocalDateTime joinedAt;
    
    @Column(name = "expires_at")
    private LocalDateTime expiresAt;
    
    @Column(name = "message", columnDefinition = "TEXT")
    private String message;
    
    @Column(name = "invitation_method")
    private String invitationMethod; // SMS, EMAIL, WHATSAPP, DIRECT_LINK
    
    @Column(name = "campaign_id")
    private String campaignId; // For tracking different campaigns
    
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Constructors
    public Invitation() {}
    
    public Invitation(User inviter, String invitationCode, String invitationLink) {
        this.inviter = inviter;
        this.invitationCode = invitationCode;
        this.invitationLink = invitationLink;
        this.expiresAt = LocalDateTime.now().plusDays(30); // Expires in 30 days
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public User getInviter() { return inviter; }
    public void setInviter(User inviter) { this.inviter = inviter; }
    
    public User getInvitee() { return invitee; }
    public void setInvitee(User invitee) { this.invitee = invitee; }
    
    public String getInvitationCode() { return invitationCode; }
    public void setInvitationCode(String invitationCode) { this.invitationCode = invitationCode; }
    
    public String getInvitationLink() { return invitationLink; }
    public void setInvitationLink(String invitationLink) { this.invitationLink = invitationLink; }
    
    public String getInviteePhone() { return inviteePhone; }
    public void setInviteePhone(String inviteePhone) { this.inviteePhone = inviteePhone; }
    
    public String getInviteeEmail() { return inviteeEmail; }
    public void setInviteeEmail(String inviteeEmail) { this.inviteeEmail = inviteeEmail; }
    
    public String getInviteeName() { return inviteeName; }
    public void setInviteeName(String inviteeName) { this.inviteeName = inviteeName; }
    
    public InvitationStatus getStatus() { return status; }
    public void setStatus(InvitationStatus status) { this.status = status; }
    
    public BigDecimal getBonusAmount() { return bonusAmount; }
    public void setBonusAmount(BigDecimal bonusAmount) { this.bonusAmount = bonusAmount; }
    
    public BigDecimal getInviterBonus() { return inviterBonus; }
    public void setInviterBonus(BigDecimal inviterBonus) { this.inviterBonus = inviterBonus; }
    
    public BigDecimal getInviteeBonus() { return inviteeBonus; }
    public void setInviteeBonus(BigDecimal inviteeBonus) { this.inviteeBonus = inviteeBonus; }
    
    public Boolean getBonusPaid() { return bonusPaid; }
    public void setBonusPaid(Boolean bonusPaid) { this.bonusPaid = bonusPaid; }
    
    public LocalDateTime getBonusPaidAt() { return bonusPaidAt; }
    public void setBonusPaidAt(LocalDateTime bonusPaidAt) { this.bonusPaidAt = bonusPaidAt; }
    
    public LocalDateTime getJoinedAt() { return joinedAt; }
    public void setJoinedAt(LocalDateTime joinedAt) { this.joinedAt = joinedAt; }
    
    public LocalDateTime getExpiresAt() { return expiresAt; }
    public void setExpiresAt(LocalDateTime expiresAt) { this.expiresAt = expiresAt; }
    
    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }
    
    public String getInvitationMethod() { return invitationMethod; }
    public void setInvitationMethod(String invitationMethod) { this.invitationMethod = invitationMethod; }
    
    public String getCampaignId() { return campaignId; }
    public void setCampaignId(String campaignId) { this.campaignId = campaignId; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
