package com.prochat.repository;

import com.prochat.model.Transaction;
import com.prochat.model.TransactionStatus;
import com.prochat.model.TransactionType;
import com.prochat.model.Wallet;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface TransactionRepository extends JpaRepository<Transaction, Long> {
    
    Optional<Transaction> findByTransactionId(String transactionId);
    
    Page<Transaction> findBySenderWalletOrReceiverWalletOrderByCreatedAtDesc(
            Wallet senderWallet, Wallet receiverWallet, Pageable pageable);
    
    List<Transaction> findBySenderWalletAndStatus(Wallet senderWallet, TransactionStatus status);
    
    List<Transaction> findByReceiverWalletAndStatus(Wallet receiverWallet, TransactionStatus status);
    
    @Query("SELECT t FROM Transaction t WHERE (t.senderWallet = :wallet OR t.receiverWallet = :wallet) " +
           "AND t.createdAt BETWEEN :startDate AND :endDate ORDER BY t.createdAt DESC")
    List<Transaction> findWalletTransactionsBetweenDates(
            @Param("wallet") Wallet wallet,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT SUM(t.amount) FROM Transaction t WHERE t.senderWallet = :wallet " +
           "AND t.status = 'COMPLETED' AND t.createdAt BETWEEN :startDate AND :endDate")
    BigDecimal getTotalSentAmount(@Param("wallet") Wallet wallet,
                                 @Param("startDate") LocalDateTime startDate,
                                 @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT SUM(t.amount) FROM Transaction t WHERE t.receiverWallet = :wallet " +
           "AND t.status = 'COMPLETED' AND t.createdAt BETWEEN :startDate AND :endDate")
    BigDecimal getTotalReceivedAmount(@Param("wallet") Wallet wallet,
                                     @Param("startDate") LocalDateTime startDate,
                                     @Param("endDate") LocalDateTime endDate);
    
    List<Transaction> findByTypeAndStatus(TransactionType type, TransactionStatus status);
}
