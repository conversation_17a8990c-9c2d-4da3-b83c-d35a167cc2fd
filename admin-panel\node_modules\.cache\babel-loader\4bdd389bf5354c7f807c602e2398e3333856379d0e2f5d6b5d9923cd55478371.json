{"ast": null, "code": "var baseMatches = require('./_baseMatches'),\n  baseMatchesProperty = require('./_baseMatchesProperty'),\n  identity = require('./identity'),\n  isArray = require('./isArray'),\n  property = require('./property');\n\n/**\n * The base implementation of `_.iteratee`.\n *\n * @private\n * @param {*} [value=_.identity] The value to convert to an iteratee.\n * @returns {Function} Returns the iteratee.\n */\nfunction baseIteratee(value) {\n  // Don't store the `typeof` result in a variable to avoid a JIT bug in Safari 9.\n  // See https://bugs.webkit.org/show_bug.cgi?id=156034 for more details.\n  if (typeof value == 'function') {\n    return value;\n  }\n  if (value == null) {\n    return identity;\n  }\n  if (typeof value == 'object') {\n    return isArray(value) ? baseMatchesProperty(value[0], value[1]) : baseMatches(value);\n  }\n  return property(value);\n}\nmodule.exports = baseIteratee;", "map": {"version": 3, "names": ["baseMatches", "require", "baseMatchesProperty", "identity", "isArray", "property", "baseIteratee", "value", "module", "exports"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/lodash/_baseIteratee.js"], "sourcesContent": ["var baseMatches = require('./_baseMatches'),\n    baseMatchesProperty = require('./_baseMatchesProperty'),\n    identity = require('./identity'),\n    isArray = require('./isArray'),\n    property = require('./property');\n\n/**\n * The base implementation of `_.iteratee`.\n *\n * @private\n * @param {*} [value=_.identity] The value to convert to an iteratee.\n * @returns {Function} Returns the iteratee.\n */\nfunction baseIteratee(value) {\n  // Don't store the `typeof` result in a variable to avoid a JIT bug in Safari 9.\n  // See https://bugs.webkit.org/show_bug.cgi?id=156034 for more details.\n  if (typeof value == 'function') {\n    return value;\n  }\n  if (value == null) {\n    return identity;\n  }\n  if (typeof value == 'object') {\n    return isArray(value)\n      ? baseMatchesProperty(value[0], value[1])\n      : baseMatches(value);\n  }\n  return property(value);\n}\n\nmodule.exports = baseIteratee;\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,gBAAgB,CAAC;EACvCC,mBAAmB,GAAGD,OAAO,CAAC,wBAAwB,CAAC;EACvDE,QAAQ,GAAGF,OAAO,CAAC,YAAY,CAAC;EAChCG,OAAO,GAAGH,OAAO,CAAC,WAAW,CAAC;EAC9BI,QAAQ,GAAGJ,OAAO,CAAC,YAAY,CAAC;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,YAAYA,CAACC,KAAK,EAAE;EAC3B;EACA;EACA,IAAI,OAAOA,KAAK,IAAI,UAAU,EAAE;IAC9B,OAAOA,KAAK;EACd;EACA,IAAIA,KAAK,IAAI,IAAI,EAAE;IACjB,OAAOJ,QAAQ;EACjB;EACA,IAAI,OAAOI,KAAK,IAAI,QAAQ,EAAE;IAC5B,OAAOH,OAAO,CAACG,KAAK,CAAC,GACjBL,mBAAmB,CAACK,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC,GACvCP,WAAW,CAACO,KAAK,CAAC;EACxB;EACA,OAAOF,QAAQ,CAACE,KAAK,CAAC;AACxB;AAEAC,MAAM,CAACC,OAAO,GAAGH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}