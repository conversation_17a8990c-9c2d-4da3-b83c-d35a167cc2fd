import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  Card<PERSON>ontent,
  Button,
  Grid,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  InputAdornment,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Search,
  Support,
  CheckCircle,
  Cancel,
  MoreVert,
  Visibility,
  Reply,
  Flag,
  Assignment,
} from '@mui/icons-material';

export default function SupportTickets() {
  const [tickets] = useState([
    {
      id: 1,
      ticketNumber: 'TKT-001234',
      subject: 'Ta<PERSON><PERSON> la malipo',
      user: '<PERSON>',
      category: 'PAYMENT_ISSUE',
      priority: 'HIGH',
      status: 'OPEN',
      assignedTo: 'Mary Admin',
      createdAt: '2024-01-20T10:30:00',
      lastResponse: '2024-01-20T11:00:00',
    },
    {
      id: 2,
      ticketNumber: 'TKT-001235',
      subject: '<PERSON>wali kuhusu ProPay',
      user: '<PERSON>',
      category: 'PROPAY_WALLET',
      priority: 'MEDIUM',
      status: 'IN_PROGRESS',
      assignedTo: 'Peter Support',
      createdAt: '2024-01-20T09:15:00',
      lastResponse: '2024-01-20T10:30:00',
    },
    {
      id: 3,
      ticketNumber: 'TKT-001236',
      subject: 'Ripoti ya maudhui yasiyofaa',
      user: 'Bob Wilson',
      category: 'CONTENT_REPORT',
      priority: 'LOW',
      status: 'RESOLVED',
      assignedTo: 'Alice Moderator',
      createdAt: '2024-01-19T14:20:00',
      lastResponse: '2024-01-20T08:45:00',
    },
  ]);

  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchQuery, setSearchQuery] = useState('');
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [responseDialogOpen, setResponseDialogOpen] = useState(false);

  const handleActionClick = (event, ticket) => {
    setSelectedTicket(ticket);
    setAnchorEl(event.currentTarget);
  };

  const handleActionClose = () => {
    setAnchorEl(null);
    setSelectedTicket(null);
  };

  const handleResponse = () => {
    setResponseDialogOpen(true);
    handleActionClose();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'OPEN': return 'error';
      case 'IN_PROGRESS': return 'warning';
      case 'RESOLVED': return 'success';
      case 'CLOSED': return 'default';
      default: return 'default';
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'OPEN': return 'Wazi';
      case 'IN_PROGRESS': return 'Inaendelea';
      case 'RESOLVED': return 'Imetatuliwa';
      case 'CLOSED': return 'Imefungwa';
      default: return status;
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'HIGH': return 'error';
      case 'MEDIUM': return 'warning';
      case 'LOW': return 'success';
      case 'URGENT': return 'error';
      case 'CRITICAL': return 'error';
      default: return 'default';
    }
  };

  const getPriorityLabel = (priority) => {
    switch (priority) {
      case 'HIGH': return 'Juu';
      case 'MEDIUM': return 'Wastani';
      case 'LOW': return 'Chini';
      case 'URGENT': return 'Haraka';
      case 'CRITICAL': return 'Muhimu';
      default: return priority;
    }
  };

  const getCategoryLabel = (category) => {
    const categories = {
      PAYMENT_ISSUE: 'Tatizo la Malipo',
      PROPAY_WALLET: 'ProPay Wallet',
      CONTENT_REPORT: 'Ripoti ya Maudhui',
      TECHNICAL_ISSUE: 'Tatizo la Kiufundi',
      ACCOUNT_PROBLEM: 'Tatizo la Akaunti',
      GENERAL_INQUIRY: 'Swali la Jumla',
    };
    return categories[category] || category;
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Tiketi za Msaada
        </Typography>
        <Button variant="contained" startIcon={<Support />}>
          Unda Tiketi
        </Button>
      </Box>

      {/* Stats */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Tiketi za Jumla
              </Typography>
              <Typography variant="h4" color="primary">
                {tickets.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Wazi
              </Typography>
              <Typography variant="h4" color="error.main">
                {tickets.filter(t => t.status === 'OPEN').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Inaendelea
              </Typography>
              <Typography variant="h4" color="warning.main">
                {tickets.filter(t => t.status === 'IN_PROGRESS').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Imetatuliwa
              </Typography>
              <Typography variant="h4" color="success.main">
                {tickets.filter(t => t.status === 'RESOLVED').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <TextField
            fullWidth
            placeholder="Tafuta tiketi..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
          />
        </CardContent>
      </Card>

      {/* Tickets Table */}
      <Card>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Nambari ya Tiketi</TableCell>
                <TableCell>Mtumiaji</TableCell>
                <TableCell>Mada</TableCell>
                <TableCell>Aina</TableCell>
                <TableCell>Kipaumbele</TableCell>
                <TableCell>Hali</TableCell>
                <TableCell>Imepewa</TableCell>
                <TableCell>Tarehe</TableCell>
                <TableCell>Vitendo</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {tickets
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((ticket) => (
                  <TableRow key={ticket.id}>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {ticket.ticketNumber}
                      </Typography>
                    </TableCell>
                    <TableCell>{ticket.user}</TableCell>
                    <TableCell>
                      <Typography variant="body2" noWrap sx={{ maxWidth: 200 }}>
                        {ticket.subject}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getCategoryLabel(ticket.category)}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getPriorityLabel(ticket.priority)}
                        color={getPriorityColor(ticket.priority)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getStatusLabel(ticket.status)}
                        color={getStatusColor(ticket.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {ticket.assignedTo}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(ticket.createdAt).toLocaleDateString('sw-TZ')}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <IconButton onClick={(e) => handleActionClick(e, ticket)}>
                        <MoreVert />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={tickets.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={(e, newPage) => setPage(newPage)}
          onRowsPerPageChange={(e) => {
            setRowsPerPage(parseInt(e.target.value, 10));
            setPage(0);
          }}
        />
      </Card>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleActionClose}
      >
        <MenuItem onClick={handleActionClose}>
          <Visibility sx={{ mr: 1 }} /> Angalia
        </MenuItem>
        <MenuItem onClick={handleResponse}>
          <Reply sx={{ mr: 1 }} /> Jibu
        </MenuItem>
        <MenuItem onClick={handleActionClose}>
          <Assignment sx={{ mr: 1 }} /> Gawa
        </MenuItem>
        <MenuItem onClick={handleActionClose}>
          <Flag sx={{ mr: 1 }} /> Pandisha
        </MenuItem>
        <MenuItem onClick={handleActionClose}>
          <CheckCircle sx={{ mr: 1 }} /> Tatua
        </MenuItem>
      </Menu>

      {/* Response Dialog */}
      <Dialog 
        open={responseDialogOpen} 
        onClose={() => setResponseDialogOpen(false)} 
        maxWidth="md" 
        fullWidth
      >
        <DialogTitle>Jibu Tiketi</DialogTitle>
        <DialogContent>
          {selectedTicket && (
            <Box>
              <Typography variant="h6" gutterBottom>
                {selectedTicket.subject}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Tiketi: {selectedTicket.ticketNumber} | Mtumiaji: {selectedTicket.user}
              </Typography>
              <TextField
                fullWidth
                multiline
                rows={6}
                placeholder="Andika jibu lako hapa..."
                sx={{ mt: 2 }}
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setResponseDialogOpen(false)}>
            Ghairi
          </Button>
          <Button variant="contained">
            Tuma Jibu
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
