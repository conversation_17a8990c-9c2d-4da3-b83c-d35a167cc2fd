{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { GlobalStyles as MuiGlobalStyles } from '@mui/styled-engine';\nimport useTheme from '../useTheme';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction GlobalStyles({\n  styles,\n  themeId,\n  defaultTheme = {}\n}) {\n  const upperTheme = useTheme(defaultTheme);\n  const globalStyles = typeof styles === 'function' ? styles(themeId ? upperTheme[themeId] || upperTheme : upperTheme) : styles;\n  return /*#__PURE__*/_jsx(MuiGlobalStyles, {\n    styles: globalStyles\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GlobalStyles.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  defaultTheme: PropTypes.object,\n  /**\n   * @ignore\n   */\n  styles: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.array, PropTypes.func, PropTypes.number, PropTypes.object, PropTypes.string, PropTypes.bool]),\n  /**\n   * @ignore\n   */\n  themeId: PropTypes.string\n} : void 0;\nexport default GlobalStyles;", "map": {"version": 3, "names": ["React", "PropTypes", "GlobalStyles", "MuiGlobalStyles", "useTheme", "jsx", "_jsx", "styles", "themeId", "defaultTheme", "upperTheme", "globalStyles", "process", "env", "NODE_ENV", "propTypes", "object", "oneOfType", "array", "func", "number", "string", "bool"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/@mui/system/esm/GlobalStyles/GlobalStyles.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { GlobalStyles as MuiGlobalStyles } from '@mui/styled-engine';\nimport useTheme from '../useTheme';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction GlobalStyles({\n  styles,\n  themeId,\n  defaultTheme = {}\n}) {\n  const upperTheme = useTheme(defaultTheme);\n  const globalStyles = typeof styles === 'function' ? styles(themeId ? upperTheme[themeId] || upperTheme : upperTheme) : styles;\n  return /*#__PURE__*/_jsx(MuiGlobalStyles, {\n    styles: globalStyles\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GlobalStyles.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  defaultTheme: PropTypes.object,\n  /**\n   * @ignore\n   */\n  styles: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.array, PropTypes.func, PropTypes.number, PropTypes.object, PropTypes.string, PropTypes.bool]),\n  /**\n   * @ignore\n   */\n  themeId: PropTypes.string\n} : void 0;\nexport default GlobalStyles;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,YAAY,IAAIC,eAAe,QAAQ,oBAAoB;AACpE,OAAOC,QAAQ,MAAM,aAAa;AAClC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASJ,YAAYA,CAAC;EACpBK,MAAM;EACNC,OAAO;EACPC,YAAY,GAAG,CAAC;AAClB,CAAC,EAAE;EACD,MAAMC,UAAU,GAAGN,QAAQ,CAACK,YAAY,CAAC;EACzC,MAAME,YAAY,GAAG,OAAOJ,MAAM,KAAK,UAAU,GAAGA,MAAM,CAACC,OAAO,GAAGE,UAAU,CAACF,OAAO,CAAC,IAAIE,UAAU,GAAGA,UAAU,CAAC,GAAGH,MAAM;EAC7H,OAAO,aAAaD,IAAI,CAACH,eAAe,EAAE;IACxCI,MAAM,EAAEI;EACV,CAAC,CAAC;AACJ;AACAC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGZ,YAAY,CAACa,SAAS,CAAC,yBAAyB;EACtF;EACA;EACA;EACA;EACA;AACF;AACA;EACEN,YAAY,EAAER,SAAS,CAACe,MAAM;EAC9B;AACF;AACA;EACET,MAAM,EAAEN,SAAS,CAAC,sCAAsCgB,SAAS,CAAC,CAAChB,SAAS,CAACiB,KAAK,EAAEjB,SAAS,CAACkB,IAAI,EAAElB,SAAS,CAACmB,MAAM,EAAEnB,SAAS,CAACe,MAAM,EAAEf,SAAS,CAACoB,MAAM,EAAEpB,SAAS,CAACqB,IAAI,CAAC,CAAC;EAC1K;AACF;AACA;EACEd,OAAO,EAAEP,SAAS,CAACoB;AACrB,CAAC,GAAG,KAAK,CAAC;AACV,eAAenB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}