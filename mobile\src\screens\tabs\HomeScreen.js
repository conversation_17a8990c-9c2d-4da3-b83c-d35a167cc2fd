import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const { width } = Dimensions.get('window');

// Mock data for posts
const mockPosts = [
  {
    id: '1',
    user: {
      name: '<PERSON>',
      username: '@johnmwangi',
      avatar: 'https://via.placeholder.com/40',
      isVerified: true,
    },
    content: 'Habari za asubuhi! Leo ni siku nzuri ya kufanya kazi. #Motivation #Tanzania',
    images: ['https://via.placeholder.com/300x200'],
    timestamp: '2 saa zilizopita',
    likes: 45,
    comments: 12,
    shares: 8,
    gifts: 3,
    impressions: 234,
    isLiked: false,
  },
  {
    id: '2',
    user: {
      name: '<PERSON>',
      username: '@mary<PERSON><PERSON>',
      avatar: 'https://via.placeholder.com/40',
      isVerified: false,
    },
    content: 'Ni<PERSON>pata kazi mpya! Asante kwa wote mlionisaidia. 🎉',
    images: [],
    timestamp: '4 saa zilizopita',
    likes: 128,
    comments: 34,
    shares: 15,
    gifts: 7,
    impressions: 567,
    isLiked: true,
  },
  {
    id: '3',
    user: {
      name: 'ProChat Official',
      username: '@prochat',
      avatar: 'https://via.placeholder.com/40',
      isVerified: true,
    },
    content: 'Tumefurahi kuwasilisha kipengele kipya cha ProPay! Sasa unaweza kulipa bili zako moja kwa moja kwenye app. 💰',
    images: ['https://via.placeholder.com/300x200', 'https://via.placeholder.com/300x200'],
    timestamp: '6 saa zilizopita',
    likes: 89,
    comments: 23,
    shares: 45,
    gifts: 12,
    impressions: 890,
    isLiked: false,
  },
];

export default function HomeScreen({ navigation }) {
  const [posts, setPosts] = useState(mockPosts);
  const [refreshing, setRefreshing] = useState(false);

  const handleLike = (postId) => {
    setPosts(prevPosts =>
      prevPosts.map(post =>
        post.id === postId
          ? {
              ...post,
              isLiked: !post.isLiked,
              likes: post.isLiked ? post.likes - 1 : post.likes + 1,
            }
          : post
      )
    );
  };

  const handleRefresh = () => {
    setRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const renderPost = ({ item }) => (
    <View style={styles.postContainer}>
      {/* Post Header */}
      <View style={styles.postHeader}>
        <Image source={{ uri: item.user.avatar }} style={styles.userAvatar} />
        <View style={styles.userInfo}>
          <View style={styles.userNameContainer}>
            <Text style={styles.userName}>{item.user.name}</Text>
            {item.user.isVerified && (
              <Ionicons name="checkmark-circle" size={16} color="#007AFF" />
            )}
          </View>
          <Text style={styles.userHandle}>{item.user.username}</Text>
        </View>
        <View style={styles.postActions}>
          <Text style={styles.timestamp}>{item.timestamp}</Text>
          <TouchableOpacity style={styles.moreButton}>
            <Ionicons name="ellipsis-horizontal" size={20} color="#666" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Post Content */}
      <Text style={styles.postContent}>{item.content}</Text>

      {/* Post Images */}
      {item.images.length > 0 && (
        <View style={styles.imagesContainer}>
          {item.images.length === 1 ? (
            <Image source={{ uri: item.images[0] }} style={styles.singleImage} />
          ) : (
            <View style={styles.multipleImages}>
              {item.images.slice(0, 2).map((image, index) => (
                <Image
                  key={index}
                  source={{ uri: image }}
                  style={[
                    styles.multipleImage,
                    index === 1 && item.images.length > 2 && styles.lastImage,
                  ]}
                />
              ))}
              {item.images.length > 2 && (
                <View style={styles.moreImagesOverlay}>
                  <Text style={styles.moreImagesText}>+{item.images.length - 2}</Text>
                </View>
              )}
            </View>
          )}
        </View>
      )}

      {/* Post Stats */}
      <View style={styles.postStats}>
        <Text style={styles.statsText}>
          {item.impressions} maoni • {item.likes} likes • {item.comments} majibu
        </Text>
      </View>

      {/* Post Actions */}
      <View style={styles.postActionsContainer}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleLike(item.id)}
        >
          <Ionicons
            name={item.isLiked ? "heart" : "heart-outline"}
            size={20}
            color={item.isLiked ? "#FF3B30" : "#666"}
          />
          <Text style={[styles.actionText, item.isLiked && styles.likedText]}>
            {item.likes}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="chatbubble-outline" size={20} color="#666" />
          <Text style={styles.actionText}>{item.comments}</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="repeat-outline" size={20} color="#666" />
          <Text style={styles.actionText}>{item.shares}</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="gift-outline" size={20} color="#666" />
          <Text style={styles.actionText}>{item.gifts}</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton}>
          <Ionicons name="share-outline" size={20} color="#666" />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>ProChat</Text>
        <TouchableOpacity style={styles.addButton}>
          <Ionicons name="add-circle" size={28} color="#007AFF" />
        </TouchableOpacity>
      </View>

      {/* Posts Feed */}
      <FlatList
        data={posts}
        renderItem={renderPost}
        keyExtractor={(item) => item.id}
        style={styles.feed}
        showsVerticalScrollIndicator={false}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  addButton: {
    padding: 5,
  },
  feed: {
    flex: 1,
  },
  postContainer: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  postHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  userInfo: {
    flex: 1,
  },
  userNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginRight: 4,
  },
  userHandle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  postActions: {
    alignItems: 'flex-end',
  },
  timestamp: {
    fontSize: 12,
    color: '#999',
    marginBottom: 4,
  },
  moreButton: {
    padding: 4,
  },
  postContent: {
    fontSize: 16,
    lineHeight: 22,
    color: '#333',
    marginBottom: 12,
  },
  imagesContainer: {
    marginBottom: 12,
  },
  singleImage: {
    width: '100%',
    height: 200,
    borderRadius: 12,
    backgroundColor: '#F0F0F0',
  },
  multipleImages: {
    flexDirection: 'row',
    height: 200,
  },
  multipleImage: {
    flex: 1,
    height: '100%',
    borderRadius: 12,
    backgroundColor: '#F0F0F0',
    marginRight: 4,
  },
  lastImage: {
    marginRight: 0,
    position: 'relative',
  },
  moreImagesOverlay: {
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 0,
    left: '50%',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  moreImagesText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  postStats: {
    marginBottom: 12,
  },
  statsText: {
    fontSize: 12,
    color: '#999',
  },
  postActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  actionText: {
    marginLeft: 6,
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  likedText: {
    color: '#FF3B30',
  },
  separator: {
    height: 8,
    backgroundColor: '#F8F9FA',
  },
});
