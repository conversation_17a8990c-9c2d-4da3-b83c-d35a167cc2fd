package com.prochat.model.monetization;

import com.prochat.model.User;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "cashback_rules")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CashbackRule {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "name", nullable = false)
    private String name;
    
    @Column(name = "description")
    private String description;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "activity_type", nullable = false)
    private ActivityType activityType;
    
    @Column(name = "cashback_percentage", nullable = false, precision = 5, scale = 2)
    private BigDecimal cashbackPercentage;
    
    @Column(name = "minimum_amount", precision = 10, scale = 2)
    private BigDecimal minimumAmount;
    
    @Column(name = "maximum_cashback", precision = 10, scale = 2)
    private BigDecimal maximumCashback;
    
    @Column(name = "minimum_user_level")
    private Integer minimumUserLevel = 1;
    
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;
    
    @Column(name = "start_date")
    private LocalDateTime startDate;
    
    @Column(name = "end_date")
    private LocalDateTime endDate;
    
    @Column(name = "max_uses_per_user")
    private Integer maxUsesPerUser;
    
    @Column(name = "max_uses_per_day")
    private Integer maxUsesPerDay;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @Column(name = "created_by")
    private Long createdBy;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    public enum ActivityType {
        PROPAY_TRANSACTION("ProPay Transaction", "Miamala ya ProPay"),
        TICKET_PURCHASE("Ticket Purchase", "Ununuzi wa tiketi"),
        GIFT_SENDING("Gift Sending", "Kutuma zawadi"),
        EVENT_ATTENDANCE("Event Attendance", "Kuhudhuria matukio"),
        MERCHANT_PAYMENT("Merchant Payment", "Malipo ya biashara"),
        BILL_PAYMENT("Bill Payment", "Malipo ya bili"),
        MONEY_TRANSFER("Money Transfer", "Uhamisho wa pesa"),
        MOBILE_RECHARGE("Mobile Recharge", "Kujaza mitandao"),
        SUBSCRIPTION("Subscription", "Michango ya mwezi"),
        DONATION("Donation", "Michango");
        
        private final String displayName;
        private final String swahiliName;
        
        ActivityType(String displayName, String swahiliName) {
            this.displayName = displayName;
            this.swahiliName = swahiliName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public String getSwahiliName() {
            return swahiliName;
        }
    }
}

@Entity
@Table(name = "cashback_transactions")
@Data
@NoArgsConstructor
@AllArgsConstructor
class CashbackTransaction {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cashback_rule_id", nullable = false)
    private CashbackRule cashbackRule;
    
    @Column(name = "original_transaction_id")
    private Long originalTransactionId;
    
    @Column(name = "original_amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal originalAmount;
    
    @Column(name = "cashback_amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal cashbackAmount;
    
    @Column(name = "cashback_percentage", nullable = false, precision = 5, scale = 2)
    private BigDecimal cashbackPercentage;
    
    @Column(name = "currency", nullable = false)
    private String currency = "TZS";
    
    @Column(name = "description")
    private String description;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private CashbackStatus status = CashbackStatus.PENDING;
    
    @Column(name = "processed_at")
    private LocalDateTime processedAt;
    
    @Column(name = "expires_at")
    private LocalDateTime expiresAt;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        // Cashback expires after 30 days if not processed
        expiresAt = LocalDateTime.now().plusDays(30);
    }
    
    public enum CashbackStatus {
        PENDING("Inasubiri"),
        PROCESSED("Imechakatwa"),
        EXPIRED("Imeisha muda"),
        CANCELLED("Imeghairiwa");
        
        private final String displayName;
        
        CashbackStatus(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
}

@Entity
@Table(name = "user_loyalty_levels")
@Data
@NoArgsConstructor
@AllArgsConstructor
class UserLoyaltyLevel {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false, unique = true)
    private User user;
    
    @Column(name = "current_level", nullable = false)
    private Integer currentLevel = 1;
    
    @Column(name = "total_spent", nullable = false, precision = 12, scale = 2)
    private BigDecimal totalSpent = BigDecimal.ZERO;
    
    @Column(name = "total_cashback_earned", nullable = false, precision = 10, scale = 2)
    private BigDecimal totalCashbackEarned = BigDecimal.ZERO;
    
    @Column(name = "points_earned", nullable = false)
    private Long pointsEarned = 0L;
    
    @Column(name = "points_redeemed", nullable = false)
    private Long pointsRedeemed = 0L;
    
    @Column(name = "level_upgraded_at")
    private LocalDateTime levelUpgradedAt;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    // Calculate level based on total spent
    public void calculateLevel() {
        int newLevel = 1;
        BigDecimal spent = totalSpent;
        
        if (spent.compareTo(new BigDecimal("100000")) >= 0) newLevel = 2;  // 100K
        if (spent.compareTo(new BigDecimal("500000")) >= 0) newLevel = 3;  // 500K
        if (spent.compareTo(new BigDecimal("1000000")) >= 0) newLevel = 4; // 1M
        if (spent.compareTo(new BigDecimal("5000000")) >= 0) newLevel = 5; // 5M
        if (spent.compareTo(new BigDecimal("10000000")) >= 0) newLevel = 6; // 10M
        
        if (newLevel > currentLevel) {
            currentLevel = newLevel;
            levelUpgradedAt = LocalDateTime.now();
        }
    }
}
