# 🛠️ ProChat Platform - Installation Guide

This guide will help you set up the complete ProChat platform on your local development environment.

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

### Required Software
- **Node.js** (v18.0.0 or higher)
- **Java** (JDK 17 or higher)
- **MySQL** (v8.0 or higher)
- **Git** (latest version)

### Development Tools
- **Android Studio** (for Android development)
- **Xcode** (for iOS development - macOS only)
- **VS Code** or **IntelliJ IDEA**

### Cloud Services
- **AWS Account** (for S3 file storage)
- **Firebase Account** (for push notifications)

## 🚀 Installation Steps

### 1. Clone the Repository

```bash
git clone https://github.com/prochat/platform.git
cd prochat-platform
```

### 2. Database Setup

#### Install MySQL
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server

# macOS (using Homebrew)
brew install mysql

# Windows
# Download from https://dev.mysql.com/downloads/mysql/
```

#### Create Database
```sql
mysql -u root -p

CREATE DATABASE prochat_db;
CREATE USER 'prochat_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON prochat_db.* TO 'prochat_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 3. Backend Setup (Spring Boot)

```bash
cd backend

# Copy environment configuration
cp src/main/resources/application.properties.example src/main/resources/application.properties

# Edit configuration file
nano src/main/resources/application.properties
```

#### Configure application.properties
```properties
# Database Configuration
spring.datasource.url=**************************************
spring.datasource.username=prochat_user
spring.datasource.password=your_password

# AWS S3 Configuration
aws.s3.bucket-name=your-s3-bucket
aws.s3.access-key=your-access-key
aws.s3.secret-key=your-secret-key
aws.s3.region=us-east-1

# JWT Configuration
jwt.secret=your-jwt-secret-key
jwt.expiration=86400000

# Server Configuration
server.port=8080
```

#### Install Dependencies and Run
```bash
# Install dependencies
./mvnw clean install

# Run the application
./mvnw spring-boot:run
```

### 4. Mobile App Setup (React Native)

```bash
cd mobile

# Install dependencies
npm install

# Install iOS dependencies (macOS only)
cd ios && pod install && cd ..

# Copy environment configuration
cp .env.example .env
```

#### Configure .env file
```env
# API Configuration
API_BASE_URL=http://localhost:8080/api
WS_BASE_URL=ws://localhost:8080

# AWS Configuration
AWS_S3_BUCKET=your-s3-bucket
AWS_REGION=us-east-1

# Firebase Configuration
FIREBASE_API_KEY=your-firebase-api-key
FIREBASE_PROJECT_ID=your-project-id

# Other Configuration
APP_NAME=ProChat
APP_VERSION=1.0.0
```

#### Run the Mobile App
```bash
# Start Metro bundler
npx react-native start

# Run on Android (in another terminal)
npx react-native run-android

# Run on iOS (macOS only, in another terminal)
npx react-native run-ios
```

### 5. Admin Panel Setup (React)

```bash
cd admin

# Install dependencies
npm install

# Copy environment configuration
cp .env.example .env.local
```

#### Configure .env.local file
```env
REACT_APP_API_BASE_URL=http://localhost:8080/api
REACT_APP_WS_BASE_URL=ws://localhost:8080
REACT_APP_S3_BUCKET_URL=https://your-s3-bucket.s3.amazonaws.com
```

#### Run the Admin Panel
```bash
npm start
```

### 6. Public Website Setup (React)

```bash
cd website

# Install dependencies
npm install

# Copy environment configuration
cp .env.example .env.local
```

#### Configure .env.local file
```env
REACT_APP_API_BASE_URL=http://localhost:8080/api
REACT_APP_SITE_NAME=ProChat
REACT_APP_SITE_URL=http://localhost:3000
```

#### Run the Public Website
```bash
npm start
```

## 🔧 Development Tools Setup

### Android Development
1. Install Android Studio
2. Set up Android SDK
3. Create virtual device (AVD)
4. Set ANDROID_HOME environment variable

### iOS Development (macOS only)
1. Install Xcode from App Store
2. Install Xcode Command Line Tools
3. Install CocoaPods: `sudo gem install cocoapods`

### VS Code Extensions
- React Native Tools
- Java Extension Pack
- MySQL
- GitLens
- Prettier
- ESLint

## 🧪 Verify Installation

### 1. Check Backend
```bash
curl http://localhost:8080/api/health
# Should return: {"status":"UP"}
```

### 2. Check Database Connection
```bash
mysql -u prochat_user -p prochat_db -e "SHOW TABLES;"
```

### 3. Check Mobile App
- Open the app on emulator/device
- Try to register a new account
- Verify API calls in network tab

### 4. Check Admin Panel
- Open http://localhost:3001
- Login with admin credentials
- Verify dashboard loads

### 5. Check Public Website
- Open http://localhost:3000
- Verify homepage loads
- Check responsive design

## 🐛 Troubleshooting

### Common Issues

#### Backend Issues
```bash
# Port already in use
sudo lsof -i :8080
sudo kill -9 <PID>

# Database connection failed
mysql -u root -p -e "SHOW PROCESSLIST;"
```

#### Mobile App Issues
```bash
# Metro bundler issues
npx react-native start --reset-cache

# Android build issues
cd android && ./gradlew clean && cd ..

# iOS build issues (macOS)
cd ios && pod install && cd ..
```

#### Node.js Issues
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

### Getting Help
- Check [FAQ](../FAQ.md)
- Join our [Discord](https://discord.gg/prochat)
- Create an issue on [GitHub](https://github.com/prochat/platform/issues)

## ✅ Next Steps

After successful installation:
1. Read the [Configuration Guide](configuration.md)
2. Check [API Documentation](../api/README.md)
3. Review [Development Guidelines](../development/coding-standards.md)
4. Set up [Testing Environment](../development/testing-guide.md)

---

**Need help?** Contact <NAME_EMAIL>
