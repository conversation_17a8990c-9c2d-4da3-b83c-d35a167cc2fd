package com.prochat.controller;

import com.prochat.dto.MessageResponse;
import com.prochat.model.*;
import com.prochat.repository.TransactionRepository;
import com.prochat.repository.UserRepository;
import com.prochat.repository.WalletRepository;
import com.prochat.security.UserPrincipal;
import com.prochat.service.WalletService;
import com.prochat.service.TaxService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/wallet")
public class WalletController {

    @Autowired
    WalletRepository walletRepository;

    @Autowired
    UserRepository userRepository;

    @Autowired
    TransactionRepository transactionRepository;

    @Autowired
    WalletService walletService;

    @Autowired
    TaxService taxService;

    @GetMapping
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<?> getWallet(Authentication authentication) {
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        Optional<User> user = userRepository.findById(userPrincipal.getId());

        if (user.isPresent()) {
            Optional<Wallet> wallet = walletRepository.findByUser(user.get());

            if (wallet.isPresent()) {
                return ResponseEntity.ok(wallet.get());
            } else {
                // Create wallet if doesn't exist
                Wallet newWallet = walletService.createWallet(user.get());
                return ResponseEntity.ok(newWallet);
            }
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/transactions")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<?> getTransactions(
            Authentication authentication,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        Optional<Wallet> wallet = walletRepository.findByUserId(userPrincipal.getId());

        if (wallet.isPresent()) {
            Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
            Page<Transaction> transactions = transactionRepository.findBySenderWalletOrReceiverWalletOrderByCreatedAtDesc(
                    wallet.get(), wallet.get(), pageable);

            return ResponseEntity.ok(transactions);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @PostMapping("/send")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<?> sendMoney(
            Authentication authentication,
            @Valid @RequestBody SendMoneyRequest request) {

        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Transaction transaction = walletService.sendMoney(
                    userPrincipal.getId(),
                    request.getReceiverWalletNumber(),
                    request.getAmount(),
                    request.getDescription(),
                    request.getPin()
            );

            return ResponseEntity.ok(transaction);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    @PostMapping("/request")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<?> requestMoney(
            Authentication authentication,
            @Valid @RequestBody RequestMoneyRequest request) {

        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            // Implement money request logic
            return ResponseEntity.ok(new MessageResponse("Money request sent successfully!"));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    @PostMapping("/deposit")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<?> deposit(
            Authentication authentication,
            @Valid @RequestBody DepositRequest request) {

        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Transaction transaction = walletService.deposit(
                    userPrincipal.getId(),
                    request.getAmount(),
                    request.getPaymentMethod(),
                    request.getExternalReference()
            );

            return ResponseEntity.ok(transaction);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    @PostMapping("/withdraw")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<?> withdraw(
            Authentication authentication,
            @Valid @RequestBody WithdrawRequest request) {

        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Transaction transaction = walletService.withdraw(
                    userPrincipal.getId(),
                    request.getAmount(),
                    request.getWithdrawMethod(),
                    request.getAccountDetails(),
                    request.getPin()
            );

            return ResponseEntity.ok(transaction);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    @PostMapping("/set-pin")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<?> setPin(
            Authentication authentication,
            @Valid @RequestBody SetPinRequest request) {

        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            walletService.setPin(userPrincipal.getId(), request.getPin());

            return ResponseEntity.ok(new MessageResponse("PIN set successfully!"));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    @GetMapping("/balance")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<?> getBalance(Authentication authentication) {
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        Optional<Wallet> wallet = walletRepository.findByUserId(userPrincipal.getId());

        if (wallet.isPresent()) {
            return ResponseEntity.ok(new BalanceResponse(wallet.get().getBalance()));
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/statement")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<?> getStatement(
            Authentication authentication,
            @RequestParam String startDate,
            @RequestParam String endDate) {

        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Optional<Wallet> wallet = walletRepository.findByUserId(userPrincipal.getId());

            if (wallet.isPresent()) {
                LocalDateTime start = LocalDateTime.parse(startDate);
                LocalDateTime end = LocalDateTime.parse(endDate);

                List<Transaction> transactions = transactionRepository.findWalletTransactionsBetweenDates(
                        wallet.get(), start, end);

                return ResponseEntity.ok(transactions);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    @PostMapping("/calculate-vat")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<?> calculateVAT(@Valid @RequestBody VATCalculationRequest request) {
        try {
            TaxService.TaxCalculationResult result = taxService.calculateVAT(
                request.getAmount(), request.getTransactionType());

            VATCalculationResponse response = new VATCalculationResponse(
                result.getGrossAmount(),
                result.getTaxAmount(),
                result.getNetAmount(),
                result.getTaxConfiguration() != null ?
                    result.getTaxConfiguration().getTaxPercentage() : BigDecimal.ZERO
            );

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new MessageResponse("Error: " + e.getMessage()));
        }
    }

    // DTO Classes
    public static class SendMoneyRequest {
        private String receiverWalletNumber;
        private BigDecimal amount;
        private String description;
        private String pin;

        // Getters and setters
        public String getReceiverWalletNumber() { return receiverWalletNumber; }
        public void setReceiverWalletNumber(String receiverWalletNumber) { this.receiverWalletNumber = receiverWalletNumber; }
        public BigDecimal getAmount() { return amount; }
        public void setAmount(BigDecimal amount) { this.amount = amount; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getPin() { return pin; }
        public void setPin(String pin) { this.pin = pin; }
    }

    public static class RequestMoneyRequest {
        private String senderWalletNumber;
        private BigDecimal amount;
        private String description;

        // Getters and setters
        public String getSenderWalletNumber() { return senderWalletNumber; }
        public void setSenderWalletNumber(String senderWalletNumber) { this.senderWalletNumber = senderWalletNumber; }
        public BigDecimal getAmount() { return amount; }
        public void setAmount(BigDecimal amount) { this.amount = amount; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
    }

    public static class DepositRequest {
        private BigDecimal amount;
        private String paymentMethod;
        private String externalReference;

        // Getters and setters
        public BigDecimal getAmount() { return amount; }
        public void setAmount(BigDecimal amount) { this.amount = amount; }
        public String getPaymentMethod() { return paymentMethod; }
        public void setPaymentMethod(String paymentMethod) { this.paymentMethod = paymentMethod; }
        public String getExternalReference() { return externalReference; }
        public void setExternalReference(String externalReference) { this.externalReference = externalReference; }
    }

    public static class WithdrawRequest {
        private BigDecimal amount;
        private String withdrawMethod;
        private String accountDetails;
        private String pin;

        // Getters and setters
        public BigDecimal getAmount() { return amount; }
        public void setAmount(BigDecimal amount) { this.amount = amount; }
        public String getWithdrawMethod() { return withdrawMethod; }
        public void setWithdrawMethod(String withdrawMethod) { this.withdrawMethod = withdrawMethod; }
        public String getAccountDetails() { return accountDetails; }
        public void setAccountDetails(String accountDetails) { this.accountDetails = accountDetails; }
        public String getPin() { return pin; }
        public void setPin(String pin) { this.pin = pin; }
    }

    public static class SetPinRequest {
        private String pin;

        // Getters and setters
        public String getPin() { return pin; }
        public void setPin(String pin) { this.pin = pin; }
    }

    public static class BalanceResponse {
        private BigDecimal balance;

        public BalanceResponse(BigDecimal balance) {
            this.balance = balance;
        }

        public BigDecimal getBalance() { return balance; }
        public void setBalance(BigDecimal balance) { this.balance = balance; }
    }

    public static class VATCalculationRequest {
        private BigDecimal amount;
        private String transactionType;

        // Getters and setters
        public BigDecimal getAmount() { return amount; }
        public void setAmount(BigDecimal amount) { this.amount = amount; }
        public String getTransactionType() { return transactionType; }
        public void setTransactionType(String transactionType) { this.transactionType = transactionType; }
    }

    public static class VATCalculationResponse {
        private BigDecimal grossAmount;
        private BigDecimal vatAmount;
        private BigDecimal netAmount;
        private BigDecimal vatRate;

        public VATCalculationResponse(BigDecimal grossAmount, BigDecimal vatAmount,
                                    BigDecimal netAmount, BigDecimal vatRate) {
            this.grossAmount = grossAmount;
            this.vatAmount = vatAmount;
            this.netAmount = netAmount;
            this.vatRate = vatRate;
        }

        // Getters and setters
        public BigDecimal getGrossAmount() { return grossAmount; }
        public void setGrossAmount(BigDecimal grossAmount) { this.grossAmount = grossAmount; }
        public BigDecimal getVatAmount() { return vatAmount; }
        public void setVatAmount(BigDecimal vatAmount) { this.vatAmount = vatAmount; }
        public BigDecimal getNetAmount() { return netAmount; }
        public void setNetAmount(BigDecimal netAmount) { this.netAmount = netAmount; }
        public BigDecimal getVatRate() { return vatRate; }
        public void setVatRate(BigDecimal vatRate) { this.vatRate = vatRate; }
    }
}
