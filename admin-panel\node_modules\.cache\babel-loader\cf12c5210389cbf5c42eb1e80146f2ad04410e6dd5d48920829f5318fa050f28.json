{"ast": null, "code": "import { bisector, tickStep } from \"d3-array\";\nimport { durationDay, durationHour, durationMinute, durationMonth, durationSecond, durationWeek, durationYear } from \"./duration.js\";\nimport { millisecond } from \"./millisecond.js\";\nimport { second } from \"./second.js\";\nimport { timeMinute, utcMinute } from \"./minute.js\";\nimport { timeHour, utcHour } from \"./hour.js\";\nimport { timeDay, unixDay } from \"./day.js\";\nimport { timeSunday, utcSunday } from \"./week.js\";\nimport { timeMonth, utcMonth } from \"./month.js\";\nimport { timeYear, utcYear } from \"./year.js\";\nfunction ticker(year, month, week, day, hour, minute) {\n  const tickIntervals = [[second, 1, durationSecond], [second, 5, 5 * durationSecond], [second, 15, 15 * durationSecond], [second, 30, 30 * durationSecond], [minute, 1, durationMinute], [minute, 5, 5 * durationMinute], [minute, 15, 15 * durationMinute], [minute, 30, 30 * durationMinute], [hour, 1, durationHour], [hour, 3, 3 * durationHour], [hour, 6, 6 * durationHour], [hour, 12, 12 * durationHour], [day, 1, durationDay], [day, 2, 2 * durationDay], [week, 1, durationWeek], [month, 1, durationMonth], [month, 3, 3 * durationMonth], [year, 1, durationYear]];\n  function ticks(start, stop, count) {\n    const reverse = stop < start;\n    if (reverse) [start, stop] = [stop, start];\n    const interval = count && typeof count.range === \"function\" ? count : tickInterval(start, stop, count);\n    const ticks = interval ? interval.range(start, +stop + 1) : []; // inclusive stop\n    return reverse ? ticks.reverse() : ticks;\n  }\n  function tickInterval(start, stop, count) {\n    const target = Math.abs(stop - start) / count;\n    const i = bisector(([,, step]) => step).right(tickIntervals, target);\n    if (i === tickIntervals.length) return year.every(tickStep(start / durationYear, stop / durationYear, count));\n    if (i === 0) return millisecond.every(Math.max(tickStep(start, stop, count), 1));\n    const [t, step] = tickIntervals[target / tickIntervals[i - 1][2] < tickIntervals[i][2] / target ? i - 1 : i];\n    return t.every(step);\n  }\n  return [ticks, tickInterval];\n}\nconst [utcTicks, utcTickInterval] = ticker(utcYear, utcMonth, utcSunday, unixDay, utcHour, utcMinute);\nconst [timeTicks, timeTickInterval] = ticker(timeYear, timeMonth, timeSunday, timeDay, timeHour, timeMinute);\nexport { utcTicks, utcTickInterval, timeTicks, timeTickInterval };", "map": {"version": 3, "names": ["bisector", "tickStep", "durationDay", "durationHour", "durationMinute", "durationMonth", "durationSecond", "durationWeek", "durationYear", "millisecond", "second", "timeMinute", "utcMinute", "timeHour", "utcHour", "timeDay", "unixDay", "timeSunday", "utcSunday", "timeMonth", "utcMonth", "timeYear", "utcYear", "ticker", "year", "month", "week", "day", "hour", "minute", "tickIntervals", "ticks", "start", "stop", "count", "reverse", "interval", "range", "tickInterval", "target", "Math", "abs", "i", "step", "right", "length", "every", "max", "t", "utcTicks", "utcTickInterval", "timeTicks", "timeTickInterval"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/d3-time/src/ticks.js"], "sourcesContent": ["import {bisector, tickStep} from \"d3-array\";\nimport {durationDay, durationHour, durationMinute, durationMonth, durationSecond, durationWeek, durationYear} from \"./duration.js\";\nimport {millisecond} from \"./millisecond.js\";\nimport {second} from \"./second.js\";\nimport {timeMinute, utcMinute} from \"./minute.js\";\nimport {timeHour, utcHour} from \"./hour.js\";\nimport {timeDay, unixDay} from \"./day.js\";\nimport {timeSunday, utcSunday} from \"./week.js\";\nimport {timeMonth, utcMonth} from \"./month.js\";\nimport {timeYear, utcYear} from \"./year.js\";\n\nfunction ticker(year, month, week, day, hour, minute) {\n\n  const tickIntervals = [\n    [second,  1,      durationSecond],\n    [second,  5,  5 * durationSecond],\n    [second, 15, 15 * durationSecond],\n    [second, 30, 30 * durationSecond],\n    [minute,  1,      durationMinute],\n    [minute,  5,  5 * durationMinute],\n    [minute, 15, 15 * durationMinute],\n    [minute, 30, 30 * durationMinute],\n    [  hour,  1,      durationHour  ],\n    [  hour,  3,  3 * durationHour  ],\n    [  hour,  6,  6 * durationHour  ],\n    [  hour, 12, 12 * durationHour  ],\n    [   day,  1,      durationDay   ],\n    [   day,  2,  2 * durationDay   ],\n    [  week,  1,      durationWeek  ],\n    [ month,  1,      durationMonth ],\n    [ month,  3,  3 * durationMonth ],\n    [  year,  1,      durationYear  ]\n  ];\n\n  function ticks(start, stop, count) {\n    const reverse = stop < start;\n    if (reverse) [start, stop] = [stop, start];\n    const interval = count && typeof count.range === \"function\" ? count : tickInterval(start, stop, count);\n    const ticks = interval ? interval.range(start, +stop + 1) : []; // inclusive stop\n    return reverse ? ticks.reverse() : ticks;\n  }\n\n  function tickInterval(start, stop, count) {\n    const target = Math.abs(stop - start) / count;\n    const i = bisector(([,, step]) => step).right(tickIntervals, target);\n    if (i === tickIntervals.length) return year.every(tickStep(start / durationYear, stop / durationYear, count));\n    if (i === 0) return millisecond.every(Math.max(tickStep(start, stop, count), 1));\n    const [t, step] = tickIntervals[target / tickIntervals[i - 1][2] < tickIntervals[i][2] / target ? i - 1 : i];\n    return t.every(step);\n  }\n\n  return [ticks, tickInterval];\n}\n\nconst [utcTicks, utcTickInterval] = ticker(utcYear, utcMonth, utcSunday, unixDay, utcHour, utcMinute);\nconst [timeTicks, timeTickInterval] = ticker(timeYear, timeMonth, timeSunday, timeDay, timeHour, timeMinute);\n\nexport {utcTicks, utcTickInterval, timeTicks, timeTickInterval};\n"], "mappings": "AAAA,SAAQA,QAAQ,EAAEC,QAAQ,QAAO,UAAU;AAC3C,SAAQC,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,aAAa,EAAEC,cAAc,EAAEC,YAAY,EAAEC,YAAY,QAAO,eAAe;AAClI,SAAQC,WAAW,QAAO,kBAAkB;AAC5C,SAAQC,MAAM,QAAO,aAAa;AAClC,SAAQC,UAAU,EAAEC,SAAS,QAAO,aAAa;AACjD,SAAQC,QAAQ,EAAEC,OAAO,QAAO,WAAW;AAC3C,SAAQC,OAAO,EAAEC,OAAO,QAAO,UAAU;AACzC,SAAQC,UAAU,EAAEC,SAAS,QAAO,WAAW;AAC/C,SAAQC,SAAS,EAAEC,QAAQ,QAAO,YAAY;AAC9C,SAAQC,QAAQ,EAAEC,OAAO,QAAO,WAAW;AAE3C,SAASC,MAAMA,CAACC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAE;EAEpD,MAAMC,aAAa,GAAG,CACpB,CAACpB,MAAM,EAAG,CAAC,EAAOJ,cAAc,CAAC,EACjC,CAACI,MAAM,EAAG,CAAC,EAAG,CAAC,GAAGJ,cAAc,CAAC,EACjC,CAACI,MAAM,EAAE,EAAE,EAAE,EAAE,GAAGJ,cAAc,CAAC,EACjC,CAACI,MAAM,EAAE,EAAE,EAAE,EAAE,GAAGJ,cAAc,CAAC,EACjC,CAACuB,MAAM,EAAG,CAAC,EAAOzB,cAAc,CAAC,EACjC,CAACyB,MAAM,EAAG,CAAC,EAAG,CAAC,GAAGzB,cAAc,CAAC,EACjC,CAACyB,MAAM,EAAE,EAAE,EAAE,EAAE,GAAGzB,cAAc,CAAC,EACjC,CAACyB,MAAM,EAAE,EAAE,EAAE,EAAE,GAAGzB,cAAc,CAAC,EACjC,CAAGwB,IAAI,EAAG,CAAC,EAAOzB,YAAY,CAAG,EACjC,CAAGyB,IAAI,EAAG,CAAC,EAAG,CAAC,GAAGzB,YAAY,CAAG,EACjC,CAAGyB,IAAI,EAAG,CAAC,EAAG,CAAC,GAAGzB,YAAY,CAAG,EACjC,CAAGyB,IAAI,EAAE,EAAE,EAAE,EAAE,GAAGzB,YAAY,CAAG,EACjC,CAAIwB,GAAG,EAAG,CAAC,EAAOzB,WAAW,CAAI,EACjC,CAAIyB,GAAG,EAAG,CAAC,EAAG,CAAC,GAAGzB,WAAW,CAAI,EACjC,CAAGwB,IAAI,EAAG,CAAC,EAAOnB,YAAY,CAAG,EACjC,CAAEkB,KAAK,EAAG,CAAC,EAAOpB,aAAa,CAAE,EACjC,CAAEoB,KAAK,EAAG,CAAC,EAAG,CAAC,GAAGpB,aAAa,CAAE,EACjC,CAAGmB,IAAI,EAAG,CAAC,EAAOhB,YAAY,CAAG,CAClC;EAED,SAASuB,KAAKA,CAACC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAE;IACjC,MAAMC,OAAO,GAAGF,IAAI,GAAGD,KAAK;IAC5B,IAAIG,OAAO,EAAE,CAACH,KAAK,EAAEC,IAAI,CAAC,GAAG,CAACA,IAAI,EAAED,KAAK,CAAC;IAC1C,MAAMI,QAAQ,GAAGF,KAAK,IAAI,OAAOA,KAAK,CAACG,KAAK,KAAK,UAAU,GAAGH,KAAK,GAAGI,YAAY,CAACN,KAAK,EAAEC,IAAI,EAAEC,KAAK,CAAC;IACtG,MAAMH,KAAK,GAAGK,QAAQ,GAAGA,QAAQ,CAACC,KAAK,CAACL,KAAK,EAAE,CAACC,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;IAChE,OAAOE,OAAO,GAAGJ,KAAK,CAACI,OAAO,CAAC,CAAC,GAAGJ,KAAK;EAC1C;EAEA,SAASO,YAAYA,CAACN,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAE;IACxC,MAAMK,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACR,IAAI,GAAGD,KAAK,CAAC,GAAGE,KAAK;IAC7C,MAAMQ,CAAC,GAAG1C,QAAQ,CAAC,CAAC,IAAI2C,IAAI,CAAC,KAAKA,IAAI,CAAC,CAACC,KAAK,CAACd,aAAa,EAAES,MAAM,CAAC;IACpE,IAAIG,CAAC,KAAKZ,aAAa,CAACe,MAAM,EAAE,OAAOrB,IAAI,CAACsB,KAAK,CAAC7C,QAAQ,CAAC+B,KAAK,GAAGxB,YAAY,EAAEyB,IAAI,GAAGzB,YAAY,EAAE0B,KAAK,CAAC,CAAC;IAC7G,IAAIQ,CAAC,KAAK,CAAC,EAAE,OAAOjC,WAAW,CAACqC,KAAK,CAACN,IAAI,CAACO,GAAG,CAAC9C,QAAQ,CAAC+B,KAAK,EAAEC,IAAI,EAAEC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;IAChF,MAAM,CAACc,CAAC,EAAEL,IAAI,CAAC,GAAGb,aAAa,CAACS,MAAM,GAAGT,aAAa,CAACY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGZ,aAAa,CAACY,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGH,MAAM,GAAGG,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC;IAC5G,OAAOM,CAAC,CAACF,KAAK,CAACH,IAAI,CAAC;EACtB;EAEA,OAAO,CAACZ,KAAK,EAAEO,YAAY,CAAC;AAC9B;AAEA,MAAM,CAACW,QAAQ,EAAEC,eAAe,CAAC,GAAG3B,MAAM,CAACD,OAAO,EAAEF,QAAQ,EAAEF,SAAS,EAAEF,OAAO,EAAEF,OAAO,EAAEF,SAAS,CAAC;AACrG,MAAM,CAACuC,SAAS,EAAEC,gBAAgB,CAAC,GAAG7B,MAAM,CAACF,QAAQ,EAAEF,SAAS,EAAEF,UAAU,EAAEF,OAAO,EAAEF,QAAQ,EAAEF,UAAU,CAAC;AAE5G,SAAQsC,QAAQ,EAAEC,eAAe,EAAEC,SAAS,EAAEC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}