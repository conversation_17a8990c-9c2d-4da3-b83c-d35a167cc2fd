package com.prochat.controller;

import com.prochat.dto.MessageResponse;
import com.prochat.model.Post;
import com.prochat.model.PostStatus;
import com.prochat.model.PostType;
import com.prochat.model.User;
import com.prochat.repository.PostRepository;
import com.prochat.repository.UserRepository;
import com.prochat.security.UserPrincipal;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/posts")
public class PostController {
    
    @Autowired
    PostRepository postRepository;
    
    @Autowired
    UserRepository userRepository;
    
    @GetMapping("/feed")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<?> getFeed(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        Page<Post> posts = postRepository.findByStatusOrderByCreatedAtDesc(PostStatus.ACTIVE, pageable);
        
        return ResponseEntity.ok(posts);
    }
    
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> getAllPosts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) PostStatus status) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        Page<Post> posts;
        
        if (status != null) {
            posts = postRepository.findByStatusOrderByCreatedAtDesc(status, pageable);
        } else {
            posts = postRepository.findAll(pageable);
        }
        
        return ResponseEntity.ok(posts);
    }
    
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<?> getPostById(@PathVariable Long id) {
        Optional<Post> post = postRepository.findById(id);
        
        if (post.isPresent()) {
            // Increment impressions count
            Post currentPost = post.get();
            currentPost.setImpressionsCount(currentPost.getImpressionsCount() + 1);
            postRepository.save(currentPost);
            
            return ResponseEntity.ok(currentPost);
        } else {
            return ResponseEntity.notFound().build();
        }
    }
    
    @GetMapping("/user/{userId}")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<?> getPostsByUser(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Optional<User> user = userRepository.findById(userId);
        if (!user.isPresent()) {
            return ResponseEntity.notFound().build();
        }
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        Page<Post> posts = postRepository.findByUserAndStatusOrderByCreatedAtDesc(
                user.get(), PostStatus.ACTIVE, pageable);
        
        return ResponseEntity.ok(posts);
    }
    
    @PostMapping
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<?> createPost(
            Authentication authentication,
            @Valid @RequestBody CreatePostRequest request) {
        
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        Optional<User> user = userRepository.findById(userPrincipal.getId());
        
        if (!user.isPresent()) {
            return ResponseEntity.notFound().build();
        }
        
        Post post = new Post(user.get(), request.getContent(), request.getType());
        
        if (request.getMediaUrls() != null && !request.getMediaUrls().isEmpty()) {
            post.setMediaUrls(request.getMediaUrls());
        }
        
        Post savedPost = postRepository.save(post);
        return ResponseEntity.ok(savedPost);
    }
    
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<?> updatePost(
            @PathVariable Long id,
            Authentication authentication,
            @Valid @RequestBody UpdatePostRequest request) {
        
        Optional<Post> postOptional = postRepository.findById(id);
        if (!postOptional.isPresent()) {
            return ResponseEntity.notFound().build();
        }
        
        Post post = postOptional.get();
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        
        // Check if user owns the post or is admin
        if (!post.getUser().getId().equals(userPrincipal.getId()) && 
            !userPrincipal.getAuthorities().stream()
                .anyMatch(auth -> auth.getAuthority().equals("ROLE_ADMIN"))) {
            return ResponseEntity.status(403).body(new MessageResponse("Access denied"));
        }
        
        if (request.getContent() != null) {
            post.setContent(request.getContent());
        }
        
        if (request.getMediaUrls() != null) {
            post.setMediaUrls(request.getMediaUrls());
        }
        
        Post updatedPost = postRepository.save(post);
        return ResponseEntity.ok(updatedPost);
    }
    
    @PostMapping("/{id}/like")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<?> likePost(@PathVariable Long id) {
        Optional<Post> postOptional = postRepository.findById(id);
        if (!postOptional.isPresent()) {
            return ResponseEntity.notFound().build();
        }
        
        Post post = postOptional.get();
        post.setLikesCount(post.getLikesCount() + 1);
        postRepository.save(post);
        
        return ResponseEntity.ok(new MessageResponse("Post liked successfully!"));
    }
    
    @PostMapping("/{id}/unlike")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<?> unlikePost(@PathVariable Long id) {
        Optional<Post> postOptional = postRepository.findById(id);
        if (!postOptional.isPresent()) {
            return ResponseEntity.notFound().build();
        }
        
        Post post = postOptional.get();
        if (post.getLikesCount() > 0) {
            post.setLikesCount(post.getLikesCount() - 1);
            postRepository.save(post);
        }
        
        return ResponseEntity.ok(new MessageResponse("Post unliked successfully!"));
    }
    
    @PostMapping("/{id}/share")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<?> sharePost(@PathVariable Long id) {
        Optional<Post> postOptional = postRepository.findById(id);
        if (!postOptional.isPresent()) {
            return ResponseEntity.notFound().build();
        }
        
        Post post = postOptional.get();
        post.setSharesCount(post.getSharesCount() + 1);
        postRepository.save(post);
        
        return ResponseEntity.ok(new MessageResponse("Post shared successfully!"));
    }
    
    @PutMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updatePostStatus(
            @PathVariable Long id,
            @RequestParam PostStatus status) {
        
        Optional<Post> postOptional = postRepository.findById(id);
        if (!postOptional.isPresent()) {
            return ResponseEntity.notFound().build();
        }
        
        Post post = postOptional.get();
        post.setStatus(status);
        postRepository.save(post);
        
        return ResponseEntity.ok(new MessageResponse("Post status updated successfully!"));
    }
    
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN')")
    public ResponseEntity<?> deletePost(
            @PathVariable Long id,
            Authentication authentication) {
        
        Optional<Post> postOptional = postRepository.findById(id);
        if (!postOptional.isPresent()) {
            return ResponseEntity.notFound().build();
        }
        
        Post post = postOptional.get();
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        
        // Check if user owns the post or is admin
        if (!post.getUser().getId().equals(userPrincipal.getId()) && 
            !userPrincipal.getAuthorities().stream()
                .anyMatch(auth -> auth.getAuthority().equals("ROLE_ADMIN"))) {
            return ResponseEntity.status(403).body(new MessageResponse("Access denied"));
        }
        
        // Soft delete - mark as deleted instead of actually deleting
        post.setStatus(PostStatus.DELETED);
        postRepository.save(post);
        
        return ResponseEntity.ok(new MessageResponse("Post deleted successfully!"));
    }
    
    // DTO Classes
    public static class CreatePostRequest {
        private String content;
        private PostType type;
        private List<String> mediaUrls;
        
        // Getters and setters
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        public PostType getType() { return type; }
        public void setType(PostType type) { this.type = type; }
        public List<String> getMediaUrls() { return mediaUrls; }
        public void setMediaUrls(List<String> mediaUrls) { this.mediaUrls = mediaUrls; }
    }
    
    public static class UpdatePostRequest {
        private String content;
        private List<String> mediaUrls;
        
        // Getters and setters
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        public List<String> getMediaUrls() { return mediaUrls; }
        public void setMediaUrls(List<String> mediaUrls) { this.mediaUrls = mediaUrls; }
    }
}
