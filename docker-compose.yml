version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: prochat-mysql
    environment:
      MYSQL_ROOT_PASSWORD: Ram$0101
      MYSQL_DATABASE: prochat_db
      MYSQL_USER: prochat_user
      MYSQL_PASSWORD: Ram$0101
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init_database.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - prochat-network
    restart: unless-stopped

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: prochat-backend
    environment:
      SPRING_DATASOURCE_URL: ******************************************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: Ram$0101
      AWS_ACCESS_KEY: ${AWS_ACCESS_KEY}
      AWS_SECRET_KEY: ${AWS_SECRET_KEY}
      TWILIO_ACCOUNT_SID: ${TWILIO_ACCOUNT_SID}
      TWILIO_AUTH_TOKEN: ${TWILIO_AUTH_TOKEN}
      FIREBASE_SERVER_KEY: ${FIREBASE_SERVER_KEY}
    ports:
      - "8080:8080"
    depends_on:
      - mysql
    networks:
      - prochat-network
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads

  # Admin Panel
  admin-panel:
    build:
      context: ./admin-panel
      dockerfile: Dockerfile
    container_name: prochat-admin
    environment:
      REACT_APP_API_URL: http://localhost:8080/api
    ports:
      - "3001:3000"
    depends_on:
      - backend
    networks:
      - prochat-network
    restart: unless-stopped

  # Public Website
  public-website:
    build:
      context: ./public-website
      dockerfile: Dockerfile
    container_name: prochat-website
    environment:
      REACT_APP_API_URL: http://localhost:8080/api
    ports:
      - "3002:3000"
    depends_on:
      - backend
    networks:
      - prochat-network
    restart: unless-stopped

  # Redis for Caching
  redis:
    image: redis:7-alpine
    container_name: prochat-redis
    ports:
      - "6379:6379"
    networks:
      - prochat-network
    restart: unless-stopped
    volumes:
      - redis_data:/data

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: prochat-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - admin-panel
      - public-website
    networks:
      - prochat-network
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:

networks:
  prochat-network:
    driver: bridge
