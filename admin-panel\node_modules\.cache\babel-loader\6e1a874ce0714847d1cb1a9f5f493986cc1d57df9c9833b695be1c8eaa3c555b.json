{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"elementType\", \"ownerState\", \"externalForwardedProps\", \"getSlotOwnerState\", \"internalForwardedProps\"],\n  _excluded2 = [\"component\", \"slots\", \"slotProps\"],\n  _excluded3 = [\"component\"];\nimport useForkRef from '@mui/utils/useForkRef';\nimport appendOwnerState from '@mui/utils/appendOwnerState';\nimport resolveComponentProps from '@mui/utils/resolveComponentProps';\nimport mergeSlotProps from '@mui/utils/mergeSlotProps';\n/**\n * An internal function to create a Material UI slot.\n *\n * This is an advanced version of Base UI `useSlotProps` because Material UI allows leaf component to be customized via `component` prop\n * while Base UI does not need to support leaf component customization.\n *\n * @param {string} name: name of the slot\n * @param {object} parameters\n * @returns {[Slot, slotProps]} The slot's React component and the slot's props\n *\n * Note: the returned slot's props\n * - will never contain `component` prop.\n * - might contain `as` prop.\n */\nexport default function useSlot(\n/**\n * The slot's name. All Material UI components should have `root` slot.\n *\n * If the name is `root`, the logic behaves differently from other slots,\n * e.g. the `externalForwardedProps` are spread to `root` slot but not other slots.\n */\nname, parameters) {\n  const {\n      className,\n      elementType: initialElementType,\n      ownerState,\n      externalForwardedProps,\n      getSlotOwnerState,\n      internalForwardedProps\n    } = parameters,\n    useSlotPropsParams = _objectWithoutPropertiesLoose(parameters, _excluded);\n  const {\n      component: rootComponent,\n      slots = {\n        [name]: undefined\n      },\n      slotProps = {\n        [name]: undefined\n      }\n    } = externalForwardedProps,\n    other = _objectWithoutPropertiesLoose(externalForwardedProps, _excluded2);\n  const elementType = slots[name] || initialElementType;\n\n  // `slotProps[name]` can be a callback that receives the component's ownerState.\n  // `resolvedComponentsProps` is always a plain object.\n  const resolvedComponentsProps = resolveComponentProps(slotProps[name], ownerState);\n  const _mergeSlotProps = mergeSlotProps(_extends({\n      className\n    }, useSlotPropsParams, {\n      externalForwardedProps: name === 'root' ? other : undefined,\n      externalSlotProps: resolvedComponentsProps\n    })),\n    {\n      props: {\n        component: slotComponent\n      },\n      internalRef\n    } = _mergeSlotProps,\n    mergedProps = _objectWithoutPropertiesLoose(_mergeSlotProps.props, _excluded3);\n  const ref = useForkRef(internalRef, resolvedComponentsProps == null ? void 0 : resolvedComponentsProps.ref, parameters.ref);\n  const slotOwnerState = getSlotOwnerState ? getSlotOwnerState(mergedProps) : {};\n  const finalOwnerState = _extends({}, ownerState, slotOwnerState);\n  const LeafComponent = name === 'root' ? slotComponent || rootComponent : slotComponent;\n  const props = appendOwnerState(elementType, _extends({}, name === 'root' && !rootComponent && !slots[name] && internalForwardedProps, name !== 'root' && !slots[name] && internalForwardedProps, mergedProps, LeafComponent && {\n    as: LeafComponent\n  }, {\n    ref\n  }), finalOwnerState);\n  Object.keys(slotOwnerState).forEach(propName => {\n    delete props[propName];\n  });\n  return [elementType, props];\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "_excluded2", "_excluded3", "useForkRef", "appendOwnerState", "resolveComponentProps", "mergeSlotProps", "useSlot", "name", "parameters", "className", "elementType", "initialElementType", "ownerState", "externalForwardedProps", "getSlotOwnerState", "internalForwardedProps", "useSlotPropsParams", "component", "rootComponent", "slots", "undefined", "slotProps", "other", "resolvedComponentsProps", "_mergeSlotProps", "externalSlotProps", "props", "slotComponent", "internalRef", "mergedProps", "ref", "slotOwnerState", "finalOwnerState", "LeafComponent", "as", "Object", "keys", "for<PERSON>ach", "propName"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/@mui/material/utils/useSlot.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"elementType\", \"ownerState\", \"externalForwardedProps\", \"getSlotOwnerState\", \"internalForwardedProps\"],\n  _excluded2 = [\"component\", \"slots\", \"slotProps\"],\n  _excluded3 = [\"component\"];\nimport useForkRef from '@mui/utils/useForkRef';\nimport appendOwnerState from '@mui/utils/appendOwnerState';\nimport resolveComponentProps from '@mui/utils/resolveComponentProps';\nimport mergeSlotProps from '@mui/utils/mergeSlotProps';\n/**\n * An internal function to create a Material UI slot.\n *\n * This is an advanced version of Base UI `useSlotProps` because Material UI allows leaf component to be customized via `component` prop\n * while Base UI does not need to support leaf component customization.\n *\n * @param {string} name: name of the slot\n * @param {object} parameters\n * @returns {[Slot, slotProps]} The slot's React component and the slot's props\n *\n * Note: the returned slot's props\n * - will never contain `component` prop.\n * - might contain `as` prop.\n */\nexport default function useSlot(\n/**\n * The slot's name. All Material UI components should have `root` slot.\n *\n * If the name is `root`, the logic behaves differently from other slots,\n * e.g. the `externalForwardedProps` are spread to `root` slot but not other slots.\n */\nname, parameters) {\n  const {\n      className,\n      elementType: initialElementType,\n      ownerState,\n      externalForwardedProps,\n      getSlotOwnerState,\n      internalForwardedProps\n    } = parameters,\n    useSlotPropsParams = _objectWithoutPropertiesLoose(parameters, _excluded);\n  const {\n      component: rootComponent,\n      slots = {\n        [name]: undefined\n      },\n      slotProps = {\n        [name]: undefined\n      }\n    } = externalForwardedProps,\n    other = _objectWithoutPropertiesLoose(externalForwardedProps, _excluded2);\n  const elementType = slots[name] || initialElementType;\n\n  // `slotProps[name]` can be a callback that receives the component's ownerState.\n  // `resolvedComponentsProps` is always a plain object.\n  const resolvedComponentsProps = resolveComponentProps(slotProps[name], ownerState);\n  const _mergeSlotProps = mergeSlotProps(_extends({\n      className\n    }, useSlotPropsParams, {\n      externalForwardedProps: name === 'root' ? other : undefined,\n      externalSlotProps: resolvedComponentsProps\n    })),\n    {\n      props: {\n        component: slotComponent\n      },\n      internalRef\n    } = _mergeSlotProps,\n    mergedProps = _objectWithoutPropertiesLoose(_mergeSlotProps.props, _excluded3);\n  const ref = useForkRef(internalRef, resolvedComponentsProps == null ? void 0 : resolvedComponentsProps.ref, parameters.ref);\n  const slotOwnerState = getSlotOwnerState ? getSlotOwnerState(mergedProps) : {};\n  const finalOwnerState = _extends({}, ownerState, slotOwnerState);\n  const LeafComponent = name === 'root' ? slotComponent || rootComponent : slotComponent;\n  const props = appendOwnerState(elementType, _extends({}, name === 'root' && !rootComponent && !slots[name] && internalForwardedProps, name !== 'root' && !slots[name] && internalForwardedProps, mergedProps, LeafComponent && {\n    as: LeafComponent\n  }, {\n    ref\n  }), finalOwnerState);\n  Object.keys(slotOwnerState).forEach(propName => {\n    delete props[propName];\n  });\n  return [elementType, props];\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,wBAAwB,EAAE,mBAAmB,EAAE,wBAAwB,CAAC;EACnIC,UAAU,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,WAAW,CAAC;EAChDC,UAAU,GAAG,CAAC,WAAW,CAAC;AAC5B,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,qBAAqB,MAAM,kCAAkC;AACpE,OAAOC,cAAc,MAAM,2BAA2B;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,OAAOA;AAC/B;AACA;AACA;AACA;AACA;AACA;AACAC,IAAI,EAAEC,UAAU,EAAE;EAChB,MAAM;MACFC,SAAS;MACTC,WAAW,EAAEC,kBAAkB;MAC/BC,UAAU;MACVC,sBAAsB;MACtBC,iBAAiB;MACjBC;IACF,CAAC,GAAGP,UAAU;IACdQ,kBAAkB,GAAGlB,6BAA6B,CAACU,UAAU,EAAET,SAAS,CAAC;EAC3E,MAAM;MACFkB,SAAS,EAAEC,aAAa;MACxBC,KAAK,GAAG;QACN,CAACZ,IAAI,GAAGa;MACV,CAAC;MACDC,SAAS,GAAG;QACV,CAACd,IAAI,GAAGa;MACV;IACF,CAAC,GAAGP,sBAAsB;IAC1BS,KAAK,GAAGxB,6BAA6B,CAACe,sBAAsB,EAAEb,UAAU,CAAC;EAC3E,MAAMU,WAAW,GAAGS,KAAK,CAACZ,IAAI,CAAC,IAAII,kBAAkB;;EAErD;EACA;EACA,MAAMY,uBAAuB,GAAGnB,qBAAqB,CAACiB,SAAS,CAACd,IAAI,CAAC,EAAEK,UAAU,CAAC;EAClF,MAAMY,eAAe,GAAGnB,cAAc,CAACR,QAAQ,CAAC;MAC5CY;IACF,CAAC,EAAEO,kBAAkB,EAAE;MACrBH,sBAAsB,EAAEN,IAAI,KAAK,MAAM,GAAGe,KAAK,GAAGF,SAAS;MAC3DK,iBAAiB,EAAEF;IACrB,CAAC,CAAC,CAAC;IACH;MACEG,KAAK,EAAE;QACLT,SAAS,EAAEU;MACb,CAAC;MACDC;IACF,CAAC,GAAGJ,eAAe;IACnBK,WAAW,GAAG/B,6BAA6B,CAAC0B,eAAe,CAACE,KAAK,EAAEzB,UAAU,CAAC;EAChF,MAAM6B,GAAG,GAAG5B,UAAU,CAAC0B,WAAW,EAAEL,uBAAuB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,uBAAuB,CAACO,GAAG,EAAEtB,UAAU,CAACsB,GAAG,CAAC;EAC3H,MAAMC,cAAc,GAAGjB,iBAAiB,GAAGA,iBAAiB,CAACe,WAAW,CAAC,GAAG,CAAC,CAAC;EAC9E,MAAMG,eAAe,GAAGnC,QAAQ,CAAC,CAAC,CAAC,EAAEe,UAAU,EAAEmB,cAAc,CAAC;EAChE,MAAME,aAAa,GAAG1B,IAAI,KAAK,MAAM,GAAGoB,aAAa,IAAIT,aAAa,GAAGS,aAAa;EACtF,MAAMD,KAAK,GAAGvB,gBAAgB,CAACO,WAAW,EAAEb,QAAQ,CAAC,CAAC,CAAC,EAAEU,IAAI,KAAK,MAAM,IAAI,CAACW,aAAa,IAAI,CAACC,KAAK,CAACZ,IAAI,CAAC,IAAIQ,sBAAsB,EAAER,IAAI,KAAK,MAAM,IAAI,CAACY,KAAK,CAACZ,IAAI,CAAC,IAAIQ,sBAAsB,EAAEc,WAAW,EAAEI,aAAa,IAAI;IAC7NC,EAAE,EAAED;EACN,CAAC,EAAE;IACDH;EACF,CAAC,CAAC,EAAEE,eAAe,CAAC;EACpBG,MAAM,CAACC,IAAI,CAACL,cAAc,CAAC,CAACM,OAAO,CAACC,QAAQ,IAAI;IAC9C,OAAOZ,KAAK,CAACY,QAAQ,CAAC;EACxB,CAAC,CAAC;EACF,OAAO,CAAC5B,WAAW,EAAEgB,KAAK,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}