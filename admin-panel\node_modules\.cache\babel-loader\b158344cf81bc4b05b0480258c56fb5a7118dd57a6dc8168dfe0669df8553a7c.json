{"ast": null, "code": "var Symbol = require('./_Symbol'),\n  getRawTag = require('./_getRawTag'),\n  objectToString = require('./_objectToString');\n\n/** `Object#toString` result references. */\nvar nullTag = '[object Null]',\n  undefinedTag = '[object Undefined]';\n\n/** Built-in value references. */\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\n/**\n * The base implementation of `getTag` without fallbacks for buggy environments.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nfunction baseGetTag(value) {\n  if (value == null) {\n    return value === undefined ? undefinedTag : nullTag;\n  }\n  return symToStringTag && symToStringTag in Object(value) ? getRawTag(value) : objectToString(value);\n}\nmodule.exports = baseGetTag;", "map": {"version": 3, "names": ["Symbol", "require", "getRawTag", "objectToString", "nullTag", "undefinedTag", "symToStringTag", "toStringTag", "undefined", "baseGetTag", "value", "Object", "module", "exports"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/lodash/_baseGetTag.js"], "sourcesContent": ["var Symbol = require('./_Symbol'),\n    getRawTag = require('./_getRawTag'),\n    objectToString = require('./_objectToString');\n\n/** `Object#toString` result references. */\nvar nullTag = '[object Null]',\n    undefinedTag = '[object Undefined]';\n\n/** Built-in value references. */\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\n/**\n * The base implementation of `getTag` without fallbacks for buggy environments.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nfunction baseGetTag(value) {\n  if (value == null) {\n    return value === undefined ? undefinedTag : nullTag;\n  }\n  return (symToStringTag && symToStringTag in Object(value))\n    ? getRawTag(value)\n    : objectToString(value);\n}\n\nmodule.exports = baseGetTag;\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,WAAW,CAAC;EAC7BC,SAAS,GAAGD,OAAO,CAAC,cAAc,CAAC;EACnCE,cAAc,GAAGF,OAAO,CAAC,mBAAmB,CAAC;;AAEjD;AACA,IAAIG,OAAO,GAAG,eAAe;EACzBC,YAAY,GAAG,oBAAoB;;AAEvC;AACA,IAAIC,cAAc,GAAGN,MAAM,GAAGA,MAAM,CAACO,WAAW,GAAGC,SAAS;;AAE5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,IAAIA,KAAK,IAAI,IAAI,EAAE;IACjB,OAAOA,KAAK,KAAKF,SAAS,GAAGH,YAAY,GAAGD,OAAO;EACrD;EACA,OAAQE,cAAc,IAAIA,cAAc,IAAIK,MAAM,CAACD,KAAK,CAAC,GACrDR,SAAS,CAACQ,KAAK,CAAC,GAChBP,cAAc,CAACO,KAAK,CAAC;AAC3B;AAEAE,MAAM,CAACC,OAAO,GAAGJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}