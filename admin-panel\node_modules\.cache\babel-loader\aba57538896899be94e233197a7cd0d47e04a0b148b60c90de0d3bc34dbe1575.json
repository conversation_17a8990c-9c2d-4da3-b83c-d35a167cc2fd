{"ast": null, "code": "var arrayPush = require('./_arrayPush'),\n  getPrototype = require('./_getPrototype'),\n  getSymbols = require('./_getSymbols'),\n  stubArray = require('./stubArray');\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeGetSymbols = Object.getOwnPropertySymbols;\n\n/**\n * Creates an array of the own and inherited enumerable symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of symbols.\n */\nvar getSymbolsIn = !nativeGetSymbols ? stubArray : function (object) {\n  var result = [];\n  while (object) {\n    arrayPush(result, getSymbols(object));\n    object = getPrototype(object);\n  }\n  return result;\n};\nmodule.exports = getSymbolsIn;", "map": {"version": 3, "names": ["arrayPush", "require", "getPrototype", "getSymbols", "stubArray", "nativeGetSymbols", "Object", "getOwnPropertySymbols", "getSymbolsIn", "object", "result", "module", "exports"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/lodash/_getSymbolsIn.js"], "sourcesContent": ["var arrayPush = require('./_arrayPush'),\n    getPrototype = require('./_getPrototype'),\n    getSymbols = require('./_getSymbols'),\n    stubArray = require('./stubArray');\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeGetSymbols = Object.getOwnPropertySymbols;\n\n/**\n * Creates an array of the own and inherited enumerable symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of symbols.\n */\nvar getSymbolsIn = !nativeGetSymbols ? stubArray : function(object) {\n  var result = [];\n  while (object) {\n    arrayPush(result, getSymbols(object));\n    object = getPrototype(object);\n  }\n  return result;\n};\n\nmodule.exports = getSymbolsIn;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;EACnCC,YAAY,GAAGD,OAAO,CAAC,iBAAiB,CAAC;EACzCE,UAAU,GAAGF,OAAO,CAAC,eAAe,CAAC;EACrCG,SAAS,GAAGH,OAAO,CAAC,aAAa,CAAC;;AAEtC;AACA,IAAII,gBAAgB,GAAGC,MAAM,CAACC,qBAAqB;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,YAAY,GAAG,CAACH,gBAAgB,GAAGD,SAAS,GAAG,UAASK,MAAM,EAAE;EAClE,IAAIC,MAAM,GAAG,EAAE;EACf,OAAOD,MAAM,EAAE;IACbT,SAAS,CAACU,MAAM,EAAEP,UAAU,CAACM,MAAM,CAAC,CAAC;IACrCA,MAAM,GAAGP,YAAY,CAACO,MAAM,CAAC;EAC/B;EACA,OAAOC,MAAM;AACf,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAGJ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}