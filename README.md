# ProChat - Social & Financial Platform

ProChat ni app ya kisasa inayochanganya huduma za mitandao ya kijamii na miamala ya kifedha. Lengo lake ni kuitikia mahitaji ya jamii ya Kitanzania na Afrika Mashariki kwa ujumla.

## 🏗️ Architecture

- **Frontend Mobile**: React Native (iOS & Android)
- **Frontend Web**: React (Admin Panel)
- **Backend**: Spring Boot (Java 17)
- **Database**: MySQL 8.0
- **Cloud Storage**: AWS S3
- **Authentication**: JWT

## ✨ Features

### Main Tabs
1. **💬 Chats** - WhatsApp-style messaging
   - Private & group chats
   - Media sharing
   - Voice & video calls
   - Message status (sent, delivered, read)

2. **🏠 Home** - Twitter-style social feed
   - Create posts with text, images, videos
   - Like, comment, share posts
   - Gift system for monetization
   - Real-time feed updates

3. **🔍 Discover** - Content discovery
   - Stories & channels
   - News articles with text-to-speech
   - Short videos (TikTok-style)
   - Live streaming
   - Games & entertainment

4. **👤 Me** - Profile & wallet management
   - User profile with verification
   - ProPay digital wallet
   - Settings & privacy controls
   - Invite friends system

### 💰 ProPay Digital Wallet
- **Send/Receive Money**: Instant transfers between users
- **Deposit/Withdraw**: Multiple payment methods
- **Bill Payments**: Government & utility bills
- **Agent Services**: ProZone for agents & merchants
- **Budget Planning**: Auto-payment features
- **Transaction History**: Detailed financial records

### 🏢 Business Features
- **Advertisement System**: Targeted ads with analytics
- **Ticket Booking**: Event tickets with QR codes
- **Live Streaming**: Interactive streaming with gifts
- **Meeting Platform**: Zoom-like video conferencing
- **Agent Network**: ProZone for financial agents

## 📁 Project Structure

```
ProChat/
├── backend/                 # Spring Boot API
│   ├── src/main/java/com/prochat/
│   │   ├── controller/      # REST Controllers
│   │   ├── model/          # JPA Entities
│   │   ├── repository/     # Data Access Layer
│   │   ├── service/        # Business Logic
│   │   ├── security/       # JWT & Security Config
│   │   ├── dto/           # Data Transfer Objects
│   │   └── util/          # Utility Classes
│   └── src/main/resources/
│       └── application.yml # Configuration
├── mobile/                  # React Native App
│   ├── src/
│   │   ├── screens/        # App Screens
│   │   ├── components/     # Reusable Components
│   │   ├── services/       # API Services
│   │   ├── utils/         # Helper Functions
│   │   └── styles/        # Styling
│   ├── App.js             # Main App Component
│   └── package.json       # Dependencies
├── web-admin/              # React Admin Panel
│   ├── src/
│   │   ├── pages/         # Admin Pages
│   │   ├── components/    # UI Components
│   │   └── services/      # API Services
│   └── package.json       # Dependencies
├── database/               # MySQL Schema
│   ├── init_database.sql  # Database Setup
│   └── README.md          # Database Guide
└── README.md              # This file
```

## 🚀 Quick Start

### Prerequisites
- Java 17+
- Node.js 16+
- MySQL 8.0+
- React Native CLI
- Android Studio / Xcode

### 1. Database Setup
```bash
# Create database
mysql -u root -p
CREATE DATABASE prochat_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# Run schema
mysql -u root -p prochat_db < database/init_database.sql
```

### 2. Backend Setup
```bash
cd backend
./mvnw spring-boot:run
```
Backend will run on `http://localhost:8080`

### 3. Mobile App Setup
```bash
cd mobile
npm install
npx react-native run-android  # For Android
npx react-native run-ios      # For iOS
```

### 4. Web Admin Setup
```bash
cd web-admin
npm install
npm start
```
Admin panel will run on `http://localhost:3000`

## 🔧 Configuration

### Database Configuration
Update `backend/src/main/resources/application.yml`:
```yaml
spring:
  datasource:
    url: **************************************
    username: root
    password: Ram$0101
```

### AWS S3 Configuration
Set environment variables:
```bash
export AWS_ACCESS_KEY=your_access_key
export AWS_SECRET_KEY=your_secret_key
```

## 📱 Mobile App Features

### Authentication Screens
- Welcome screen with app introduction
- Login with username/phone + password
- Registration with full validation
- Forgot password with email reset

### Main Features
- **Real-time messaging** with WebSocket
- **Social media feed** with infinite scroll
- **Digital wallet** with secure PIN
- **Live streaming** with interactive features
- **Video calls** with WebRTC
- **File sharing** with AWS S3

## 🌐 Web Admin Features

### Dashboard
- User statistics and analytics
- Transaction monitoring
- Real-time activity feed
- Revenue tracking

### User Management
- View all users with filtering
- User verification system
- Account status management
- Role assignment

### Financial Monitoring
- Transaction history
- Wallet balances
- Agent commissions
- Revenue analytics

### Content Moderation
- Post management
- Chat monitoring
- Report handling
- Content filtering

## 🔐 Security Features

### Authentication & Authorization
- JWT-based authentication
- Role-based access control (RBAC)
- Secure password hashing (bcrypt)
- PIN encryption for wallet

### Financial Security
- Transaction encryption
- Fraud detection
- Daily/monthly limits
- Audit trails

### Data Protection
- Input validation
- SQL injection prevention
- XSS protection
- CORS configuration

## 🧪 Testing

### Backend Testing
```bash
cd backend
./mvnw test
```

### Mobile Testing
```bash
cd mobile
npm test
```

### Web Admin Testing
```bash
cd web-admin
npm test
```

## 📊 API Documentation

### Authentication Endpoints
- `POST /api/auth/signin` - User login
- `POST /api/auth/signup` - User registration
- `POST /api/auth/refresh` - Token refresh

### User Endpoints
- `GET /api/users/me` - Get current user
- `PUT /api/users/{id}` - Update user
- `GET /api/users/search` - Search users

### Wallet Endpoints
- `GET /api/wallet` - Get wallet info
- `POST /api/wallet/send` - Send money
- `POST /api/wallet/deposit` - Deposit money
- `GET /api/wallet/transactions` - Transaction history

### Post Endpoints
- `GET /api/posts/feed` - Get social feed
- `POST /api/posts` - Create post
- `POST /api/posts/{id}/like` - Like post

## 🌍 Localization

App supports Swahili and English:
- UI text in Swahili for local users
- Error messages in user's language
- Currency formatting (TSH)
- Date/time formatting

## 🚀 Deployment

### Backend Deployment
```bash
cd backend
./mvnw clean package
java -jar target/prochat-backend-1.0.0.jar
```

### Mobile App Deployment
```bash
cd mobile
# Android
npx react-native build-android --release

# iOS
npx react-native build-ios --release
```

### Web Admin Deployment
```bash
cd web-admin
npm run build
# Deploy build/ folder to web server
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👥 Team

- **Backend Development**: Spring Boot, MySQL, AWS
- **Mobile Development**: React Native, iOS, Android
- **Web Development**: React, Material-UI
- **UI/UX Design**: Modern, user-friendly interface
- **DevOps**: CI/CD, deployment, monitoring

## 📞 Support

For support and questions:
- Email: <EMAIL>
- Phone: +255 123 456 789
- Website: https://prochat.com

## 🎯 Roadmap

### Phase 1 (Current)
- ✅ Basic authentication
- ✅ User management
- ✅ Wallet functionality
- ✅ Social media features
- ✅ Admin panel

### Phase 2 (Next)
- 🔄 Live streaming
- 🔄 Video calls
- 🔄 Advanced analytics
- 🔄 Mobile app optimization

### Phase 3 (Future)
- 📋 AI-powered features
- 📋 Advanced security
- 📋 International expansion
- 📋 Blockchain integration

---

**ProChat** - Connecting Tanzania through social interaction and financial empowerment! 🇹🇿
