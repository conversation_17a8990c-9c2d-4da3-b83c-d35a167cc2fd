package com.prochat.model.tax;

import com.prochat.model.User;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "tax_transactions")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TaxTransaction {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "tax_config_id", nullable = false)
    private TaxConfiguration taxConfiguration;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @Column(name = "original_transaction_id")
    private Long originalTransactionId;
    
    @Column(name = "transaction_type", nullable = false)
    private String transactionType; // SEND_MONEY, WITHDRAW, DONATION, etc.
    
    @Column(name = "gross_amount", nullable = false, precision = 15, scale = 2)
    private BigDecimal grossAmount; // Amount before tax
    
    @Column(name = "tax_rate", nullable = false, precision = 5, scale = 4)
    private BigDecimal taxRate; // Rate used at time of transaction
    
    @Column(name = "tax_amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal taxAmount; // Calculated tax amount
    
    @Column(name = "net_amount", nullable = false, precision = 15, scale = 2)
    private BigDecimal netAmount; // Amount after tax deduction
    
    @Column(name = "currency", nullable = false)
    private String currency = "TZS";
    
    @Column(name = "tax_reference", nullable = false, unique = true)
    private String taxReference; // Unique tax transaction reference
    
    @Column(name = "receipt_number")
    private String receiptNumber; // Tax receipt number
    
    @Column(name = "description")
    private String description;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private TaxStatus status = TaxStatus.COLLECTED;
    
    @Column(name = "collection_date", nullable = false)
    private LocalDateTime collectionDate;
    
    @Column(name = "remittance_date")
    private LocalDateTime remittanceDate; // When tax was remitted to authority
    
    @Column(name = "remittance_reference")
    private String remittanceReference;
    
    @Column(name = "financial_year")
    private String financialYear; // e.g., "2024/2025"
    
    @Column(name = "tax_period")
    private String taxPeriod; // e.g., "January 2024"
    
    @Column(name = "is_remitted", nullable = false)
    private Boolean isRemitted = false;
    
    @Column(name = "remitted_by")
    private Long remittedBy; // Admin who remitted
    
    @Column(name = "notes")
    private String notes;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        collectionDate = LocalDateTime.now();
        
        // Generate tax reference
        if (taxReference == null) {
            taxReference = generateTaxReference();
        }
        
        // Set financial year and tax period
        setFinancialYearAndPeriod();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    private String generateTaxReference() {
        return "TAX" + System.currentTimeMillis() + 
               String.format("%04d", (int)(Math.random() * 10000));
    }
    
    private void setFinancialYearAndPeriod() {
        LocalDateTime now = LocalDateTime.now();
        int year = now.getYear();
        int month = now.getMonthValue();
        
        // Tanzania financial year: July to June
        if (month >= 7) {
            financialYear = year + "/" + (year + 1);
        } else {
            financialYear = (year - 1) + "/" + year;
        }
        
        // Set tax period (month/year)
        taxPeriod = now.getMonth().name() + " " + year;
    }
    
    public enum TaxStatus {
        COLLECTED("Imekusanywa"),
        PENDING_REMITTANCE("Inasubiri Kutumwa"),
        REMITTED("Imetumwa kwa Serikali"),
        DISPUTED("Ina Mgogoro"),
        REFUNDED("Imerudishwa"),
        CANCELLED("Imeghairiwa");
        
        private final String swahiliName;
        
        TaxStatus(String swahiliName) {
            this.swahiliName = swahiliName;
        }
        
        public String getSwahiliName() {
            return swahiliName;
        }
    }
}

@Entity
@Table(name = "tax_remittances")
@Data
@NoArgsConstructor
@AllArgsConstructor
class TaxRemittance {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "remittance_reference", nullable = false, unique = true)
    private String remittanceReference;
    
    @Column(name = "tax_period", nullable = false)
    private String taxPeriod; // e.g., "January 2024"
    
    @Column(name = "financial_year", nullable = false)
    private String financialYear;
    
    @Column(name = "total_tax_collected", nullable = false, precision = 15, scale = 2)
    private BigDecimal totalTaxCollected;
    
    @Column(name = "total_transactions")
    private Long totalTransactions;
    
    @Column(name = "remittance_date", nullable = false)
    private LocalDateTime remittanceDate;
    
    @Column(name = "due_date")
    private LocalDateTime dueDate;
    
    @Column(name = "payment_method")
    private String paymentMethod; // BANK_TRANSFER, ONLINE, etc.
    
    @Column(name = "payment_reference")
    private String paymentReference;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private RemittanceStatus status = RemittanceStatus.PENDING;
    
    @Column(name = "tra_acknowledgment")
    private String traAcknowledgment; // TRA receipt/acknowledgment
    
    @Column(name = "remitted_by", nullable = false)
    private Long remittedBy;
    
    @Column(name = "notes")
    private String notes;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        
        if (remittanceReference == null) {
            remittanceReference = "REM" + System.currentTimeMillis();
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    public enum RemittanceStatus {
        PENDING("Inasubiri"),
        SUBMITTED("Imetumwa"),
        ACKNOWLEDGED("Imepokewa na TRA"),
        COMPLETED("Imekamilika"),
        FAILED("Imeshindwa"),
        DISPUTED("Ina Mgogoro");
        
        private final String swahiliName;
        
        RemittanceStatus(String swahiliName) {
            this.swahiliName = swahiliName;
        }
        
        public String getSwahiliName() {
            return swahiliName;
        }
    }
}
