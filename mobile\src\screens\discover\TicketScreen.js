import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import QRCode from 'react-native-qrcode-svg';
import { colors, typography, spacing } from '../../theme/theme';
import { ticketsAPI, walletAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

export default function TicketScreen({ navigation, route }) {
  const { eventId, ticketType } = route.params;
  const { user } = useAuth();
  const [event, setEvent] = useState(null);
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [loading, setLoading] = useState(true);
  const [purchasing, setPurchasing] = useState(false);
  const [showQRModal, setShowQRModal] = useState(false);
  const [purchasedTicket, setPurchasedTicket] = useState(null);

  useEffect(() => {
    loadEventDetails();
  }, [eventId]);

  const loadEventDetails = async () => {
    try {
      const response = await ticketsAPI.getEventTickets(eventId);
      if (response.success) {
        setEvent(response.data);
        if (ticketType) {
          const ticket = response.data.ticketTypes.find(t => t.type === ticketType);
          setSelectedTicket(ticket);
        }
      }
    } catch (error) {
      console.error('Error loading event:', error);
      Alert.alert('Hitilafu', 'Imeshindwa kupakia taarifa za tukio');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const calculateTotal = () => {
    if (!selectedTicket) return 0;
    return selectedTicket.price * quantity;
  };

  const calculateVAT = () => {
    const total = calculateTotal();
    return total * 0.18; // 18% VAT
  };

  const calculateGrandTotal = () => {
    return calculateTotal() + calculateVAT();
  };

  const handleTicketSelect = (ticket) => {
    setSelectedTicket(ticket);
    setQuantity(1);
  };

  const handleQuantityChange = (change) => {
    const newQuantity = quantity + change;
    if (newQuantity >= 1 && newQuantity <= selectedTicket.maxPerPerson) {
      setQuantity(newQuantity);
    }
  };

  const handlePurchase = async () => {
    if (!selectedTicket) {
      Alert.alert('Hitilafu', 'Chagua aina ya tiketi');
      return;
    }

    try {
      setPurchasing(true);

      const purchaseData = {
        eventId: eventId,
        ticketTypeId: selectedTicket.id,
        quantity: quantity,
        totalAmount: calculateGrandTotal(),
      };

      const response = await ticketsAPI.purchaseTicket(purchaseData);

      if (response.success) {
        setPurchasedTicket(response.data);
        setShowQRModal(true);
        Alert.alert(
          'Mafanikio!',
          `Umefanikiwa kununua tiketi ${quantity}!\n\nTiketi zako zimehifadhiwa kwenye akaunti yako.`,
          [
            {
              text: 'Sawa',
              onPress: () => {
                setShowQRModal(false);
                navigation.goBack();
              }
            }
          ]
        );
      } else {
        Alert.alert('Hitilafu', response.message || 'Imeshindwa kununua tiketi');
      }
    } catch (error) {
      console.error('Purchase error:', error);
      Alert.alert('Hitilafu', error.message || 'Hitilafu ya mtandao');
    } finally {
      setPurchasing(false);
    }
  };

  const getTicketTypeIcon = (type) => {
    switch (type) {
      case 'VIP': return 'star';
      case 'PREMIUM': return 'workspace-premium';
      case 'REGULAR': return 'event-seat';
      case 'STUDENT': return 'school';
      default: return 'confirmation-number';
    }
  };

  const getTicketTypeColor = (type) => {
    switch (type) {
      case 'VIP': return colors.warning;
      case 'PREMIUM': return colors.primary;
      case 'REGULAR': return colors.success;
      case 'STUDENT': return colors.info;
      default: return colors.gray;
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Inapakia tiketi...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!event) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text>Tukio halipatikani</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Nunua Tiketi</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content}>
        {/* Event Info */}
        <View style={styles.eventInfo}>
          <Text style={styles.eventTitle}>{event.title}</Text>
          <View style={styles.eventDetails}>
            <View style={styles.eventDetail}>
              <Icon name="event" size={16} color={colors.textSecondary} />
              <Text style={styles.eventDetailText}>
                {new Date(event.startDate).toLocaleDateString('sw-TZ')}
              </Text>
            </View>
            <View style={styles.eventDetail}>
              <Icon name="access-time" size={16} color={colors.textSecondary} />
              <Text style={styles.eventDetailText}>
                {new Date(event.startDate).toLocaleTimeString('sw-TZ', {
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </Text>
            </View>
            <View style={styles.eventDetail}>
              <Icon name="location-on" size={16} color={colors.textSecondary} />
              <Text style={styles.eventDetailText}>{event.location}</Text>
            </View>
          </View>
        </View>

        {/* Ticket Types */}
        <View style={styles.ticketTypesSection}>
          <Text style={styles.sectionTitle}>Chagua Aina ya Tiketi</Text>
          {event.ticketTypes.map((ticket) => (
            <TouchableOpacity
              key={ticket.id}
              style={[
                styles.ticketTypeItem,
                selectedTicket?.id === ticket.id && styles.selectedTicketType
              ]}
              onPress={() => handleTicketSelect(ticket)}
            >
              <View style={[
                styles.ticketTypeIcon,
                { backgroundColor: getTicketTypeColor(ticket.type) + '20' }
              ]}>
                <Icon 
                  name={getTicketTypeIcon(ticket.type)} 
                  size={24} 
                  color={getTicketTypeColor(ticket.type)} 
                />
              </View>
              
              <View style={styles.ticketTypeInfo}>
                <Text style={styles.ticketTypeName}>{ticket.name}</Text>
                <Text style={styles.ticketTypeDescription}>{ticket.description}</Text>
                <Text style={styles.ticketTypePrice}>
                  {formatCurrency(ticket.price)}
                </Text>
              </View>
              
              <View style={styles.ticketTypeStatus}>
                <Text style={styles.ticketAvailable}>
                  {ticket.availableQuantity} zilizobaki
                </Text>
                {selectedTicket?.id === ticket.id && (
                  <Icon name="check-circle" size={24} color={colors.success} />
                )}
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Quantity Selector */}
        {selectedTicket && (
          <View style={styles.quantitySection}>
            <Text style={styles.sectionTitle}>Idadi ya Tiketi</Text>
            <View style={styles.quantityControls}>
              <TouchableOpacity
                style={styles.quantityButton}
                onPress={() => handleQuantityChange(-1)}
                disabled={quantity <= 1}
              >
                <Icon name="remove" size={20} color={colors.primary} />
              </TouchableOpacity>
              
              <Text style={styles.quantityText}>{quantity}</Text>
              
              <TouchableOpacity
                style={styles.quantityButton}
                onPress={() => handleQuantityChange(1)}
                disabled={quantity >= selectedTicket.maxPerPerson}
              >
                <Icon name="add" size={20} color={colors.primary} />
              </TouchableOpacity>
            </View>
            <Text style={styles.quantityLimit}>
              Kiwango cha juu: {selectedTicket.maxPerPerson} kwa mtu mmoja
            </Text>
          </View>
        )}

        {/* Price Breakdown */}
        {selectedTicket && (
          <View style={styles.priceBreakdown}>
            <Text style={styles.sectionTitle}>Muhtasari wa Bei</Text>
            
            <View style={styles.priceRow}>
              <Text style={styles.priceLabel}>
                {selectedTicket.name} x {quantity}
              </Text>
              <Text style={styles.priceValue}>
                {formatCurrency(calculateTotal())}
              </Text>
            </View>
            
            <View style={styles.priceRow}>
              <Text style={styles.priceLabel}>VAT (18%)</Text>
              <Text style={styles.priceValue}>
                {formatCurrency(calculateVAT())}
              </Text>
            </View>
            
            <View style={styles.divider} />
            
            <View style={styles.priceRow}>
              <Text style={styles.priceLabelTotal}>Jumla</Text>
              <Text style={styles.priceValueTotal}>
                {formatCurrency(calculateGrandTotal())}
              </Text>
            </View>
          </View>
        )}
      </ScrollView>

      {/* Purchase Button */}
      {selectedTicket && (
        <View style={styles.purchaseSection}>
          <TouchableOpacity
            style={[
              styles.purchaseButton,
              purchasing && styles.purchaseButtonDisabled
            ]}
            onPress={handlePurchase}
            disabled={purchasing}
          >
            <Icon name="payment" size={20} color={colors.white} />
            <Text style={styles.purchaseButtonText}>
              {purchasing ? 'Inanunua...' : `Nunua - ${formatCurrency(calculateGrandTotal())}`}
            </Text>
          </TouchableOpacity>
        </View>
      )}

      {/* QR Code Modal */}
      <Modal
        visible={showQRModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowQRModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Tiketi Yako</Text>
            
            {purchasedTicket && (
              <>
                <View style={styles.qrContainer}>
                  <QRCode
                    value={purchasedTicket.qrCode}
                    size={200}
                    color={colors.text}
                    backgroundColor={colors.white}
                  />
                </View>
                
                <View style={styles.ticketDetails}>
                  <Text style={styles.ticketNumber}>
                    Namba ya Tiketi: {purchasedTicket.ticketNumber}
                  </Text>
                  <Text style={styles.ticketInfo}>
                    {event.title}
                  </Text>
                  <Text style={styles.ticketInfo}>
                    {new Date(event.startDate).toLocaleDateString('sw-TZ')}
                  </Text>
                </View>
              </>
            )}
            
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setShowQRModal(false)}
            >
              <Text style={styles.modalCloseText}>Funga</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  eventInfo: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginBottom: spacing.sm,
  },
  eventTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  eventDetails: {
    gap: spacing.sm,
  },
  eventDetail: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  eventDetailText: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginLeft: spacing.sm,
  },
  ticketTypesSection: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginBottom: spacing.sm,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  ticketTypeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    marginBottom: spacing.sm,
  },
  selectedTicketType: {
    borderColor: colors.primary,
    backgroundColor: colors.primary + '10',
  },
  ticketTypeIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  ticketTypeInfo: {
    flex: 1,
  },
  ticketTypeName: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  ticketTypeDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  ticketTypePrice: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary,
  },
  ticketTypeStatus: {
    alignItems: 'flex-end',
  },
  ticketAvailable: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  quantitySection: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginBottom: spacing.sm,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.sm,
  },
  quantityButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
  },
  quantityText: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
    marginHorizontal: spacing.xl,
  },
  quantityLimit: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  priceBreakdown: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginBottom: spacing.sm,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  priceLabel: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
  },
  priceValue: {
    fontSize: typography.fontSize.md,
    color: colors.text,
    fontWeight: typography.fontWeight.medium,
  },
  priceLabelTotal: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
  },
  priceValueTotal: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary,
  },
  divider: {
    height: 1,
    backgroundColor: colors.border,
    marginVertical: spacing.sm,
  },
  purchaseSection: {
    padding: spacing.lg,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  purchaseButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingVertical: spacing.lg,
    borderRadius: 8,
  },
  purchaseButtonDisabled: {
    backgroundColor: colors.gray,
  },
  purchaseButtonText: {
    color: colors.white,
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    marginLeft: spacing.sm,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.xl,
    width: '90%',
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
    marginBottom: spacing.lg,
  },
  qrContainer: {
    padding: spacing.lg,
    backgroundColor: colors.white,
    borderRadius: 8,
    marginBottom: spacing.lg,
  },
  ticketDetails: {
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  ticketNumber: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary,
    marginBottom: spacing.sm,
  },
  ticketInfo: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
  modalCloseButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.md,
    borderRadius: 8,
  },
  modalCloseText: {
    color: colors.white,
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
  },
});
