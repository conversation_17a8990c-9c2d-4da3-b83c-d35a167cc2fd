import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
  Linking,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors, typography, spacing } from '../../theme/theme';
import { supportAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

export default function HelpSupportScreen({ navigation }) {
  const { user } = useAuth();
  const [expandedFAQ, setExpandedFAQ] = useState(null);
  const [contactForm, setContactForm] = useState({
    subject: '',
    message: '',
    category: 'GENERAL',
  });
  const [submitting, setSubmitting] = useState(false);

  const faqData = [
    {
      id: 1,
      question: '<PERSON>si ya kuunda akaunti ya ProChat?',
      answer: '<PERSON><PERSON><PERSON> "<PERSON>unge" kwenye ukurasa wa kwanza, jaza taarifa zako za kibina<PERSON>i, na uthibitishe barua pepe yako. Baada ya hapo utaweza kutumia ProChat kikamilifu.',
    },
    {
      id: 2,
      question: 'Jinsi ya kutumia ProPay Wallet?',
      answer: 'ProPay ni wallet yako ya kidijitali. Unaweza kuweka pesa, kutuma kwa marafiki, kulipa bili, na kununua huduma mbalimbali. Bonyeza "ProPay" kwenye tab ya "Mimi" kuanza.',
    },
    {
      id: 3,
      question: 'Je, ni salama kutumia ProChat?',
      answer: 'Ndio! ProChat inatumia encryption ya hali ya juu kulinda data yako. Mazungumzo yako ni ya faragha na pesa zako ni salama kwenye ProPay wallet.',
    },
    {
      id: 4,
      question: 'Jinsi ya kuwa wakala wa ProChat?',
      answer: 'Ingia kwenye "ProZone" na ubonyeze "Jiunge Sasa". Utahitaji kutoa taarifa zako za kibiashara na kupitia mafunzo mafupi. Baada ya hapo utaweza kuanza kupata mapato.',
    },
    {
      id: 5,
      question: 'Jinsi ya kupata msaada wa haraka?',
      answer: 'Unaweza kutupigia simu 24/7 kwenye +255 123 456 789, kutuma <NAME_EMAIL>, au kutumia live chat kwenye programu.',
    },
    {
      id: 6,
      question: 'Jinsi ya kufuta akaunti yangu?',
      answer: 'Ingia kwenye Mipangilio > Usalama > Futa Akaunti. Lakini kabla ya kufuta, hakikisha umehama pesa zako zote kutoka ProPay wallet.',
    },
  ];

  const contactOptions = [
    {
      title: 'Simu ya Msaada',
      subtitle: '+255 123 456 789',
      icon: 'phone',
      color: colors.success,
      onPress: () => Linking.openURL('tel:+************'),
    },
    {
      title: 'Barua Pepe',
      subtitle: '<EMAIL>',
      icon: 'email',
      color: colors.primary,
      onPress: () => Linking.openURL('mailto:<EMAIL>'),
    },
    {
      title: 'WhatsApp',
      subtitle: 'Msaada wa haraka',
      icon: 'chat',
      color: colors.success,
      onPress: () => Linking.openURL('https://wa.me/************'),
    },
    {
      title: 'Live Chat',
      subtitle: 'Mazungumzo ya moja kwa moja',
      icon: 'support-agent',
      color: colors.info,
      onPress: () => navigation.navigate('LiveChat'),
    },
  ];

  const categories = [
    { value: 'GENERAL', label: 'Maswali ya Jumla' },
    { value: 'TECHNICAL', label: 'Matatizo ya Kiufundi' },
    { value: 'PAYMENT', label: 'Malipo na ProPay' },
    { value: 'ACCOUNT', label: 'Akaunti Yangu' },
    { value: 'BUSINESS', label: 'ProZone na Biashara' },
    { value: 'REPORT', label: 'Ripoti Tatizo' },
  ];

  const handleFAQToggle = (faqId) => {
    setExpandedFAQ(expandedFAQ === faqId ? null : faqId);
  };

  const handleSubmitForm = async () => {
    if (!contactForm.subject.trim() || !contactForm.message.trim()) {
      Alert.alert('Hitilafu', 'Jaza sehemu zote za lazima');
      return;
    }

    try {
      setSubmitting(true);
      const response = await supportAPI.submitTicket({
        ...contactForm,
        userId: user?.id,
      });

      if (response.success) {
        Alert.alert(
          'Mafanikio!',
          'Ujumbe wako umepokewa. Tutajibu ndani ya masaa 24.',
          [
            {
              text: 'Sawa',
              onPress: () => {
                setContactForm({
                  subject: '',
                  message: '',
                  category: 'GENERAL',
                });
              }
            }
          ]
        );
      } else {
        Alert.alert('Hitilafu', response.message || 'Imeshindwa kutuma ujumbe');
      }
    } catch (error) {
      console.error('Submit error:', error);
      Alert.alert('Hitilafu', 'Imeshindwa kutuma ujumbe');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Msaada na Maswali</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content}>
        {/* Quick Contact Options */}
        <View style={styles.contactSection}>
          <Text style={styles.sectionTitle}>Wasiliana Nasi</Text>
          <View style={styles.contactGrid}>
            {contactOptions.map((option, index) => (
              <TouchableOpacity
                key={index}
                style={styles.contactOption}
                onPress={option.onPress}
              >
                <View style={[
                  styles.contactIcon,
                  { backgroundColor: option.color + '20' }
                ]}>
                  <Icon name={option.icon} size={24} color={option.color} />
                </View>
                <Text style={styles.contactTitle}>{option.title}</Text>
                <Text style={styles.contactSubtitle}>{option.subtitle}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* FAQ Section */}
        <View style={styles.faqSection}>
          <Text style={styles.sectionTitle}>Maswali Yanayoulizwa Mara Kwa Mara</Text>
          {faqData.map((faq) => (
            <View key={faq.id} style={styles.faqItem}>
              <TouchableOpacity
                style={styles.faqQuestion}
                onPress={() => handleFAQToggle(faq.id)}
              >
                <Text style={styles.faqQuestionText}>{faq.question}</Text>
                <Icon 
                  name={expandedFAQ === faq.id ? "expand-less" : "expand-more"} 
                  size={24} 
                  color={colors.textSecondary} 
                />
              </TouchableOpacity>
              
              {expandedFAQ === faq.id && (
                <View style={styles.faqAnswer}>
                  <Text style={styles.faqAnswerText}>{faq.answer}</Text>
                </View>
              )}
            </View>
          ))}
        </View>

        {/* Contact Form */}
        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Tuma Ujumbe</Text>
          
          {/* Category Selector */}
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Aina ya Swali</Text>
            <View style={styles.categoryGrid}>
              {categories.map((category) => (
                <TouchableOpacity
                  key={category.value}
                  style={[
                    styles.categoryOption,
                    contactForm.category === category.value && styles.selectedCategory
                  ]}
                  onPress={() => setContactForm(prev => ({ ...prev, category: category.value }))}
                >
                  <Text style={[
                    styles.categoryText,
                    contactForm.category === category.value && styles.selectedCategoryText
                  ]}>
                    {category.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Subject Input */}
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Mada *</Text>
            <TextInput
              style={styles.textInput}
              placeholder="Andika mada ya ujumbe wako..."
              placeholderTextColor={colors.textSecondary}
              value={contactForm.subject}
              onChangeText={(text) => setContactForm(prev => ({ ...prev, subject: text }))}
            />
          </View>

          {/* Message Input */}
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Ujumbe *</Text>
            <TextInput
              style={[styles.textInput, styles.messageInput]}
              placeholder="Eleza tatizo lako au swali lako kwa undani..."
              placeholderTextColor={colors.textSecondary}
              value={contactForm.message}
              onChangeText={(text) => setContactForm(prev => ({ ...prev, message: text }))}
              multiline
              numberOfLines={5}
              textAlignVertical="top"
            />
          </View>

          {/* Submit Button */}
          <TouchableOpacity
            style={[
              styles.submitButton,
              submitting && styles.submitButtonDisabled
            ]}
            onPress={handleSubmitForm}
            disabled={submitting}
          >
            <Icon name="send" size={20} color={colors.white} />
            <Text style={styles.submitButtonText}>
              {submitting ? 'Inatuma...' : 'Tuma Ujumbe'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Additional Resources */}
        <View style={styles.resourcesSection}>
          <Text style={styles.sectionTitle}>Rasilimali za Ziada</Text>
          
          <TouchableOpacity style={styles.resourceItem}>
            <Icon name="book" size={24} color={colors.primary} />
            <View style={styles.resourceContent}>
              <Text style={styles.resourceTitle}>Mwongozo wa Mtumiaji</Text>
              <Text style={styles.resourceDescription}>
                Jifunze jinsi ya kutumia ProChat kikamilifu
              </Text>
            </View>
            <Icon name="chevron-right" size={24} color={colors.gray} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.resourceItem}>
            <Icon name="video-library" size={24} color={colors.primary} />
            <View style={styles.resourceContent}>
              <Text style={styles.resourceTitle}>Video za Mafunzo</Text>
              <Text style={styles.resourceDescription}>
                Angalia video za jinsi ya kutumia huduma zetu
              </Text>
            </View>
            <Icon name="chevron-right" size={24} color={colors.gray} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.resourceItem}>
            <Icon name="forum" size={24} color={colors.primary} />
            <View style={styles.resourceContent}>
              <Text style={styles.resourceTitle}>Jamii ya Watumiaji</Text>
              <Text style={styles.resourceDescription}>
                Jiunge na watumiaji wengine na ushiriki uzoefu
              </Text>
            </View>
            <Icon name="chevron-right" size={24} color={colors.gray} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.resourceItem}>
            <Icon name="security" size={24} color={colors.primary} />
            <View style={styles.resourceContent}>
              <Text style={styles.resourceTitle}>Usalama na Faragha</Text>
              <Text style={styles.resourceDescription}>
                Jifunze kuhusu jinsi tunavyolinda data yako
              </Text>
            </View>
            <Icon name="chevron-right" size={24} color={colors.gray} />
          </TouchableOpacity>
        </View>

        {/* Emergency Contact */}
        <View style={styles.emergencySection}>
          <View style={styles.emergencyHeader}>
            <Icon name="warning" size={24} color={colors.error} />
            <Text style={styles.emergencyTitle}>Msaada wa Dharura</Text>
          </View>
          <Text style={styles.emergencyText}>
            Kama una tatizo la dharura la usalama au fedha, tupigie moja kwa moja:
          </Text>
          <TouchableOpacity
            style={styles.emergencyButton}
            onPress={() => Linking.openURL('tel:+************')}
          >
            <Icon name="phone" size={20} color={colors.white} />
            <Text style={styles.emergencyButtonText}>Piga Simu Sasa</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  contactSection: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginBottom: spacing.sm,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  contactGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  contactOption: {
    width: '48%',
    alignItems: 'center',
    padding: spacing.md,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    marginBottom: spacing.md,
  },
  contactIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  contactTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.xs,
    textAlign: 'center',
  },
  contactSubtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  faqSection: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginBottom: spacing.sm,
  },
  faqItem: {
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    marginBottom: spacing.sm,
  },
  faqQuestion: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.md,
  },
  faqQuestionText: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
    flex: 1,
    marginRight: spacing.sm,
  },
  faqAnswer: {
    paddingBottom: spacing.md,
  },
  faqAnswerText: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  formSection: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginBottom: spacing.sm,
  },
  inputGroup: {
    marginBottom: spacing.lg,
  },
  inputLabel: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  categoryOption: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: colors.border,
    backgroundColor: colors.surface,
  },
  selectedCategory: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  categoryText: {
    fontSize: typography.fontSize.sm,
    color: colors.text,
  },
  selectedCategoryText: {
    color: colors.white,
  },
  textInput: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    fontSize: typography.fontSize.md,
    color: colors.text,
    backgroundColor: colors.surface,
  },
  messageInput: {
    height: 120,
    textAlignVertical: 'top',
  },
  submitButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingVertical: spacing.lg,
    borderRadius: 8,
  },
  submitButtonDisabled: {
    backgroundColor: colors.gray,
  },
  submitButtonText: {
    color: colors.white,
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
    marginLeft: spacing.sm,
  },
  resourcesSection: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginBottom: spacing.sm,
  },
  resourceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  resourceContent: {
    flex: 1,
    marginLeft: spacing.md,
  },
  resourceTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  resourceDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
  emergencySection: {
    backgroundColor: colors.error + '10',
    padding: spacing.lg,
    margin: spacing.lg,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.error + '30',
  },
  emergencyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  emergencyTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.error,
    marginLeft: spacing.sm,
  },
  emergencyText: {
    fontSize: typography.fontSize.md,
    color: colors.text,
    lineHeight: 22,
    marginBottom: spacing.md,
  },
  emergencyButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.error,
    paddingVertical: spacing.md,
    borderRadius: 8,
  },
  emergencyButtonText: {
    color: colors.white,
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
    marginLeft: spacing.sm,
  },
});
