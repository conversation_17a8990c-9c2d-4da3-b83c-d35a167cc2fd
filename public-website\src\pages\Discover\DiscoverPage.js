import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Chip,
  Button,
  TextField,
  InputAdornment,
  Tabs,
  Tab,
  Avatar,
} from '@mui/material';
import {
  Search,
  TrendingUp,
  Article,
  VideoLibrary,
  People,
  Visibility,
  ThumbUp,
  Comment,
} from '@mui/icons-material';

export default function DiscoverPage() {
  const [currentTab, setCurrentTab] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');

  const tabs = [
    { label: 'Vyote', icon: <TrendingUp /> },
    { label: 'Habari', icon: <Article /> },
    { label: 'Video', icon: <VideoLibrary /> },
    { label: 'Vituo', icon: <People /> },
  ];

  const trendingNews = [
    {
      id: 1,
      title: 'Uchumi wa Tanzania Unaongezeka kwa Kasi',
      excerpt: 'Takwimu mpya zina<PERSON><PERSON>a ukuaji wa uchumi wa asilimia 6.5 mwaka huu...',
      author: '<PERSON><PERSON> za Uchumi',
      publishedAt: '2024-01-20T10:00:00',
      imageUrl: '/images/economy-news.jpg',
      category: 'Uchumi',
      views: 1250,
      likes: 89,
      comments: 23,
    },
    {
      id: 2,
      title: 'Teknolojia Mpya ya Kilimo Inasaidia Wakulima',
      excerpt: 'Mfumo mpya wa umwagiliaji unaosaidia wakulima kupata mazao mazuri...',
      author: 'Kilimo Leo',
      publishedAt: '2024-01-20T08:30:00',
      imageUrl: '/images/agriculture-tech.jpg',
      category: 'Kilimo',
      views: 890,
      likes: 67,
      comments: 15,
    },
    {
      id: 3,
      title: 'Michezo ya Olimpiki: Tanzania Inajiandaa',
      excerpt: 'Timu ya taifa inaendelea na mazoezi ya mwisho kabla ya michezo...',
      author: 'Michezo Tanzania',
      publishedAt: '2024-01-19T16:45:00',
      imageUrl: '/images/olympics-prep.jpg',
      category: 'Michezo',
      views: 2100,
      likes: 156,
      comments: 45,
    },
  ];

  const trendingVideos = [
    {
      id: 1,
      title: 'Muziki Mpya wa Diamond Platnumz',
      creator: 'Diamond Platnumz Official',
      duration: '3:45',
      views: 125000,
      thumbnail: '/images/diamond-video.jpg',
      category: 'Muziki',
    },
    {
      id: 2,
      title: 'Jinsi ya Kuanza Biashara Ndogo',
      creator: 'Biashara Tips',
      duration: '8:20',
      views: 45000,
      thumbnail: '/images/business-tips.jpg',
      category: 'Elimu',
    },
    {
      id: 3,
      title: 'Mapishi ya Pilau ya Kiswahili',
      creator: 'Mama Mapishi',
      duration: '12:15',
      views: 78000,
      thumbnail: '/images/pilau-recipe.jpg',
      category: 'Mapishi',
    },
  ];

  const popularChannels = [
    {
      id: 1,
      name: 'Habari za Tanzania',
      description: 'Habari za kila siku kutoka Tanzania',
      subscribers: 125000,
      avatar: '/images/news-channel.jpg',
      verified: true,
      category: 'Habari',
    },
    {
      id: 2,
      name: 'Muziki wa Bongo',
      description: 'Muziki mpya wa Bongo Flava',
      subscribers: 89000,
      avatar: '/images/music-channel.jpg',
      verified: true,
      category: 'Muziki',
    },
    {
      id: 3,
      name: 'Teknolojia Tanzania',
      description: 'Habari za teknolojia na uvumbuzi',
      subscribers: 56000,
      avatar: '/images/tech-channel.jpg',
      verified: false,
      category: 'Teknolojia',
    },
  ];

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const renderNewsContent = () => (
    <Grid container spacing={3}>
      {trendingNews.map((article) => (
        <Grid item xs={12} md={6} lg={4} key={article.id}>
          <Card sx={{ height: '100%', cursor: 'pointer', '&:hover': { transform: 'translateY(-4px)' }, transition: 'transform 0.3s' }}>
            <CardMedia
              component="img"
              height="200"
              image={article.imageUrl}
              alt={article.title}
              sx={{ objectFit: 'cover' }}
            />
            <CardContent>
              <Chip label={article.category} size="small" color="primary" sx={{ mb: 1 }} />
              <Typography variant="h6" component="h3" gutterBottom>
                {article.title}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {article.excerpt}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Typography variant="caption" color="text.secondary">
                  {article.author}
                </Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <Visibility sx={{ fontSize: 16 }} />
                    <Typography variant="caption">{formatNumber(article.views)}</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <ThumbUp sx={{ fontSize: 16 }} />
                    <Typography variant="caption">{article.likes}</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <Comment sx={{ fontSize: 16 }} />
                    <Typography variant="caption">{article.comments}</Typography>
                  </Box>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );

  const renderVideoContent = () => (
    <Grid container spacing={3}>
      {trendingVideos.map((video) => (
        <Grid item xs={12} md={6} lg={4} key={video.id}>
          <Card sx={{ height: '100%', cursor: 'pointer', '&:hover': { transform: 'translateY(-4px)' }, transition: 'transform 0.3s' }}>
            <Box sx={{ position: 'relative' }}>
              <CardMedia
                component="img"
                height="200"
                image={video.thumbnail}
                alt={video.title}
                sx={{ objectFit: 'cover' }}
              />
              <Chip
                label={video.duration}
                size="small"
                sx={{
                  position: 'absolute',
                  bottom: 8,
                  right: 8,
                  backgroundColor: 'rgba(0,0,0,0.8)',
                  color: 'white',
                }}
              />
            </Box>
            <CardContent>
              <Chip label={video.category} size="small" color="secondary" sx={{ mb: 1 }} />
              <Typography variant="h6" component="h3" gutterBottom>
                {video.title}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                {video.creator}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {formatNumber(video.views)} mionzi
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );

  const renderChannelsContent = () => (
    <Grid container spacing={3}>
      {popularChannels.map((channel) => (
        <Grid item xs={12} md={6} lg={4} key={channel.id}>
          <Card sx={{ height: '100%', cursor: 'pointer', '&:hover': { transform: 'translateY(-4px)' }, transition: 'transform 0.3s' }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar
                src={channel.avatar}
                sx={{ width: 80, height: 80, mx: 'auto', mb: 2 }}
              >
                {channel.name[0]}
              </Avatar>
              <Typography variant="h6" component="h3" gutterBottom>
                {channel.name}
                {channel.verified && (
                  <Chip label="✓" size="small" color="primary" sx={{ ml: 1 }} />
                )}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {channel.description}
              </Typography>
              <Chip label={channel.category} size="small" variant="outlined" sx={{ mb: 2 }} />
              <Typography variant="caption" display="block" color="text.secondary" sx={{ mb: 2 }}>
                {formatNumber(channel.subscribers)} wafuasi
              </Typography>
              <Button variant="contained" size="small">
                Fuata
              </Button>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );

  const renderContent = () => {
    switch (currentTab) {
      case 0: return (
        <Box>
          <Typography variant="h5" gutterBottom sx={{ mt: 3 }}>Habari za Trending</Typography>
          {renderNewsContent()}
          <Typography variant="h5" gutterBottom sx={{ mt: 4 }}>Video za Maarufu</Typography>
          {renderVideoContent()}
        </Box>
      );
      case 1: return renderNewsContent();
      case 2: return renderVideoContent();
      case 3: return renderChannelsContent();
      default: return renderNewsContent();
    }
  };

  return (
    <Box sx={{ py: 4 }}>
      <Container maxWidth="lg">
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Typography variant="h3" component="h1" gutterBottom>
            Gundua Maudhui
          </Typography>
          <Typography variant="h6" color="text.secondary" sx={{ mb: 3 }}>
            Pata habari, video, na maudhui mengine ya kuvutia kutoka ProChat
          </Typography>
          
          {/* Search */}
          <Box sx={{ maxWidth: 600, mx: 'auto', mb: 3 }}>
            <TextField
              fullWidth
              placeholder="Tafuta habari, video, au vituo..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 3,
                },
              }}
            />
          </Box>
        </Box>

        {/* Content Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs
            value={currentTab}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            centered
          >
            {tabs.map((tab, index) => (
              <Tab
                key={index}
                label={tab.label}
                icon={tab.icon}
                iconPosition="start"
              />
            ))}
          </Tabs>
        </Box>

        {/* Content */}
        {renderContent()}

        {/* Load More Button */}
        <Box sx={{ textAlign: 'center', mt: 4 }}>
          <Button variant="outlined" size="large">
            Pakia Zaidi
          </Button>
        </Box>
      </Container>
    </Box>
  );
}
