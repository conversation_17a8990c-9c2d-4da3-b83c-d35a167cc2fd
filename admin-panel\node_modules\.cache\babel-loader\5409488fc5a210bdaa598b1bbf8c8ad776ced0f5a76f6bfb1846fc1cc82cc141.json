{"ast": null, "code": "import { isVisible } from '../util/TickUtils';\nimport { getEveryNthWithCondition } from '../util/getEveryNthWithCondition';\nexport function getEquidistantTicks(sign, boundaries, getTickSize, ticks, minTickGap) {\n  var result = (ticks || []).slice();\n  var initialStart = boundaries.start,\n    end = boundaries.end;\n  var index = 0;\n  // Premature optimisation idea 1: Estimate a lower bound, and start from there.\n  // For now, start from every tick\n  var stepsize = 1;\n  var start = initialStart;\n  var _loop = function _loop() {\n      // Given stepsize, evaluate whether every stepsize-th tick can be shown.\n      // If it can not, then increase the stepsize by 1, and try again.\n\n      var entry = ticks === null || ticks === void 0 ? void 0 : ticks[index];\n\n      // Break condition - If we have evaluate all the ticks, then we are done.\n      if (entry === undefined) {\n        return {\n          v: getEveryNthWithCondition(ticks, stepsize)\n        };\n      }\n\n      // Check if the element collides with the next element\n      var i = index;\n      var size;\n      var getSize = function getSize() {\n        if (size === undefined) {\n          size = getTickSize(entry, i);\n        }\n        return size;\n      };\n      var tickCoord = entry.coordinate;\n      // We will always show the first tick.\n      var isShow = index === 0 || isVisible(sign, tickCoord, getSize, start, end);\n      if (!isShow) {\n        // Start all over with a larger stepsize\n        index = 0;\n        start = initialStart;\n        stepsize += 1;\n      }\n      if (isShow) {\n        // If it can be shown, update the start\n        start = tickCoord + sign * (getSize() / 2 + minTickGap);\n        index += stepsize;\n      }\n    },\n    _ret;\n  while (stepsize <= result.length) {\n    _ret = _loop();\n    if (_ret) return _ret.v;\n  }\n  return [];\n}", "map": {"version": 3, "names": ["isVisible", "getEveryNthWithCondition", "getEquidistantTicks", "sign", "boundaries", "getTickSize", "ticks", "minTickGap", "result", "slice", "initialStart", "start", "end", "index", "stepsize", "_loop", "entry", "undefined", "v", "i", "size", "getSize", "tickCoord", "coordinate", "isShow", "_ret", "length"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/recharts/es6/cartesian/getEquidistantTicks.js"], "sourcesContent": ["import { isVisible } from '../util/TickUtils';\nimport { getEveryNthWithCondition } from '../util/getEveryNthWithCondition';\nexport function getEquidistantTicks(sign, boundaries, getTickSize, ticks, minTickGap) {\n  var result = (ticks || []).slice();\n  var initialStart = boundaries.start,\n    end = boundaries.end;\n  var index = 0;\n  // Premature optimisation idea 1: Estimate a lower bound, and start from there.\n  // For now, start from every tick\n  var stepsize = 1;\n  var start = initialStart;\n  var _loop = function _loop() {\n      // Given stepsize, evaluate whether every stepsize-th tick can be shown.\n      // If it can not, then increase the stepsize by 1, and try again.\n\n      var entry = ticks === null || ticks === void 0 ? void 0 : ticks[index];\n\n      // Break condition - If we have evaluate all the ticks, then we are done.\n      if (entry === undefined) {\n        return {\n          v: getEveryNthWithCondition(ticks, stepsize)\n        };\n      }\n\n      // Check if the element collides with the next element\n      var i = index;\n      var size;\n      var getSize = function getSize() {\n        if (size === undefined) {\n          size = getTickSize(entry, i);\n        }\n        return size;\n      };\n      var tickCoord = entry.coordinate;\n      // We will always show the first tick.\n      var isShow = index === 0 || isVisible(sign, tickCoord, getSize, start, end);\n      if (!isShow) {\n        // Start all over with a larger stepsize\n        index = 0;\n        start = initialStart;\n        stepsize += 1;\n      }\n      if (isShow) {\n        // If it can be shown, update the start\n        start = tickCoord + sign * (getSize() / 2 + minTickGap);\n        index += stepsize;\n      }\n    },\n    _ret;\n  while (stepsize <= result.length) {\n    _ret = _loop();\n    if (_ret) return _ret.v;\n  }\n  return [];\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,wBAAwB,QAAQ,kCAAkC;AAC3E,OAAO,SAASC,mBAAmBA,CAACC,IAAI,EAAEC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAEC,UAAU,EAAE;EACpF,IAAIC,MAAM,GAAG,CAACF,KAAK,IAAI,EAAE,EAAEG,KAAK,CAAC,CAAC;EAClC,IAAIC,YAAY,GAAGN,UAAU,CAACO,KAAK;IACjCC,GAAG,GAAGR,UAAU,CAACQ,GAAG;EACtB,IAAIC,KAAK,GAAG,CAAC;EACb;EACA;EACA,IAAIC,QAAQ,GAAG,CAAC;EAChB,IAAIH,KAAK,GAAGD,YAAY;EACxB,IAAIK,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;MACzB;MACA;;MAEA,IAAIC,KAAK,GAAGV,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACO,KAAK,CAAC;;MAEtE;MACA,IAAIG,KAAK,KAAKC,SAAS,EAAE;QACvB,OAAO;UACLC,CAAC,EAAEjB,wBAAwB,CAACK,KAAK,EAAEQ,QAAQ;QAC7C,CAAC;MACH;;MAEA;MACA,IAAIK,CAAC,GAAGN,KAAK;MACb,IAAIO,IAAI;MACR,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;QAC/B,IAAID,IAAI,KAAKH,SAAS,EAAE;UACtBG,IAAI,GAAGf,WAAW,CAACW,KAAK,EAAEG,CAAC,CAAC;QAC9B;QACA,OAAOC,IAAI;MACb,CAAC;MACD,IAAIE,SAAS,GAAGN,KAAK,CAACO,UAAU;MAChC;MACA,IAAIC,MAAM,GAAGX,KAAK,KAAK,CAAC,IAAIb,SAAS,CAACG,IAAI,EAAEmB,SAAS,EAAED,OAAO,EAAEV,KAAK,EAAEC,GAAG,CAAC;MAC3E,IAAI,CAACY,MAAM,EAAE;QACX;QACAX,KAAK,GAAG,CAAC;QACTF,KAAK,GAAGD,YAAY;QACpBI,QAAQ,IAAI,CAAC;MACf;MACA,IAAIU,MAAM,EAAE;QACV;QACAb,KAAK,GAAGW,SAAS,GAAGnB,IAAI,IAAIkB,OAAO,CAAC,CAAC,GAAG,CAAC,GAAGd,UAAU,CAAC;QACvDM,KAAK,IAAIC,QAAQ;MACnB;IACF,CAAC;IACDW,IAAI;EACN,OAAOX,QAAQ,IAAIN,MAAM,CAACkB,MAAM,EAAE;IAChCD,IAAI,GAAGV,KAAK,CAAC,CAAC;IACd,IAAIU,IAAI,EAAE,OAAOA,IAAI,CAACP,CAAC;EACzB;EACA,OAAO,EAAE;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}