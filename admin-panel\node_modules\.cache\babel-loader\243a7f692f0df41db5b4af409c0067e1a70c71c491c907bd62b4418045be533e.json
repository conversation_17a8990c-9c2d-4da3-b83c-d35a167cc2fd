{"ast": null, "code": "const e10 = Math.sqrt(50),\n  e5 = Math.sqrt(10),\n  e2 = Math.sqrt(2);\nfunction tickSpec(start, stop, count) {\n  const step = (stop - start) / Math.max(0, count),\n    power = Math.floor(Math.log10(step)),\n    error = step / Math.pow(10, power),\n    factor = error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1;\n  let i1, i2, inc;\n  if (power < 0) {\n    inc = Math.pow(10, -power) / factor;\n    i1 = Math.round(start * inc);\n    i2 = Math.round(stop * inc);\n    if (i1 / inc < start) ++i1;\n    if (i2 / inc > stop) --i2;\n    inc = -inc;\n  } else {\n    inc = Math.pow(10, power) * factor;\n    i1 = Math.round(start / inc);\n    i2 = Math.round(stop / inc);\n    if (i1 * inc < start) ++i1;\n    if (i2 * inc > stop) --i2;\n  }\n  if (i2 < i1 && 0.5 <= count && count < 2) return tickSpec(start, stop, count * 2);\n  return [i1, i2, inc];\n}\nexport default function ticks(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  if (!(count > 0)) return [];\n  if (start === stop) return [start];\n  const reverse = stop < start,\n    [i1, i2, inc] = reverse ? tickSpec(stop, start, count) : tickSpec(start, stop, count);\n  if (!(i2 >= i1)) return [];\n  const n = i2 - i1 + 1,\n    ticks = new Array(n);\n  if (reverse) {\n    if (inc < 0) for (let i = 0; i < n; ++i) ticks[i] = (i2 - i) / -inc;else for (let i = 0; i < n; ++i) ticks[i] = (i2 - i) * inc;\n  } else {\n    if (inc < 0) for (let i = 0; i < n; ++i) ticks[i] = (i1 + i) / -inc;else for (let i = 0; i < n; ++i) ticks[i] = (i1 + i) * inc;\n  }\n  return ticks;\n}\nexport function tickIncrement(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  return tickSpec(start, stop, count)[2];\n}\nexport function tickStep(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  const reverse = stop < start,\n    inc = reverse ? tickIncrement(stop, start, count) : tickIncrement(start, stop, count);\n  return (reverse ? -1 : 1) * (inc < 0 ? 1 / -inc : inc);\n}", "map": {"version": 3, "names": ["e10", "Math", "sqrt", "e5", "e2", "tickSpec", "start", "stop", "count", "step", "max", "power", "floor", "log10", "error", "pow", "factor", "i1", "i2", "inc", "round", "ticks", "reverse", "n", "Array", "i", "tickIncrement", "tickStep"], "sources": ["E:/RamsTech/App/ProChat/admin-panel/node_modules/d3-array/src/ticks.js"], "sourcesContent": ["const e10 = Math.sqrt(50),\n    e5 = Math.sqrt(10),\n    e2 = Math.sqrt(2);\n\nfunction tickSpec(start, stop, count) {\n  const step = (stop - start) / Math.max(0, count),\n      power = Math.floor(Math.log10(step)),\n      error = step / Math.pow(10, power),\n      factor = error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1;\n  let i1, i2, inc;\n  if (power < 0) {\n    inc = Math.pow(10, -power) / factor;\n    i1 = Math.round(start * inc);\n    i2 = Math.round(stop * inc);\n    if (i1 / inc < start) ++i1;\n    if (i2 / inc > stop) --i2;\n    inc = -inc;\n  } else {\n    inc = Math.pow(10, power) * factor;\n    i1 = Math.round(start / inc);\n    i2 = Math.round(stop / inc);\n    if (i1 * inc < start) ++i1;\n    if (i2 * inc > stop) --i2;\n  }\n  if (i2 < i1 && 0.5 <= count && count < 2) return tickSpec(start, stop, count * 2);\n  return [i1, i2, inc];\n}\n\nexport default function ticks(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  if (!(count > 0)) return [];\n  if (start === stop) return [start];\n  const reverse = stop < start, [i1, i2, inc] = reverse ? tickSpec(stop, start, count) : tickSpec(start, stop, count);\n  if (!(i2 >= i1)) return [];\n  const n = i2 - i1 + 1, ticks = new Array(n);\n  if (reverse) {\n    if (inc < 0) for (let i = 0; i < n; ++i) ticks[i] = (i2 - i) / -inc;\n    else for (let i = 0; i < n; ++i) ticks[i] = (i2 - i) * inc;\n  } else {\n    if (inc < 0) for (let i = 0; i < n; ++i) ticks[i] = (i1 + i) / -inc;\n    else for (let i = 0; i < n; ++i) ticks[i] = (i1 + i) * inc;\n  }\n  return ticks;\n}\n\nexport function tickIncrement(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  return tickSpec(start, stop, count)[2];\n}\n\nexport function tickStep(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  const reverse = stop < start, inc = reverse ? tickIncrement(stop, start, count) : tickIncrement(start, stop, count);\n  return (reverse ? -1 : 1) * (inc < 0 ? 1 / -inc : inc);\n}\n"], "mappings": "AAAA,MAAMA,GAAG,GAAGC,IAAI,CAACC,IAAI,CAAC,EAAE,CAAC;EACrBC,EAAE,GAAGF,IAAI,CAACC,IAAI,CAAC,EAAE,CAAC;EAClBE,EAAE,GAAGH,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;AAErB,SAASG,QAAQA,CAACC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAE;EACpC,MAAMC,IAAI,GAAG,CAACF,IAAI,GAAGD,KAAK,IAAIL,IAAI,CAACS,GAAG,CAAC,CAAC,EAAEF,KAAK,CAAC;IAC5CG,KAAK,GAAGV,IAAI,CAACW,KAAK,CAACX,IAAI,CAACY,KAAK,CAACJ,IAAI,CAAC,CAAC;IACpCK,KAAK,GAAGL,IAAI,GAAGR,IAAI,CAACc,GAAG,CAAC,EAAE,EAAEJ,KAAK,CAAC;IAClCK,MAAM,GAAGF,KAAK,IAAId,GAAG,GAAG,EAAE,GAAGc,KAAK,IAAIX,EAAE,GAAG,CAAC,GAAGW,KAAK,IAAIV,EAAE,GAAG,CAAC,GAAG,CAAC;EACtE,IAAIa,EAAE,EAAEC,EAAE,EAAEC,GAAG;EACf,IAAIR,KAAK,GAAG,CAAC,EAAE;IACbQ,GAAG,GAAGlB,IAAI,CAACc,GAAG,CAAC,EAAE,EAAE,CAACJ,KAAK,CAAC,GAAGK,MAAM;IACnCC,EAAE,GAAGhB,IAAI,CAACmB,KAAK,CAACd,KAAK,GAAGa,GAAG,CAAC;IAC5BD,EAAE,GAAGjB,IAAI,CAACmB,KAAK,CAACb,IAAI,GAAGY,GAAG,CAAC;IAC3B,IAAIF,EAAE,GAAGE,GAAG,GAAGb,KAAK,EAAE,EAAEW,EAAE;IAC1B,IAAIC,EAAE,GAAGC,GAAG,GAAGZ,IAAI,EAAE,EAAEW,EAAE;IACzBC,GAAG,GAAG,CAACA,GAAG;EACZ,CAAC,MAAM;IACLA,GAAG,GAAGlB,IAAI,CAACc,GAAG,CAAC,EAAE,EAAEJ,KAAK,CAAC,GAAGK,MAAM;IAClCC,EAAE,GAAGhB,IAAI,CAACmB,KAAK,CAACd,KAAK,GAAGa,GAAG,CAAC;IAC5BD,EAAE,GAAGjB,IAAI,CAACmB,KAAK,CAACb,IAAI,GAAGY,GAAG,CAAC;IAC3B,IAAIF,EAAE,GAAGE,GAAG,GAAGb,KAAK,EAAE,EAAEW,EAAE;IAC1B,IAAIC,EAAE,GAAGC,GAAG,GAAGZ,IAAI,EAAE,EAAEW,EAAE;EAC3B;EACA,IAAIA,EAAE,GAAGD,EAAE,IAAI,GAAG,IAAIT,KAAK,IAAIA,KAAK,GAAG,CAAC,EAAE,OAAOH,QAAQ,CAACC,KAAK,EAAEC,IAAI,EAAEC,KAAK,GAAG,CAAC,CAAC;EACjF,OAAO,CAACS,EAAE,EAAEC,EAAE,EAAEC,GAAG,CAAC;AACtB;AAEA,eAAe,SAASE,KAAKA,CAACf,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAE;EAChDD,IAAI,GAAG,CAACA,IAAI,EAAED,KAAK,GAAG,CAACA,KAAK,EAAEE,KAAK,GAAG,CAACA,KAAK;EAC5C,IAAI,EAAEA,KAAK,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE;EAC3B,IAAIF,KAAK,KAAKC,IAAI,EAAE,OAAO,CAACD,KAAK,CAAC;EAClC,MAAMgB,OAAO,GAAGf,IAAI,GAAGD,KAAK;IAAE,CAACW,EAAE,EAAEC,EAAE,EAAEC,GAAG,CAAC,GAAGG,OAAO,GAAGjB,QAAQ,CAACE,IAAI,EAAED,KAAK,EAAEE,KAAK,CAAC,GAAGH,QAAQ,CAACC,KAAK,EAAEC,IAAI,EAAEC,KAAK,CAAC;EACnH,IAAI,EAAEU,EAAE,IAAID,EAAE,CAAC,EAAE,OAAO,EAAE;EAC1B,MAAMM,CAAC,GAAGL,EAAE,GAAGD,EAAE,GAAG,CAAC;IAAEI,KAAK,GAAG,IAAIG,KAAK,CAACD,CAAC,CAAC;EAC3C,IAAID,OAAO,EAAE;IACX,IAAIH,GAAG,GAAG,CAAC,EAAE,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAEJ,KAAK,CAACI,CAAC,CAAC,GAAG,CAACP,EAAE,GAAGO,CAAC,IAAI,CAACN,GAAG,CAAC,KAC/D,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAEJ,KAAK,CAACI,CAAC,CAAC,GAAG,CAACP,EAAE,GAAGO,CAAC,IAAIN,GAAG;EAC5D,CAAC,MAAM;IACL,IAAIA,GAAG,GAAG,CAAC,EAAE,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAEJ,KAAK,CAACI,CAAC,CAAC,GAAG,CAACR,EAAE,GAAGQ,CAAC,IAAI,CAACN,GAAG,CAAC,KAC/D,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAEJ,KAAK,CAACI,CAAC,CAAC,GAAG,CAACR,EAAE,GAAGQ,CAAC,IAAIN,GAAG;EAC5D;EACA,OAAOE,KAAK;AACd;AAEA,OAAO,SAASK,aAAaA,CAACpB,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAE;EAChDD,IAAI,GAAG,CAACA,IAAI,EAAED,KAAK,GAAG,CAACA,KAAK,EAAEE,KAAK,GAAG,CAACA,KAAK;EAC5C,OAAOH,QAAQ,CAACC,KAAK,EAAEC,IAAI,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC;AACxC;AAEA,OAAO,SAASmB,QAAQA,CAACrB,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAE;EAC3CD,IAAI,GAAG,CAACA,IAAI,EAAED,KAAK,GAAG,CAACA,KAAK,EAAEE,KAAK,GAAG,CAACA,KAAK;EAC5C,MAAMc,OAAO,GAAGf,IAAI,GAAGD,KAAK;IAAEa,GAAG,GAAGG,OAAO,GAAGI,aAAa,CAACnB,IAAI,EAAED,KAAK,EAAEE,KAAK,CAAC,GAAGkB,aAAa,CAACpB,KAAK,EAAEC,IAAI,EAAEC,KAAK,CAAC;EACnH,OAAO,CAACc,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,KAAKH,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAACA,GAAG,GAAGA,GAAG,CAAC;AACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}