package com.prochat.model;

public enum AdminPermission {
    // User Management
    VIEW_USERS,
    EDIT_USERS,
    DELETE_USERS,
    VERIFY_USERS,
    BLOCK_USERS,
    ASSIGN_ROLES,
    
    // Content Moderation
    VIEW_POSTS,
    <PERSON><PERSON><PERSON><PERSON><PERSON>_POSTS,
    DELETE_POSTS,
    <PERSON><PERSON>OVE_CONTENT,
    FLAG_CONTENT,
    <PERSON><PERSON><PERSON><PERSON>E_COMMENTS,
    <PERSON><PERSON><PERSON><PERSON>E_VIDEOS,
    M<PERSON><PERSON>ATE_NEWS,
    
    // Financial Management
    VIEW_TRANSACTIONS,
    APPROVE_WITHDRAWALS,
    APPROVE_DEPOSITS,
    MANAGE_COMMISSIONS,
    VIEW_FINANCIAL_REPORTS,
    CONFIGURE_FEES,
    RESOLVE_DISPUTES,
    
    // Event Management
    VIEW_EVENTS,
    APPROVE_EVENTS,
    REJECT_EVENTS,
    MANAGE_TICKETS,
    VALIDATE_QR_CODES,
    VIEW_EVENT_REPORTS,
    
    // Advertisement Management
    VIEW_ADS,
    APPROVE_ADS,
    REJECT_ADS,
    MANAGE_CAMPAIGNS,
    VIEW_AD_ANALYTICS,
    CONFIGURE_AD_RATES,
    
    // Job & Tender Management
    VIEW_JOBS,
    APPROVE_JOBS,
    REJECT_JOBS,
    MANAGE_APPLICATIONS,
    SCHEDULE_INTERVIEWS,
    RANK_APPLICANTS,
    
    // System Administration
    MANAGE_ADMINS,
    CONFIGURE_SYSTEM,
    VIEW_AUDIT_LOGS,
    MANAGE_BACKUPS,
    CONFIGURE_APIS,
    SEND_NOTIFICATIONS,
    MANAGE_SETTINGS,
    
    // Support & Help
    VIEW_TICKETS,
    RESPOND_TO_TICKETS,
    ESCALATE_TICKETS,
    MANAGE_REPORTS,
    BAN_USERS,
    
    // Analytics & Reporting
    VIEW_ANALYTICS,
    GENERATE_REPORTS,
    EXPORT_DATA,
    VIEW_STATISTICS,
    
    // Security
    MANAGE_SECURITY,
    VIEW_LOGIN_LOGS,
    CONFIGURE_2FA,
    MANAGE_IP_RESTRICTIONS,
    
    // Special Permissions
    SUPER_ADMIN_ACCESS,
    EMERGENCY_ACCESS,
    SYSTEM_MAINTENANCE
}
