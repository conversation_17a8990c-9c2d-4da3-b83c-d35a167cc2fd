import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  StatusBar,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Video } from 'expo-av';
import { colors, typography, spacing } from '../../theme/theme';
import { videosAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

const { width, height } = Dimensions.get('window');

export default function VideoPlayerScreen({ navigation, route }) {
  const { videoId, videoUrl, videoTitle } = route.params;
  const { user } = useAuth();
  const videoRef = useRef(null);
  const [video, setVideo] = useState(null);
  const [status, setStatus] = useState({});
  const [isPlaying, setIsPlaying] = useState(true);
  const [showControls, setShowControls] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (videoId) {
      loadVideoDetails();
    } else {
      setLoading(false);
    }
    
    // Hide status bar for better video experience
    StatusBar.setHidden(true);
    
    return () => {
      StatusBar.setHidden(false);
    };
  }, [videoId]);

  useEffect(() => {
    // Auto-hide controls after 3 seconds
    if (showControls) {
      const timer = setTimeout(() => {
        setShowControls(false);
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [showControls]);

  const loadVideoDetails = async () => {
    try {
      const response = await videosAPI.getVideoById(videoId);
      if (response.success) {
        setVideo(response.data);
      }
    } catch (error) {
      console.error('Error loading video:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePlayPause = async () => {
    if (videoRef.current) {
      if (isPlaying) {
        await videoRef.current.pauseAsync();
      } else {
        await videoRef.current.playAsync();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleSeek = async (position) => {
    if (videoRef.current && status.durationMillis) {
      const seekPosition = (position / 100) * status.durationMillis;
      await videoRef.current.setPositionAsync(seekPosition);
    }
  };

  const handleFullscreen = async () => {
    if (videoRef.current) {
      if (isFullscreen) {
        await videoRef.current.dismissFullscreenPlayer();
      } else {
        await videoRef.current.presentFullscreenPlayer();
      }
      setIsFullscreen(!isFullscreen);
    }
  };

  const formatTime = (milliseconds) => {
    if (!milliseconds) return '0:00';
    
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getProgressPercentage = () => {
    if (!status.durationMillis || !status.positionMillis) return 0;
    return (status.positionMillis / status.durationMillis) * 100;
  };

  const handleVideoPress = () => {
    setShowControls(!showControls);
  };

  const handleBack = () => {
    navigation.goBack();
  };

  const handleShare = () => {
    // Implement share functionality
    Alert.alert('Shiriki', 'Shiriki video hii');
  };

  const handleLike = async () => {
    if (!videoId) return;
    
    try {
      await videosAPI.toggleLike(videoId);
      // Update video state if needed
    } catch (error) {
      console.error('Error liking video:', error);
    }
  };

  return (
    <View style={styles.container}>
      {/* Video Player */}
      <TouchableOpacity 
        style={styles.videoContainer} 
        onPress={handleVideoPress}
        activeOpacity={1}
      >
        <Video
          ref={videoRef}
          style={styles.video}
          source={{ uri: videoUrl || video?.videoUrl }}
          shouldPlay={isPlaying}
          isLooping={false}
          resizeMode="contain"
          onPlaybackStatusUpdate={setStatus}
          onError={(error) => {
            console.error('Video error:', error);
            Alert.alert('Hitilafu', 'Imeshindwa kucheza video');
          }}
        />

        {/* Video Controls Overlay */}
        {showControls && (
          <View style={styles.controlsOverlay}>
            {/* Top Controls */}
            <View style={styles.topControls}>
              <TouchableOpacity style={styles.backButton} onPress={handleBack}>
                <Icon name="arrow-back" size={24} color={colors.white} />
              </TouchableOpacity>
              
              <View style={styles.topRightControls}>
                <TouchableOpacity style={styles.controlButton} onPress={handleShare}>
                  <Icon name="share" size={24} color={colors.white} />
                </TouchableOpacity>
                
                <TouchableOpacity style={styles.controlButton} onPress={handleFullscreen}>
                  <Icon 
                    name={isFullscreen ? "fullscreen-exit" : "fullscreen"} 
                    size={24} 
                    color={colors.white} 
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* Center Play/Pause Button */}
            <View style={styles.centerControls}>
              <TouchableOpacity style={styles.playPauseButton} onPress={handlePlayPause}>
                <Icon 
                  name={isPlaying ? "pause" : "play-arrow"} 
                  size={48} 
                  color={colors.white} 
                />
              </TouchableOpacity>
            </View>

            {/* Bottom Controls */}
            <View style={styles.bottomControls}>
              {/* Progress Bar */}
              <View style={styles.progressContainer}>
                <Text style={styles.timeText}>
                  {formatTime(status.positionMillis)}
                </Text>
                
                <View style={styles.progressBar}>
                  <View style={styles.progressTrack}>
                    <View 
                      style={[
                        styles.progressFill,
                        { width: `${getProgressPercentage()}%` }
                      ]} 
                    />
                  </View>
                </View>
                
                <Text style={styles.timeText}>
                  {formatTime(status.durationMillis)}
                </Text>
              </View>

              {/* Action Buttons */}
              <View style={styles.actionButtons}>
                <TouchableOpacity style={styles.actionButton} onPress={handleLike}>
                  <Icon name="favorite-border" size={20} color={colors.white} />
                </TouchableOpacity>
                
                <TouchableOpacity style={styles.actionButton}>
                  <Icon name="comment" size={20} color={colors.white} />
                </TouchableOpacity>
                
                <TouchableOpacity style={styles.actionButton}>
                  <Icon name="bookmark-border" size={20} color={colors.white} />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )}

        {/* Loading Indicator */}
        {loading && (
          <View style={styles.loadingOverlay}>
            <Text style={styles.loadingText}>Inapakia video...</Text>
          </View>
        )}
      </TouchableOpacity>

      {/* Video Info (if available) */}
      {(video || videoTitle) && (
        <View style={styles.videoInfo}>
          <Text style={styles.videoTitle}>
            {video?.title || videoTitle || 'Video'}
          </Text>
          
          {video && (
            <>
              <View style={styles.videoMeta}>
                <Text style={styles.videoAuthor}>{video.author}</Text>
                <Text style={styles.videoDuration}>
                  {formatTime(video.duration)}
                </Text>
              </View>
              
              <View style={styles.videoStats}>
                <Text style={styles.videoViews}>
                  {video.viewsCount} miwani
                </Text>
                <Text style={styles.videoDate}>
                  {new Date(video.createdAt).toLocaleDateString('sw-TZ')}
                </Text>
              </View>
              
              {video.description && (
                <Text style={styles.videoDescription} numberOfLines={3}>
                  {video.description}
                </Text>
              )}
            </>
          )}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
  },
  videoContainer: {
    flex: 1,
    position: 'relative',
  },
  video: {
    width: '100%',
    height: '100%',
  },
  controlsOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'space-between',
  },
  topControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.xl,
  },
  backButton: {
    padding: spacing.sm,
  },
  topRightControls: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  controlButton: {
    padding: spacing.sm,
  },
  centerControls: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  playPauseButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomControls: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.xl,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  timeText: {
    color: colors.white,
    fontSize: typography.fontSize.sm,
    minWidth: 40,
    textAlign: 'center',
  },
  progressBar: {
    flex: 1,
    marginHorizontal: spacing.md,
  },
  progressTrack: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 2,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: spacing.xl,
  },
  actionButton: {
    padding: spacing.sm,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: colors.white,
    fontSize: typography.fontSize.md,
  },
  videoInfo: {
    backgroundColor: colors.white,
    padding: spacing.lg,
  },
  videoTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  videoMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  videoAuthor: {
    fontSize: typography.fontSize.md,
    color: colors.primary,
    fontWeight: typography.fontWeight.medium,
  },
  videoDuration: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
  videoStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  videoViews: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
  videoDate: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
  videoDescription: {
    fontSize: typography.fontSize.sm,
    color: colors.text,
    lineHeight: 20,
  },
});
