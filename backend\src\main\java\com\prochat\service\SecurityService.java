package com.prochat.service;

import com.prochat.model.User;
import com.prochat.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class SecurityService {
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    // Rate limiting storage
    private final Map<String, RateLimitInfo> rateLimitMap = new ConcurrentHashMap<>();
    
    // Failed login attempts tracking
    private final Map<String, FailedLoginInfo> failedLoginMap = new ConcurrentHashMap<>();
    
    // OTP storage
    private final Map<String, OTPInfo> otpMap = new ConcurrentHashMap<>();
    
    private final SecureRandom secureRandom = new SecureRandom();
    
    /**
     * Rate limiting for API endpoints
     */
    public boolean isRateLimited(String identifier, int maxRequests, int timeWindowMinutes) {
        String key = identifier;
        LocalDateTime now = LocalDateTime.now();
        
        rateLimitMap.entrySet().removeIf(entry -> 
            entry.getValue().windowStart.plusMinutes(timeWindowMinutes).isBefore(now));
        
        RateLimitInfo info = rateLimitMap.get(key);
        if (info == null) {
            rateLimitMap.put(key, new RateLimitInfo(now, 1));
            return false;
        }
        
        if (info.windowStart.plusMinutes(timeWindowMinutes).isBefore(now)) {
            rateLimitMap.put(key, new RateLimitInfo(now, 1));
            return false;
        }
        
        if (info.requestCount >= maxRequests) {
            return true;
        }
        
        info.requestCount++;
        return false;
    }
    
    /**
     * Track failed login attempts and implement account lockout
     */
    public boolean isAccountLocked(String username) {
        FailedLoginInfo info = failedLoginMap.get(username);
        if (info == null) {
            return false;
        }
        
        // Unlock account after 30 minutes
        if (info.lockTime != null && info.lockTime.plusMinutes(30).isBefore(LocalDateTime.now())) {
            failedLoginMap.remove(username);
            return false;
        }
        
        return info.isLocked;
    }
    
    public void recordFailedLogin(String username) {
        FailedLoginInfo info = failedLoginMap.getOrDefault(username, new FailedLoginInfo());
        info.attempts++;
        info.lastAttempt = LocalDateTime.now();
        
        // Lock account after 5 failed attempts
        if (info.attempts >= 5) {
            info.isLocked = true;
            info.lockTime = LocalDateTime.now();
        }
        
        failedLoginMap.put(username, info);
    }
    
    public void recordSuccessfulLogin(String username) {
        failedLoginMap.remove(username);
    }
    
    /**
     * Generate secure OTP for 2FA
     */
    public String generateOTP(String identifier) {
        String otp = String.format("%06d", secureRandom.nextInt(1000000));
        otpMap.put(identifier, new OTPInfo(otp, LocalDateTime.now()));
        return otp;
    }
    
    /**
     * Verify OTP
     */
    public boolean verifyOTP(String identifier, String otp) {
        OTPInfo info = otpMap.get(identifier);
        if (info == null) {
            return false;
        }
        
        // OTP expires after 5 minutes
        if (info.createdAt.plusMinutes(5).isBefore(LocalDateTime.now())) {
            otpMap.remove(identifier);
            return false;
        }
        
        boolean isValid = info.otp.equals(otp);
        if (isValid) {
            otpMap.remove(identifier);
        }
        
        return isValid;
    }
    
    /**
     * Encrypt sensitive data
     */
    public String encryptData(String data, String key) {
        try {
            SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            byte[] encryptedData = cipher.doFinal(data.getBytes());
            return Base64.getEncoder().encodeToString(encryptedData);
        } catch (Exception e) {
            throw new RuntimeException("Encryption failed", e);
        }
    }
    
    /**
     * Decrypt sensitive data
     */
    public String decryptData(String encryptedData, String key) {
        try {
            SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] decodedData = Base64.getDecoder().decode(encryptedData);
            byte[] decryptedData = cipher.doFinal(decodedData);
            return new String(decryptedData);
        } catch (Exception e) {
            throw new RuntimeException("Decryption failed", e);
        }
    }
    
    /**
     * Validate password strength
     */
    public PasswordValidationResult validatePassword(String password) {
        PasswordValidationResult result = new PasswordValidationResult();
        
        if (password == null || password.length() < 6) {
            result.isValid = false;
            result.errors.add("Password must be at least 6 characters long");
        }
        
        if (password != null && password.length() > 128) {
            result.isValid = false;
            result.errors.add("Password must be less than 128 characters");
        }
        
        if (password != null && !password.matches(".*\\d.*")) {
            result.isValid = false;
            result.errors.add("Password must contain at least one number");
        }
        
        // Check against common passwords
        String[] commonPasswords = {
            "123456", "password", "123456789", "12345678", "12345",
            "1234567", "1234567890", "qwerty", "abc123", "password123"
        };
        
        if (password != null) {
            for (String common : commonPasswords) {
                if (password.toLowerCase().contains(common.toLowerCase())) {
                    result.isValid = false;
                    result.errors.add("Password is too common");
                    break;
                }
            }
        }
        
        return result;
    }
    
    /**
     * Generate secure session token
     */
    public String generateSecureToken() {
        byte[] token = new byte[32];
        secureRandom.nextBytes(token);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(token);
    }
    
    /**
     * Validate transaction PIN
     */
    public boolean validateTransactionPin(String pin) {
        if (pin == null || pin.length() != 4) {
            return false;
        }
        
        // Check if PIN is all same digits (e.g., 1111, 2222)
        if (pin.matches("(.)\\1{3}")) {
            return false;
        }
        
        // Check if PIN is sequential (e.g., 1234, 4321)
        if (pin.equals("1234") || pin.equals("4321") || pin.equals("0123")) {
            return false;
        }
        
        return pin.matches("\\d{4}");
    }
    
    /**
     * Log security events
     */
    public void logSecurityEvent(String userId, String eventType, String details, String ipAddress) {
        // In production, this would log to a security monitoring system
        System.out.println(String.format(
            "[SECURITY] User: %s, Event: %s, Details: %s, IP: %s, Time: %s",
            userId, eventType, details, ipAddress, LocalDateTime.now()
        ));
    }
    
    /**
     * Check for suspicious activity patterns
     */
    public boolean detectSuspiciousActivity(String userId, String activityType, String location) {
        // This would implement machine learning-based anomaly detection
        // For now, implement basic rules
        
        // Check for multiple login attempts from different locations
        // Check for unusual transaction patterns
        // Check for rapid account changes
        
        return false; // No suspicious activity detected
    }
    
    /**
     * Sanitize user input to prevent XSS and injection attacks
     */
    public String sanitizeInput(String input) {
        if (input == null) {
            return null;
        }
        
        return input
            .replaceAll("<script[^>]*>.*?</script>", "")
            .replaceAll("<[^>]+>", "")
            .replaceAll("javascript:", "")
            .replaceAll("vbscript:", "")
            .replaceAll("onload", "")
            .replaceAll("onerror", "")
            .replaceAll("onclick", "")
            .trim();
    }
    
    // Helper classes
    private static class RateLimitInfo {
        LocalDateTime windowStart;
        int requestCount;
        
        RateLimitInfo(LocalDateTime windowStart, int requestCount) {
            this.windowStart = windowStart;
            this.requestCount = requestCount;
        }
    }
    
    private static class FailedLoginInfo {
        int attempts = 0;
        LocalDateTime lastAttempt;
        boolean isLocked = false;
        LocalDateTime lockTime;
    }
    
    private static class OTPInfo {
        String otp;
        LocalDateTime createdAt;
        
        OTPInfo(String otp, LocalDateTime createdAt) {
            this.otp = otp;
            this.createdAt = createdAt;
        }
    }
    
    public static class PasswordValidationResult {
        boolean isValid = true;
        java.util.List<String> errors = new java.util.ArrayList<>();
    }
}
