package com.prochat.model;

import jakarta.persistence.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "meetings")
@EntityListeners(AuditingEntityListener.class)
public class Meeting {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "host_id", nullable = false)
    private User host;
    
    @Column(name = "meeting_id", unique = true, nullable = false)
    private String meetingId;
    
    @Column(name = "title", nullable = false)
    private String title;
    
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    @Column(name = "password")
    private String password;
    
    @Column(name = "join_url", nullable = false)
    private String joinUrl;
    
    @Column(name = "host_url")
    private String hostUrl;
    
    @Column(name = "scheduled_start")
    private LocalDateTime scheduledStart;
    
    @Column(name = "scheduled_end")
    private LocalDateTime scheduledEnd;
    
    @Column(name = "actual_start")
    private LocalDateTime actualStart;
    
    @Column(name = "actual_end")
    private LocalDateTime actualEnd;
    
    @Column(name = "duration_minutes")
    private Integer durationMinutes;
    
    @Column(name = "timezone")
    private String timezone = "Africa/Dar_es_Salaam";
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private MeetingStatus status = MeetingStatus.SCHEDULED;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "type")
    private MeetingType type = MeetingType.INSTANT;
    
    @Column(name = "max_participants")
    private Integer maxParticipants = 100;
    
    @Column(name = "current_participants")
    private Integer currentParticipants = 0;
    
    @Column(name = "total_participants")
    private Integer totalParticipants = 0;
    
    @Column(name = "is_recording_enabled")
    private Boolean isRecordingEnabled = false;
    
    @Column(name = "is_recording_active")
    private Boolean isRecordingActive = false;
    
    @Column(name = "recording_url")
    private String recordingUrl;
    
    @Column(name = "is_waiting_room_enabled")
    private Boolean isWaitingRoomEnabled = true;
    
    @Column(name = "is_mute_on_entry")
    private Boolean isMuteOnEntry = true;
    
    @Column(name = "is_video_on_entry")
    private Boolean isVideoOnEntry = false;
    
    @Column(name = "is_chat_enabled")
    private Boolean isChatEnabled = true;
    
    @Column(name = "is_screen_share_enabled")
    private Boolean isScreenShareEnabled = true;
    
    @Column(name = "is_translation_enabled")
    private Boolean isTranslationEnabled = false;
    
    @Column(name = "primary_language")
    private String primaryLanguage = "sw";
    
    @ElementCollection
    @CollectionTable(name = "meeting_languages", joinColumns = @JoinColumn(name = "meeting_id"))
    @Column(name = "language")
    private List<String> supportedLanguages;
    
    @Column(name = "agenda", columnDefinition = "TEXT")
    private String agenda;
    
    @Column(name = "meeting_notes", columnDefinition = "TEXT")
    private String meetingNotes;
    
    @Column(name = "is_public")
    private Boolean isPublic = false;
    
    @Column(name = "is_recurring")
    private Boolean isRecurring = false;
    
    @Column(name = "recurrence_pattern")
    private String recurrencePattern; // JSON string for recurrence rules
    
    @Column(name = "parent_meeting_id")
    private Long parentMeetingId; // For recurring meetings
    
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Constructors
    public Meeting() {}
    
    public Meeting(User host, String title, String meetingId, String joinUrl) {
        this.host = host;
        this.title = title;
        this.meetingId = meetingId;
        this.joinUrl = joinUrl;
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public User getHost() { return host; }
    public void setHost(User host) { this.host = host; }
    
    public String getMeetingId() { return meetingId; }
    public void setMeetingId(String meetingId) { this.meetingId = meetingId; }
    
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }
    
    public String getJoinUrl() { return joinUrl; }
    public void setJoinUrl(String joinUrl) { this.joinUrl = joinUrl; }
    
    public String getHostUrl() { return hostUrl; }
    public void setHostUrl(String hostUrl) { this.hostUrl = hostUrl; }
    
    public LocalDateTime getScheduledStart() { return scheduledStart; }
    public void setScheduledStart(LocalDateTime scheduledStart) { this.scheduledStart = scheduledStart; }
    
    public LocalDateTime getScheduledEnd() { return scheduledEnd; }
    public void setScheduledEnd(LocalDateTime scheduledEnd) { this.scheduledEnd = scheduledEnd; }
    
    public LocalDateTime getActualStart() { return actualStart; }
    public void setActualStart(LocalDateTime actualStart) { this.actualStart = actualStart; }
    
    public LocalDateTime getActualEnd() { return actualEnd; }
    public void setActualEnd(LocalDateTime actualEnd) { this.actualEnd = actualEnd; }
    
    public Integer getDurationMinutes() { return durationMinutes; }
    public void setDurationMinutes(Integer durationMinutes) { this.durationMinutes = durationMinutes; }
    
    public String getTimezone() { return timezone; }
    public void setTimezone(String timezone) { this.timezone = timezone; }
    
    public MeetingStatus getStatus() { return status; }
    public void setStatus(MeetingStatus status) { this.status = status; }
    
    public MeetingType getType() { return type; }
    public void setType(MeetingType type) { this.type = type; }
    
    public Integer getMaxParticipants() { return maxParticipants; }
    public void setMaxParticipants(Integer maxParticipants) { this.maxParticipants = maxParticipants; }
    
    public Integer getCurrentParticipants() { return currentParticipants; }
    public void setCurrentParticipants(Integer currentParticipants) { this.currentParticipants = currentParticipants; }
    
    public Integer getTotalParticipants() { return totalParticipants; }
    public void setTotalParticipants(Integer totalParticipants) { this.totalParticipants = totalParticipants; }
    
    public Boolean getIsRecordingEnabled() { return isRecordingEnabled; }
    public void setIsRecordingEnabled(Boolean isRecordingEnabled) { this.isRecordingEnabled = isRecordingEnabled; }
    
    public Boolean getIsRecordingActive() { return isRecordingActive; }
    public void setIsRecordingActive(Boolean isRecordingActive) { this.isRecordingActive = isRecordingActive; }
    
    public String getRecordingUrl() { return recordingUrl; }
    public void setRecordingUrl(String recordingUrl) { this.recordingUrl = recordingUrl; }
    
    public Boolean getIsWaitingRoomEnabled() { return isWaitingRoomEnabled; }
    public void setIsWaitingRoomEnabled(Boolean isWaitingRoomEnabled) { this.isWaitingRoomEnabled = isWaitingRoomEnabled; }
    
    public Boolean getIsMuteOnEntry() { return isMuteOnEntry; }
    public void setIsMuteOnEntry(Boolean isMuteOnEntry) { this.isMuteOnEntry = isMuteOnEntry; }
    
    public Boolean getIsVideoOnEntry() { return isVideoOnEntry; }
    public void setIsVideoOnEntry(Boolean isVideoOnEntry) { this.isVideoOnEntry = isVideoOnEntry; }
    
    public Boolean getIsChatEnabled() { return isChatEnabled; }
    public void setIsChatEnabled(Boolean isChatEnabled) { this.isChatEnabled = isChatEnabled; }
    
    public Boolean getIsScreenShareEnabled() { return isScreenShareEnabled; }
    public void setIsScreenShareEnabled(Boolean isScreenShareEnabled) { this.isScreenShareEnabled = isScreenShareEnabled; }
    
    public Boolean getIsTranslationEnabled() { return isTranslationEnabled; }
    public void setIsTranslationEnabled(Boolean isTranslationEnabled) { this.isTranslationEnabled = isTranslationEnabled; }
    
    public String getPrimaryLanguage() { return primaryLanguage; }
    public void setPrimaryLanguage(String primaryLanguage) { this.primaryLanguage = primaryLanguage; }
    
    public List<String> getSupportedLanguages() { return supportedLanguages; }
    public void setSupportedLanguages(List<String> supportedLanguages) { this.supportedLanguages = supportedLanguages; }
    
    public String getAgenda() { return agenda; }
    public void setAgenda(String agenda) { this.agenda = agenda; }
    
    public String getMeetingNotes() { return meetingNotes; }
    public void setMeetingNotes(String meetingNotes) { this.meetingNotes = meetingNotes; }
    
    public Boolean getIsPublic() { return isPublic; }
    public void setIsPublic(Boolean isPublic) { this.isPublic = isPublic; }
    
    public Boolean getIsRecurring() { return isRecurring; }
    public void setIsRecurring(Boolean isRecurring) { this.isRecurring = isRecurring; }
    
    public String getRecurrencePattern() { return recurrencePattern; }
    public void setRecurrencePattern(String recurrencePattern) { this.recurrencePattern = recurrencePattern; }
    
    public Long getParentMeetingId() { return parentMeetingId; }
    public void setParentMeetingId(Long parentMeetingId) { this.parentMeetingId = parentMeetingId; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
