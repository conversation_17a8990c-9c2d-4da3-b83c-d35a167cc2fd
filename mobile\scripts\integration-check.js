#!/usr/bin/env node

/**
 * ProChat Frontend-Backend Integration Check
 * This script verifies that all API endpoints used in the mobile app
 * have corresponding backend implementations.
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 ProChat Integration Check Starting...\n');

// API endpoints used in mobile app
const mobileAPIEndpoints = {
  // Authentication endpoints
  auth: [
    'POST /auth/signin',
    'POST /auth/signup',
    'POST /auth/logout',
    'POST /auth/refresh',
    'POST /auth/forgot-password',
    'POST /auth/reset-password',
    'POST /auth/verify-email',
    'POST /auth/change-password'
  ],

  // User endpoints
  users: [
    'GET /users/{id}',
    'PUT /users/{id}',
    'GET /users/search',
    'GET /users/profile',
    'PUT /users/profile',
    'POST /users/follow',
    'DELETE /users/unfollow',
    'GET /users/followers',
    'GET /users/following'
  ],

  // Chat endpoints
  chats: [
    'GET /chats',
    'POST /chats',
    'GET /chats/{id}/messages',
    'POST /chats/{id}/messages',
    'PUT /chats/{id}/messages/{messageId}',
    'DELETE /chats/{id}/messages/{messageId}',
    'POST /chats/{id}/typing',
    'POST /chats/{id}/read'
  ],

  // Post endpoints
  posts: [
    'GET /posts/feed',
    'POST /posts',
    'GET /posts/{id}',
    'PUT /posts/{id}',
    'DELETE /posts/{id}',
    'POST /posts/{id}/like',
    'DELETE /posts/{id}/like',
    'GET /posts/{id}/comments',
    'POST /posts/{id}/comments',
    'GET /posts/user/{userId}'
  ],

  // Wallet endpoints
  wallet: [
    'GET /wallet',
    'GET /wallet/balance',
    'GET /wallet/transactions',
    'POST /wallet/send',
    'POST /wallet/request',
    'POST /wallet/deposit',
    'POST /wallet/withdraw',
    'POST /wallet/calculate-vat',
    'GET /wallet/agents',
    'POST /wallet/agent/register'
  ],

  // Events endpoints
  events: [
    'GET /events',
    'GET /events/{id}',
    'POST /events',
    'PUT /events/{id}',
    'DELETE /events/{id}',
    'POST /events/{id}/attend',
    'DELETE /events/{id}/attend',
    'GET /events/{id}/attendees',
    'GET /events/categories',
    'GET /events/trending'
  ],

  // Jobs endpoints
  jobs: [
    'GET /jobs',
    'GET /jobs/{id}',
    'POST /jobs',
    'PUT /jobs/{id}',
    'DELETE /jobs/{id}',
    'POST /jobs/apply',
    'GET /jobs/applications',
    'GET /jobs/saved',
    'POST /jobs/{id}/save',
    'GET /jobs/categories',
    'GET /jobs/trending'
  ],

  // Tickets endpoints
  tickets: [
    'GET /tickets/event/{eventId}',
    'POST /tickets/purchase',
    'GET /tickets/my-tickets',
    'GET /tickets/purchase/{purchaseId}',
    'POST /tickets/validate/{ticketNumber}',
    'GET /tickets/qr-code/{ticketNumber}',
    'POST /tickets/transfer',
    'POST /tickets/refund/{ticketNumber}'
  ],

  // Tasks endpoints
  tasks: [
    'GET /tasks/available',
    'GET /tasks/user',
    'POST /tasks/{id}/start',
    'POST /tasks/user/{userTaskId}/complete',
    'GET /tasks/user/{userTaskId}/progress',
    'GET /tasks/rewards',
    'POST /tasks/rewards/claim'
  ],

  // Donations endpoints
  donations: [
    'GET /donations/campaigns',
    'GET /donations/campaigns/{id}',
    'POST /donations/campaigns',
    'POST /donations/donate',
    'GET /donations/history',
    'GET /donations/my-campaigns',
    'PUT /donations/campaigns/{id}',
    'DELETE /donations/campaigns/{id}'
  ],

  // Live Stream endpoints
  streams: [
    'GET /streams/active',
    'POST /streams/start',
    'POST /streams/{id}/stop',
    'GET /streams/{id}',
    'POST /streams/{id}/join',
    'POST /streams/{id}/leave',
    'POST /streams/{id}/comment',
    'POST /streams/{id}/gift',
    'GET /streams/{id}/viewers'
  ],

  // News endpoints
  news: [
    'GET /news',
    'GET /news/{id}',
    'POST /news',
    'PUT /news/{id}',
    'DELETE /news/{id}',
    'POST /news/{id}/bookmark',
    'GET /news/bookmarks',
    'GET /news/categories',
    'GET /news/trending',
    'GET /news/search'
  ],

  // Gifts endpoints
  gifts: [
    'GET /gifts',
    'POST /gifts/send',
    'GET /gifts/history',
    'GET /gifts/received',
    'GET /gifts/types',
    'POST /gifts/purchase'
  ],

  // Notifications endpoints
  notifications: [
    'GET /notifications',
    'PUT /notifications/{id}/read',
    'PUT /notifications/read-all',
    'DELETE /notifications/{id}',
    'POST /notifications/settings',
    'GET /notifications/settings'
  ]
};

// Backend controllers that should exist
const expectedBackendControllers = [
  'AuthController.java',
  'UserController.java',
  'ChatController.java',
  'PostController.java',
  'WalletController.java',
  'EventController.java',
  'JobController.java',
  'TicketController.java',
  'TaskController.java',
  'DonationController.java',
  'LiveStreamController.java',
  'NewsController.java',
  'GiftController.java',
  'NotificationController.java'
];

// Check if backend controllers exist
function checkBackendControllers() {
  console.log('📋 Checking Backend Controllers...\n');
  
  const backendPath = '../backend/src/main/java/com/prochat/controller';
  let allControllersExist = true;
  
  expectedBackendControllers.forEach(controller => {
    const controllerPath = path.join(__dirname, backendPath, controller);
    if (fs.existsSync(controllerPath)) {
      console.log(`✅ ${controller} - EXISTS`);
    } else {
      console.log(`❌ ${controller} - MISSING`);
      allControllersExist = false;
    }
  });
  
  console.log(`\n📊 Backend Controllers: ${allControllersExist ? 'ALL PRESENT' : 'SOME MISSING'}\n`);
  return allControllersExist;
}

// Check API service implementations
function checkAPIServices() {
  console.log('🔌 Checking API Service Implementations...\n');
  
  const apiServicePath = path.join(__dirname, '../src/services/api.js');
  
  if (!fs.existsSync(apiServicePath)) {
    console.log('❌ API service file not found!');
    return false;
  }
  
  const apiContent = fs.readFileSync(apiServicePath, 'utf8');
  
  // Check if main API objects are exported
  const expectedAPIObjects = [
    'authAPI',
    'userAPI',
    'chatAPI',
    'postAPI',
    'walletAPI',
    'eventsAPI',
    'jobsAPI',
    'tasksAPI',
    'donationsAPI',
    'liveStreamAPI',
    'giftsAPI'
  ];
  
  let allAPIsImplemented = true;
  
  expectedAPIObjects.forEach(apiObject => {
    if (apiContent.includes(`export const ${apiObject}`)) {
      console.log(`✅ ${apiObject} - IMPLEMENTED`);
    } else {
      console.log(`❌ ${apiObject} - MISSING`);
      allAPIsImplemented = false;
    }
  });
  
  console.log(`\n📊 API Services: ${allAPIsImplemented ? 'ALL IMPLEMENTED' : 'SOME MISSING'}\n`);
  return allAPIsImplemented;
}

// Check environment configuration
function checkEnvironmentConfig() {
  console.log('⚙️ Checking Environment Configuration...\n');
  
  const envFiles = [
    '../.env.example',
    '../.env.production'
  ];
  
  let allEnvFilesExist = true;
  
  envFiles.forEach(envFile => {
    const envPath = path.join(__dirname, envFile);
    if (fs.existsSync(envPath)) {
      console.log(`✅ ${envFile} - EXISTS`);
    } else {
      console.log(`❌ ${envFile} - MISSING`);
      allEnvFilesExist = false;
    }
  });
  
  console.log(`\n📊 Environment Config: ${allEnvFilesExist ? 'ALL PRESENT' : 'SOME MISSING'}\n`);
  return allEnvFilesExist;
}

// Check build configuration
function checkBuildConfig() {
  console.log('🔨 Checking Build Configuration...\n');
  
  const buildFiles = [
    '../app.json',
    '../eas.json',
    '../package.json'
  ];
  
  let allBuildFilesExist = true;
  
  buildFiles.forEach(buildFile => {
    const buildPath = path.join(__dirname, buildFile);
    if (fs.existsSync(buildPath)) {
      console.log(`✅ ${buildFile} - EXISTS`);
    } else {
      console.log(`❌ ${buildFile} - MISSING`);
      allBuildFilesExist = false;
    }
  });
  
  console.log(`\n📊 Build Config: ${allBuildFilesExist ? 'ALL PRESENT' : 'SOME MISSING'}\n`);
  return allBuildFilesExist;
}

// Generate integration report
function generateReport(results) {
  console.log('📋 INTEGRATION CHECK REPORT\n');
  console.log('=' .repeat(50));
  
  const overallStatus = Object.values(results).every(result => result);
  
  console.log(`\n🎯 OVERALL STATUS: ${overallStatus ? '✅ READY FOR DEPLOYMENT' : '❌ NEEDS ATTENTION'}\n`);
  
  console.log('📊 Component Status:');
  console.log(`   Backend Controllers: ${results.controllers ? '✅' : '❌'}`);
  console.log(`   API Services: ${results.apiServices ? '✅' : '❌'}`);
  console.log(`   Environment Config: ${results.envConfig ? '✅' : '❌'}`);
  console.log(`   Build Configuration: ${results.buildConfig ? '✅' : '❌'}`);
  
  if (overallStatus) {
    console.log('\n🚀 ProChat is ready for deployment!');
    console.log('\nNext steps:');
    console.log('1. Run build scripts: ./scripts/build-android.sh all');
    console.log('2. Test on devices');
    console.log('3. Submit to app stores');
  } else {
    console.log('\n⚠️  Please fix the missing components before deployment.');
  }
  
  console.log('\n📞 Support: <EMAIL>');
  console.log('📚 Documentation: /docs/');
}

// Main execution
async function main() {
  try {
    const results = {
      controllers: checkBackendControllers(),
      apiServices: checkAPIServices(),
      envConfig: checkEnvironmentConfig(),
      buildConfig: checkBuildConfig()
    };
    
    generateReport(results);
    
    // Exit with appropriate code
    const success = Object.values(results).every(result => result);
    process.exit(success ? 0 : 1);
    
  } catch (error) {
    console.error('❌ Integration check failed:', error.message);
    process.exit(1);
  }
}

// Run the check
main();
