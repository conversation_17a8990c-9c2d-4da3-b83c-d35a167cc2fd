#!/bin/bash

# ProChat Stop Script
# This script stops all ProChat services

echo "🛑 Stopping ProChat services..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Stop backend
if [ -f ".backend.pid" ]; then
    BACKEND_PID=$(cat .backend.pid)
    if ps -p $BACKEND_PID > /dev/null; then
        kill $BACKEND_PID
        print_status "Backend stopped (PID: $BACKEND_PID)"
    else
        print_warning "Backend process not found"
    fi
    rm .backend.pid
else
    # Try to find and kill Spring Boot process
    SPRING_PID=$(ps aux | grep 'spring-boot:run' | grep -v grep | awk '{print $2}')
    if [ ! -z "$SPRING_PID" ]; then
        kill $SPRING_PID
        print_status "Spring Boot process stopped (PID: $SPRING_PID)"
    fi
fi

# Stop web admin
if [ -f ".webadmin.pid" ]; then
    WEB_ADMIN_PID=$(cat .webadmin.pid)
    if ps -p $WEB_ADMIN_PID > /dev/null; then
        kill $WEB_ADMIN_PID
        print_status "Web admin stopped (PID: $WEB_ADMIN_PID)"
    else
        print_warning "Web admin process not found"
    fi
    rm .webadmin.pid
else
    # Try to find and kill React process on port 3000
    REACT_PID=$(lsof -ti:3000)
    if [ ! -z "$REACT_PID" ]; then
        kill $REACT_PID
        print_status "React process stopped (PID: $REACT_PID)"
    fi
fi

# Kill any remaining processes on our ports
print_status "Checking for remaining processes..."

# Port 8080 (Backend)
BACKEND_PORT_PID=$(lsof -ti:8080)
if [ ! -z "$BACKEND_PORT_PID" ]; then
    kill $BACKEND_PORT_PID
    print_status "Process on port 8080 stopped"
fi

# Port 3000 (Web Admin)
WEB_PORT_PID=$(lsof -ti:3000)
if [ ! -z "$WEB_PORT_PID" ]; then
    kill $WEB_PORT_PID
    print_status "Process on port 3000 stopped"
fi

print_status "All ProChat services stopped! 🎉"
