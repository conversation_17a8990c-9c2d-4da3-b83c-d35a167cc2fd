import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
  Image,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors, typography, spacing } from '../../theme/theme';
import { donationsAPI, walletAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

export default function DonationScreen({ navigation, route }) {
  const { campaignId } = route.params || {};
  const { user } = useAuth();
  const [campaign, setCampaign] = useState(null);
  const [amount, setAmount] = useState('');
  const [message, setMessage] = useState('');
  const [isAnonymous, setIsAnonymous] = useState(false);
  const [loading, setLoading] = useState(true);
  const [donating, setDonating] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [vatCalculation, setVatCalculation] = useState(null);

  useEffect(() => {
    if (campaignId) {
      loadCampaign();
    }
  }, [campaignId]);

  useEffect(() => {
    if (amount && parseFloat(amount) >= 1000) {
      calculateVAT();
    } else {
      setVatCalculation(null);
    }
  }, [amount]);

  const loadCampaign = async () => {
    try {
      const response = await donationsAPI.getCampaignById(campaignId);
      if (response.success) {
        setCampaign(response.data);
      }
    } catch (error) {
      console.error('Error loading campaign:', error);
      Alert.alert('Hitilafu', 'Imeshindwa kupakia taarifa za kampeni');
    } finally {
      setLoading(false);
    }
  };

  const calculateVAT = async () => {
    try {
      const response = await walletAPI.calculateVAT(
        parseFloat(amount), 
        'DONATION'
      );
      
      if (response.success) {
        setVatCalculation(response.data);
      }
    } catch (error) {
      console.error('VAT calculation error:', error);
    }
  };

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('sw-TZ', {
      style: 'currency',
      currency: 'TZS',
      minimumFractionDigits: 0,
    }).format(value);
  };

  const calculateProgress = () => {
    if (!campaign) return 0;
    return Math.min((campaign.currentAmount / campaign.targetAmount) * 100, 100);
  };

  const getDaysRemaining = () => {
    if (!campaign || !campaign.endDate) return null;
    const today = new Date();
    const endDate = new Date(campaign.endDate);
    const diffTime = endDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  };

  const validateDonation = () => {
    if (!amount || parseFloat(amount) < 1000) {
      Alert.alert('Hitilafu', 'Kiwango cha chini ni 1,000 TZS');
      return false;
    }
    
    if (!user) {
      Alert.alert('Ingia Kwanza', 'Unahitaji kuingia ili kutoa mchango');
      return false;
    }
    
    return true;
  };

  const handleDonate = () => {
    if (!validateDonation()) return;
    setShowConfirmModal(true);
  };

  const processDonation = async () => {
    try {
      setDonating(true);
      
      const response = await donationsAPI.makeDonation({
        campaignId: campaign.id,
        amount: parseFloat(amount),
        message: message,
        isAnonymous: isAnonymous
      });

      if (response.success) {
        Alert.alert(
          'Asante!',
          `Mchango wako wa ${formatCurrency(parseFloat(amount))} umepokewa!\n\nKampeni imepata: ${formatCurrency(response.data.newTotal)}`,
          [
            {
              text: 'Sawa',
              onPress: () => {
                setShowConfirmModal(false);
                navigation.goBack();
              }
            }
          ]
        );
      } else {
        Alert.alert('Hitilafu', response.message || 'Imeshindwa kutoa mchango');
      }
    } catch (error) {
      console.error('Donation error:', error);
      Alert.alert('Hitilafu', error.message || 'Hitilafu ya mtandao');
    } finally {
      setDonating(false);
    }
  };

  const quickAmounts = [1000, 5000, 10000, 25000, 50000, 100000];

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Inapakia...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!campaign) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text>Kampeni haipatikani</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Toa Mchango</Text>
        <TouchableOpacity style={styles.shareButton}>
          <Icon name="share" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {/* Campaign Info */}
        <View style={styles.campaignCard}>
          <Image
            source={{ uri: campaign.imageUrl || 'https://via.placeholder.com/400x200' }}
            style={styles.campaignImage}
            resizeMode="cover"
          />
          
          <View style={styles.campaignInfo}>
            <Text style={styles.campaignTitle}>{campaign.title}</Text>
            <Text style={styles.campaignOrganizer}>na {campaign.organizerName}</Text>
            
            <View style={styles.progressSection}>
              <View style={styles.progressBar}>
                <View 
                  style={[
                    styles.progressFill,
                    { width: `${calculateProgress()}%` }
                  ]} 
                />
              </View>
              
              <View style={styles.progressInfo}>
                <Text style={styles.currentAmount}>
                  {formatCurrency(campaign.currentAmount)}
                </Text>
                <Text style={styles.targetAmount}>
                  Lengo: {formatCurrency(campaign.targetAmount)}
                </Text>
              </View>
              
              <View style={styles.campaignStats}>
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>{campaign.donorsCount}</Text>
                  <Text style={styles.statLabel}>Wachanga</Text>
                </View>
                
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>{getDaysRemaining()}</Text>
                  <Text style={styles.statLabel}>Siku Zilizobaki</Text>
                </View>
                
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>{Math.round(calculateProgress())}%</Text>
                  <Text style={styles.statLabel}>Imekamilika</Text>
                </View>
              </View>
            </View>
          </View>
        </View>

        {/* Donation Form */}
        <View style={styles.donationForm}>
          <Text style={styles.sectionTitle}>Kiasi cha Mchango</Text>
          
          {/* Quick Amount Buttons */}
          <View style={styles.quickAmounts}>
            {quickAmounts.map((quickAmount) => (
              <TouchableOpacity
                key={quickAmount}
                style={[
                  styles.quickAmountButton,
                  amount === quickAmount.toString() && styles.selectedQuickAmount
                ]}
                onPress={() => setAmount(quickAmount.toString())}
              >
                <Text style={[
                  styles.quickAmountText,
                  amount === quickAmount.toString() && styles.selectedQuickAmountText
                ]}>
                  {formatCurrency(quickAmount)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* Custom Amount Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Au ingiza kiasi kingine</Text>
            <View style={styles.inputWrapper}>
              <Icon name="money" size={20} color={colors.gray} />
              <TextInput
                style={styles.input}
                placeholder="10,000"
                value={amount}
                onChangeText={setAmount}
                keyboardType="numeric"
              />
              <Text style={styles.currency}>TZS</Text>
            </View>
            <Text style={styles.helperText}>Kiwango cha chini: 1,000 TZS</Text>
          </View>

          {/* VAT Calculation */}
          {vatCalculation && (
            <View style={styles.vatContainer}>
              <Text style={styles.vatTitle}>Muhtasari wa Mchango</Text>
              
              <View style={styles.vatRow}>
                <Text style={styles.vatLabel}>Kiasi ulichoingiza:</Text>
                <Text style={styles.vatValue}>
                  {formatCurrency(vatCalculation.grossAmount)}
                </Text>
              </View>
              
              <View style={styles.vatRow}>
                <Text style={styles.vatLabel}>VAT ({vatCalculation.vatRate}%):</Text>
                <Text style={[styles.vatValue, { color: colors.error }]}>
                  -{formatCurrency(vatCalculation.vatAmount)}
                </Text>
              </View>
              
              <View style={styles.divider} />
              
              <View style={styles.vatRow}>
                <Text style={styles.vatLabelTotal}>Kampeni itapokea:</Text>
                <Text style={styles.vatValueTotal}>
                  {formatCurrency(vatCalculation.netAmount)}
                </Text>
              </View>
            </View>
          )}

          {/* Message Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Ujumbe (si lazima)</Text>
            <TextInput
              style={styles.messageInput}
              placeholder="Andika ujumbe wa msaada..."
              value={message}
              onChangeText={setMessage}
              multiline
              numberOfLines={3}
            />
          </View>

          {/* Anonymous Option */}
          <TouchableOpacity
            style={styles.anonymousOption}
            onPress={() => setIsAnonymous(!isAnonymous)}
          >
            <Icon 
              name={isAnonymous ? "check-box" : "check-box-outline-blank"} 
              size={24} 
              color={colors.primary} 
            />
            <Text style={styles.anonymousText}>
              Toa mchango bila kutaja jina
            </Text>
          </TouchableOpacity>

          {/* Donate Button */}
          <TouchableOpacity
            style={[
              styles.donateButton,
              (!amount || parseFloat(amount) < 1000) && styles.donateButtonDisabled
            ]}
            onPress={handleDonate}
            disabled={!amount || parseFloat(amount) < 1000}
          >
            <Icon name="favorite" size={20} color={colors.white} />
            <Text style={styles.donateButtonText}>
              Toa Mchango - {amount ? formatCurrency(parseFloat(amount)) : '0'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Campaign Description */}
        <View style={styles.descriptionSection}>
          <Text style={styles.sectionTitle}>Maelezo ya Kampeni</Text>
          <Text style={styles.description}>{campaign.description}</Text>
        </View>
      </ScrollView>

      {/* Confirmation Modal */}
      <Modal
        visible={showConfirmModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowConfirmModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Thibitisha Mchango</Text>
            
            <View style={styles.confirmationDetails}>
              <Text style={styles.confirmationText}>
                Unataka kutoa mchango wa {formatCurrency(parseFloat(amount))} kwa kampeni ya "{campaign.title}"?
              </Text>
              
              {vatCalculation && (
                <View style={styles.confirmationBreakdown}>
                  <Text style={styles.breakdownText}>
                    Kampeni itapokea: {formatCurrency(vatCalculation.netAmount)}
                  </Text>
                  <Text style={styles.breakdownText}>
                    VAT: {formatCurrency(vatCalculation.vatAmount)}
                  </Text>
                </View>
              )}
              
              {message && (
                <Text style={styles.confirmationMessage}>
                  Ujumbe: "{message}"
                </Text>
              )}
              
              {isAnonymous && (
                <Text style={styles.confirmationAnonymous}>
                  ✓ Mchango wa kisiri
                </Text>
              )}
            </View>
            
            <View style={styles.modalActions}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setShowConfirmModal(false)}
              >
                <Text style={styles.cancelButtonText}>Ghairi</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.confirmButton}
                onPress={processDonation}
                disabled={donating}
              >
                <Text style={styles.confirmButtonText}>
                  {donating ? 'Inatuma...' : 'Thibitisha'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
  },
  shareButton: {
    padding: spacing.sm,
  },
  content: {
    flex: 1,
  },
  campaignCard: {
    backgroundColor: colors.white,
    marginBottom: spacing.sm,
  },
  campaignImage: {
    width: '100%',
    height: 200,
  },
  campaignInfo: {
    padding: spacing.lg,
  },
  campaignTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  campaignOrganizer: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
    marginBottom: spacing.lg,
  },
  progressSection: {
    marginBottom: spacing.md,
  },
  progressBar: {
    height: 8,
    backgroundColor: colors.border,
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: spacing.md,
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.success,
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  currentAmount: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.success,
  },
  targetAmount: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
  campaignStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.bold,
    color: colors.primary,
  },
  statLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
  donationForm: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    marginBottom: spacing.sm,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.md,
  },
  quickAmounts: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
    marginBottom: spacing.lg,
  },
  quickAmountButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: colors.border,
    backgroundColor: colors.surface,
  },
  selectedQuickAmount: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  quickAmountText: {
    fontSize: typography.fontSize.sm,
    color: colors.text,
  },
  selectedQuickAmountText: {
    color: colors.white,
  },
  inputContainer: {
    marginBottom: spacing.lg,
  },
  inputLabel: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingHorizontal: spacing.md,
    backgroundColor: colors.surface,
  },
  input: {
    flex: 1,
    paddingVertical: spacing.md,
    paddingLeft: spacing.sm,
    fontSize: typography.fontSize.md,
    color: colors.text,
  },
  currency: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
    fontWeight: typography.fontWeight.medium,
  },
  helperText: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  vatContainer: {
    backgroundColor: colors.surface,
    borderRadius: 8,
    padding: spacing.md,
    marginBottom: spacing.lg,
  },
  vatTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  vatRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  vatLabel: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
  vatValue: {
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: colors.text,
  },
  vatLabelTotal: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.text,
  },
  vatValueTotal: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.bold,
    color: colors.success,
  },
  divider: {
    height: 1,
    backgroundColor: colors.border,
    marginVertical: spacing.sm,
  },
  messageInput: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    padding: spacing.md,
    fontSize: typography.fontSize.md,
    color: colors.text,
    textAlignVertical: 'top',
    backgroundColor: colors.surface,
  },
  anonymousOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  anonymousText: {
    fontSize: typography.fontSize.md,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  donateButton: {
    backgroundColor: colors.primary,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.lg,
    borderRadius: 8,
  },
  donateButtonDisabled: {
    backgroundColor: colors.gray,
  },
  donateButtonText: {
    fontSize: typography.fontSize.lg,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.white,
    marginLeft: spacing.sm,
  },
  descriptionSection: {
    backgroundColor: colors.white,
    padding: spacing.lg,
  },
  description: {
    fontSize: typography.fontSize.md,
    color: colors.text,
    lineHeight: 24,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.lg,
    width: '90%',
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  confirmationDetails: {
    marginBottom: spacing.lg,
  },
  confirmationText: {
    fontSize: typography.fontSize.md,
    color: colors.text,
    textAlign: 'center',
    marginBottom: spacing.md,
  },
  confirmationBreakdown: {
    backgroundColor: colors.surface,
    borderRadius: 8,
    padding: spacing.md,
    marginBottom: spacing.md,
  },
  breakdownText: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  confirmationMessage: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    fontStyle: 'italic',
    marginBottom: spacing.sm,
  },
  confirmationAnonymous: {
    fontSize: typography.fontSize.sm,
    color: colors.info,
  },
  modalActions: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: spacing.md,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: typography.fontSize.md,
    color: colors.textSecondary,
  },
  confirmButton: {
    flex: 1,
    paddingVertical: spacing.md,
    borderRadius: 8,
    backgroundColor: colors.primary,
    alignItems: 'center',
  },
  confirmButtonText: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.white,
  },
});
