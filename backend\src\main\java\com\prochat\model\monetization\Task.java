package com.prochat.model.monetization;

import com.prochat.model.User;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "tasks")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Task {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "title", nullable = false)
    private String title;
    
    @Column(name = "description", nullable = false)
    private String description;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "task_type", nullable = false)
    private TaskType taskType;
    
    @Column(name = "target_count", nullable = false)
    private Integer targetCount;
    
    @Column(name = "reward_amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal rewardAmount;
    
    @Column(name = "currency", nullable = false)
    private String currency = "TZS";
    
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;
    
    @Column(name = "is_daily", nullable = false)
    private Boolean isDaily = false;
    
    @Column(name = "is_weekly", nullable = false)
    private Boolean isWeekly = false;
    
    @Column(name = "is_monthly", nullable = false)
    private Boolean isMonthly = false;
    
    @Column(name = "start_date")
    private LocalDateTime startDate;
    
    @Column(name = "end_date")
    private LocalDateTime endDate;
    
    @Column(name = "max_completions")
    private Integer maxCompletions; // Mara ngapi task inaweza kukamilishwa
    
    @Column(name = "minimum_level")
    private Integer minimumLevel = 1;
    
    @Column(name = "icon_url")
    private String iconUrl;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @Column(name = "created_by")
    private Long createdBy;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    public enum TaskType {
        LIKE_POSTS("Like Posts", "Penda machapisho"),
        COMMENT_POSTS("Comment Posts", "Andika maoni"),
        SHARE_POSTS("Share Posts", "Shiriki machapisho"),
        INVITE_FRIENDS("Invite Friends", "Alika marafiki"),
        COMPLETE_PROFILE("Complete Profile", "Kamilisha wasifu"),
        VERIFY_EMAIL("Verify Email", "Thibitisha barua pepe"),
        VERIFY_PHONE("Verify Phone", "Thibitisha namba ya simu"),
        FOLLOW_USERS("Follow Users", "Fuata watumiaji"),
        CREATE_POSTS("Create Posts", "Unda machapisho"),
        WATCH_VIDEOS("Watch Videos", "Tazama video"),
        ATTEND_EVENTS("Attend Events", "Hudhuria matukio"),
        BUY_TICKETS("Buy Tickets", "Nunua tiketi"),
        USE_PROPAY("Use ProPay", "Tumia ProPay"),
        SEND_GIFTS("Send Gifts", "Tuma zawadi"),
        DAILY_LOGIN("Daily Login", "Ingia kila siku"),
        WEEKLY_ACTIVE("Weekly Active", "Kuwa hai kila wiki"),
        MONTHLY_CHALLENGE("Monthly Challenge", "Changamoto ya mwezi");
        
        private final String displayName;
        private final String swahiliName;
        
        TaskType(String displayName, String swahiliName) {
            this.displayName = displayName;
            this.swahiliName = swahiliName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public String getSwahiliName() {
            return swahiliName;
        }
    }
}

@Entity
@Table(name = "user_task_completions")
@Data
@NoArgsConstructor
@AllArgsConstructor
class UserTaskCompletion {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "task_id", nullable = false)
    private Task task;
    
    @Column(name = "current_count", nullable = false)
    private Integer currentCount = 0;
    
    @Column(name = "target_count", nullable = false)
    private Integer targetCount;
    
    @Column(name = "is_completed", nullable = false)
    private Boolean isCompleted = false;
    
    @Column(name = "completed_at")
    private LocalDateTime completedAt;
    
    @Column(name = "reward_claimed", nullable = false)
    private Boolean rewardClaimed = false;
    
    @Column(name = "reward_claimed_at")
    private LocalDateTime rewardClaimedAt;
    
    @Column(name = "reward_amount", precision = 10, scale = 2)
    private BigDecimal rewardAmount;
    
    @Column(name = "started_at", nullable = false)
    private LocalDateTime startedAt;
    
    @Column(name = "expires_at")
    private LocalDateTime expiresAt;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        startedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
        if (currentCount >= targetCount && !isCompleted) {
            isCompleted = true;
            completedAt = LocalDateTime.now();
        }
    }
}
