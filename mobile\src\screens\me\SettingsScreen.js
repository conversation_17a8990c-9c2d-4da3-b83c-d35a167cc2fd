import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Switch,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors, typography, spacing } from '../../theme/theme';
import { settingsAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';

export default function SettingsScreen({ navigation }) {
  const { user, logout } = useAuth();
  const { isDarkMode, toggleTheme } = useTheme();
  const [settings, setSettings] = useState({
    notifications: {
      push: true,
      email: true,
      sms: false,
      marketing: false,
    },
    privacy: {
      profileVisibility: 'PUBLIC',
      showOnlineStatus: true,
      allowMessages: true,
      allowCalls: true,
    },
    security: {
      twoFactorAuth: false,
      loginAlerts: true,
      biometricAuth: false,
    },
    preferences: {
      language: 'sw',
      autoPlayVideos: true,
      dataUsage: 'NORMAL',
      soundEffects: true,
    }
  });

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const response = await settingsAPI.getUserSettings();
      if (response.success) {
        setSettings(response.data);
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  const updateSetting = async (category, key, value) => {
    try {
      const newSettings = {
        ...settings,
        [category]: {
          ...settings[category],
          [key]: value
        }
      };
      
      setSettings(newSettings);
      
      const response = await settingsAPI.updateSettings({
        [category]: {
          [key]: value
        }
      });
      
      if (!response.success) {
        // Revert on failure
        setSettings(settings);
        Alert.alert('Hitilafu', 'Imeshindwa kusasisha mipangilio');
      }
    } catch (error) {
      console.error('Error updating setting:', error);
      setSettings(settings);
      Alert.alert('Hitilafu', 'Imeshindwa kusasisha mipangilio');
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Toka',
      'Una uhakika unataka kutoka?',
      [
        { text: 'Ghairi', style: 'cancel' },
        {
          text: 'Toka',
          style: 'destructive',
          onPress: async () => {
            const result = await logout();
            if (result.success) {
              navigation.reset({
                index: 0,
                routes: [{ name: 'Auth' }],
              });
            }
          }
        }
      ]
    );
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      'Futa Akaunti',
      'ONYO: Hatua hii haiwezi kubatilishwa. Utapoteza data yako yote.',
      [
        { text: 'Ghairi', style: 'cancel' },
        {
          text: 'Futa',
          style: 'destructive',
          onPress: () => navigation.navigate('DeleteAccount')
        }
      ]
    );
  };

  const renderSettingItem = (title, subtitle, value, onValueChange, type = 'switch') => (
    <View style={styles.settingItem}>
      <View style={styles.settingInfo}>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
      </View>
      {type === 'switch' ? (
        <Switch
          value={value}
          onValueChange={onValueChange}
          trackColor={{ false: colors.gray, true: colors.primary }}
          thumbColor={value ? colors.white : colors.white}
        />
      ) : (
        <TouchableOpacity onPress={onValueChange}>
          <View style={styles.settingValue}>
            <Text style={styles.settingValueText}>{value}</Text>
            <Icon name="chevron-right" size={20} color={colors.gray} />
          </View>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderSection = (title, children) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <View style={styles.sectionContent}>
        {children}
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Mipangilio</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content}>
        {/* Account Section */}
        {renderSection('Akaunti', [
          <TouchableOpacity key="profile" style={styles.menuItem} onPress={() => navigation.navigate('Profile')}>
            <Icon name="person" size={24} color={colors.primary} />
            <Text style={styles.menuText}>Wasifu Wangu</Text>
            <Icon name="chevron-right" size={24} color={colors.gray} />
          </TouchableOpacity>,
          
          <TouchableOpacity key="security" style={styles.menuItem} onPress={() => navigation.navigate('Security')}>
            <Icon name="security" size={24} color={colors.primary} />
            <Text style={styles.menuText}>Usalama na Faragha</Text>
            <Icon name="chevron-right" size={24} color={colors.gray} />
          </TouchableOpacity>,
          
          <TouchableOpacity key="wallet" style={styles.menuItem} onPress={() => navigation.navigate('Wallet')}>
            <Icon name="account-balance-wallet" size={24} color={colors.primary} />
            <Text style={styles.menuText}>ProPay Wallet</Text>
            <Icon name="chevron-right" size={24} color={colors.gray} />
          </TouchableOpacity>
        ])}

        {/* Notifications */}
        {renderSection('Arifa', [
          renderSettingItem(
            'Arifa za Simu',
            'Pokea arifa kwenye simu yako',
            settings.notifications.push,
            (value) => updateSetting('notifications', 'push', value)
          ),
          renderSettingItem(
            'Arifa za Barua Pepe',
            'Pokea arifa kwenye barua pepe',
            settings.notifications.email,
            (value) => updateSetting('notifications', 'email', value)
          ),
          renderSettingItem(
            'Arifa za SMS',
            'Pokea arifa kwa njia ya SMS',
            settings.notifications.sms,
            (value) => updateSetting('notifications', 'sms', value)
          ),
          renderSettingItem(
            'Arifa za Uuzaji',
            'Pokea arifa za bidhaa na huduma',
            settings.notifications.marketing,
            (value) => updateSetting('notifications', 'marketing', value)
          )
        ])}

        {/* Privacy */}
        {renderSection('Faragha', [
          renderSettingItem(
            'Mwonekano wa Wasifu',
            'Nani anaweza kuona wasifu wako',
            settings.privacy.profileVisibility === 'PUBLIC' ? 'Umma' : 
            settings.privacy.profileVisibility === 'FRIENDS' ? 'Marafiki' : 'Binafsi',
            () => navigation.navigate('PrivacySettings'),
            'navigation'
          ),
          renderSettingItem(
            'Onyesha Hali ya Mtandaoni',
            'Wengine wataona ukiwa mtandaoni',
            settings.privacy.showOnlineStatus,
            (value) => updateSetting('privacy', 'showOnlineStatus', value)
          ),
          renderSettingItem(
            'Ruhusu Ujumbe',
            'Watu wanaweza kukutumia ujumbe',
            settings.privacy.allowMessages,
            (value) => updateSetting('privacy', 'allowMessages', value)
          ),
          renderSettingItem(
            'Ruhusu Simu',
            'Watu wanaweza kukupigia simu',
            settings.privacy.allowCalls,
            (value) => updateSetting('privacy', 'allowCalls', value)
          )
        ])}

        {/* Preferences */}
        {renderSection('Mapendeleo', [
          renderSettingItem(
            'Mandhari ya Giza',
            'Tumia mandhari ya giza',
            isDarkMode,
            toggleTheme
          ),
          renderSettingItem(
            'Lugha',
            'Chagua lugha ya programu',
            settings.preferences.language === 'sw' ? 'Kiswahili' : 'English',
            () => navigation.navigate('LanguageSettings'),
            'navigation'
          ),
          renderSettingItem(
            'Cheza Video Otomatiki',
            'Video zicheze moja kwa moja',
            settings.preferences.autoPlayVideos,
            (value) => updateSetting('preferences', 'autoPlayVideos', value)
          ),
          renderSettingItem(
            'Sauti za Programu',
            'Cheza sauti za vitendo',
            settings.preferences.soundEffects,
            (value) => updateSetting('preferences', 'soundEffects', value)
          )
        ])}

        {/* Support */}
        {renderSection('Msaada', [
          <TouchableOpacity key="help" style={styles.menuItem} onPress={() => navigation.navigate('HelpSupport')}>
            <Icon name="help" size={24} color={colors.primary} />
            <Text style={styles.menuText}>Msaada na Maswali</Text>
            <Icon name="chevron-right" size={24} color={colors.gray} />
          </TouchableOpacity>,
          
          <TouchableOpacity key="contact" style={styles.menuItem}>
            <Icon name="contact-support" size={24} color={colors.primary} />
            <Text style={styles.menuText}>Wasiliana Nasi</Text>
            <Icon name="chevron-right" size={24} color={colors.gray} />
          </TouchableOpacity>,
          
          <TouchableOpacity key="feedback" style={styles.menuItem}>
            <Icon name="feedback" size={24} color={colors.primary} />
            <Text style={styles.menuText}>Toa Maoni</Text>
            <Icon name="chevron-right" size={24} color={colors.gray} />
          </TouchableOpacity>
        ])}

        {/* About */}
        {renderSection('Kuhusu', [
          <TouchableOpacity key="terms" style={styles.menuItem}>
            <Icon name="description" size={24} color={colors.primary} />
            <Text style={styles.menuText}>Masharti ya Matumizi</Text>
            <Icon name="chevron-right" size={24} color={colors.gray} />
          </TouchableOpacity>,
          
          <TouchableOpacity key="privacy-policy" style={styles.menuItem}>
            <Icon name="privacy-tip" size={24} color={colors.primary} />
            <Text style={styles.menuText}>Sera ya Faragha</Text>
            <Icon name="chevron-right" size={24} color={colors.gray} />
          </TouchableOpacity>,
          
          <View key="version" style={styles.menuItem}>
            <Icon name="info" size={24} color={colors.primary} />
            <Text style={styles.menuText}>Toleo la Programu</Text>
            <Text style={styles.versionText}>1.0.0</Text>
          </View>
        ])}

        {/* Danger Zone */}
        <View style={styles.dangerZone}>
          <Text style={styles.dangerTitle}>Eneo la Hatari</Text>
          
          <TouchableOpacity style={styles.dangerItem} onPress={handleLogout}>
            <Icon name="logout" size={24} color={colors.error} />
            <Text style={[styles.menuText, { color: colors.error }]}>Toka</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.dangerItem} onPress={handleDeleteAccount}>
            <Icon name="delete-forever" size={24} color={colors.error} />
            <Text style={[styles.menuText, { color: colors.error }]}>Futa Akaunti</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    fontSize: typography.fontSize.xl,
    fontWeight: typography.fontWeight.bold,
    color: colors.text,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  section: {
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.textSecondary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    backgroundColor: colors.surface,
  },
  sectionContent: {
    backgroundColor: colors.white,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  menuText: {
    flex: 1,
    fontSize: typography.fontSize.md,
    color: colors.text,
    marginLeft: spacing.md,
  },
  versionText: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  settingInfo: {
    flex: 1,
  },
  settingTitle: {
    fontSize: typography.fontSize.md,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  settingSubtitle: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
  },
  settingValue: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingValueText: {
    fontSize: typography.fontSize.sm,
    color: colors.textSecondary,
    marginRight: spacing.sm,
  },
  dangerZone: {
    marginTop: spacing.xl,
    marginBottom: spacing.xl,
  },
  dangerTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.semiBold,
    color: colors.error,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    backgroundColor: colors.error + '10',
  },
  dangerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
});
